# 筛选表单组件

## FormSchema 定义

`FormSchema` 用于定义表单的结构和行为。它是一个映射，其中每个键代表表单中的一个字段，每个值描述了该字段的属性和行为。

### 类型定义

```typescript
type Option = { label: string; value: string };

interface FormField {
  type: string;
  label: string;
  component: string;
  value: string | number;
  options?: Option[];
  listeners?: Record<string, boolean>;
}

interface FormSchema {
  [key: string]: FormField;
}
```

### 示例

```typescript
const formSchema: FormSchema = {
  order_key: {
    type: 'select',
    label: '排序',
    component: 'n-select',
    value: '',
    options: [
      { label: 'default_sorting', value: '' },
      { label: '价格↑', value: 'sell_min_price,1' },
      { label: '价格↓', value: 'sell_min_price,2' },
      { label: '在售数量↑', value: 'sell_num,1' },
      { label: '在售数量↓', value: 'sell_num,2' },
    ],
  },
  search: {
    type: 'string',
    label: 'search',
    component: 'n-input-search', //封装或ui组件
    value: '',
    listeners: {
      'custom:click': true,
    },
  },
};

const createFormState = () =>
  reactive(
    Object.keys(formSchema).reduce((state: Record<string, any>, key) => {
      const formKey = key as keyof typeof formSchema;
      state[formKey] = formSchema[formKey].value;
      return state;
    }, {}),
  );

export { formSchema, createFormState };
export type { FormSchema };
```

## FilterForm 组件

`FilterForm` 组件用于创建和管理整个表单。它接收 `formSchema` 和 `formState` 作为属性，并提供一个插槽来插入具体的表单项。

### 使用示例

```vue
<template>
  <FilterForm
    ref="form"
    class="flex justify-between w-full"
    :form-schema="formSchema"
    :form-state="formState"
  >
    <div class="w-full flex justify-end">
      <FilterComponetsFormItem
        class="mr-12 w-32"
        :field="formSchema.order_key"
        field-name="order_key"
        :model-value="formState.order_key"
        @update:model-value="(value) => handleUpdate('search', value)"
      />
      <FilterComponetsFormItem
        :field="formSchema.search"
        field-name="search"
        :model-value="formState.search"
        @update:model-value="(value) => handleUpdate('search', value)"
        @handle:event="handleSearch"
      />
    </div>
  </FilterForm>
</template>

<script lang="ts" setup>
import FilterForm from './path/to/FilterForm.vue';
import FilterComponetsFormItem from './path/to/FilterComponetsFormItem.vue';
import { formSchema, createFormState } from './path/to/formSchema';

const formState = createFormState();

const handleUpdate = (fieldName, value) => {
  formState[fieldName] = value;
};

const handleSearch = (event) => {
  // 处理搜索事件
};
</script>
```
