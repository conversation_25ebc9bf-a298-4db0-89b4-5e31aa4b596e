// https://nuxt.com/docs/api/configuration/nuxt-config
import * as path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import topLevelAwait from 'vite-plugin-top-level-await';

const isProduction =
  process.env.APP_ENV === 'production' && process.env.NODE_ENV === 'production';
const languages = process.env.VITE_PUBLIC_LANGUAGE;
const locales = languages?.split(',') || [];
export default defineNuxtConfig({
  devtools: { enabled: process.env.NODE_ENV === 'development' },

  imports: {
    dirs: ['./api/openapi/', './composables/dialog'],
  },

  srcDir: 'src/',

  app: {
    head: {
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
      script: [],
    },
    // cdnURL: isProduction ? import.meta.env.VITE_PUBLIC_CDN_URL : '',
  },

  hooks: {
    'nitro:build:public-assets': (_nitro) => {
      //   const targetDir = path.join(
      //     nitro.options.output.publicDir,
      //     '.well-known',
      //   );
      //   const sourceFile = `./.well-known/${process.env.APP_ENV}`;
      //   cpSync(sourceFile, targetDir, { recursive: true });
    },
  },

  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxt/image',
    'nuxt-svgo',
    '@nuxtjs/i18n',
  ],

  image: {
    // 配置选项
  },

  css: [
    '~/assets/css/tailwind.css',
    '~/assets/css/main.css',
    '~/assets/css/base.scss',
    '~/assets/css/friendPlayerLevel.css',
    '@icon-park/vue-next/styles/index.css',
  ],

  runtimeConfig: {
    app: {
      buildId: '6fc9bff4-0e40-4e61-8167-19c6af90719a',
    },
    public: {
      APP_ENV: process.env.APP_ENV,
      PROD: process.env.NODE_ENV === 'production',
      BASE_URL: process.env.NUXT_PUBLIC_BASE_URL,
      LIVE_BASE_URL: process.env.NUXT_PUBLIC_LIVE_BASE_URL,
      LANGUAGE_URL: process.env.NUXT_PUBLIC_LANGUAGE_URL,
      APP_TITLE: '',
      APP_DESCRIPTION: '',
      APP_KEY_WORDS: '',
      TURNSTILE_SITE_KEY: process.env.NUXT_PUBLIC_TURNSTILE_SITE_KEY,
      ENABLE_TURNSTILE: process.env.NUXT_PUBLIC_ENABLE_TURNSTILE,
      CHATWOOT_TOKEN: process.env.NUXT_PUBLIC_CHATWOOT_TOKEN,
      CHATWOOT_BASE_URL: process.env.NUXT_PUBLIC_CHATWOOT_BASE_URL,
      DOMAIN_CONFIG_DOMAIN: process.env.NUXT_PUBLIC_DOMAIN_CONFIG_DOMAIN,
    },
  },

  components: {
    global: true,
    dirs: ['~/components'],
  },

  postcss: {
    plugins: {
      autoprefixer: {
        grid: true,
        flexbox: true,
      },
      'postcss-preset-env': {
        stage: 2,
        browsers: ['last 2 versions', 'Safari >= 9'],
        features: {
          'nesting-rules': {
            noIsPseudoSelector: false,
          },
        },
      },
    },
  },

  nitro: {
    esbuild: {
      options: {
        // target: 'esnext',
        drop: isProduction ? ['console'] : [],
      },
    },
    devProxy: {
      '/api': {
        target: import.meta.env.VITE_PUBLIC_BASE_URL, // 这里是接口地址
        changeOrigin: true,
      },
      '/mock': {
        target: import.meta.env.VITE_PUBLIC_MOCK_URL || '/', // mock地址
        changeOrigin: true,
        // ignorePath:true,
        // toProxy:true
      },
    },
    // 该配置用于服务端请求转发
    // routeRules: {
    //   '/api/**': {
    //     proxy: 'http://localhost:3001/**'
    //   }
    // }
  },

  plugins: ['~/plugins/global-methods'],

  vite: {
    resolve: {
      alias: {
        '@sounds': path.resolve(__dirname, './src/assets/sounds'),
      },
    },
    esbuild: {
      drop: isProduction ? ['console'] : [],
    },
    build: {
      // target: ['es2022', 'edge89', 'firefox89', 'chrome89', 'safari15']
      rollupOptions: isProduction
        ? {
            output: {
              chunkFileNames: '_nuxt/[name].js',
              assetFileNames: '_nuxt/[name][extname]',
              entryFileNames: '_nuxt/[name].js',
            },
          }
        : {},
    },
    // 添加对音频文件的处理
    assetsInclude: ['**/*.mp3'],
    optimizeDeps: {
      include:
        process.env.NODE_ENV === 'development'
          ? ['naive-ui', 'vueuc', 'date-fns-tz/esm/formatInTimeZone', 'howler']
          : [],
    },
    plugins: [
      AutoImport({
        imports: [
          {
            'naive-ui': [
              // 'useDialog',
              'useMessage',
              'useNotification',
              'useLoadingBar',
            ],
          },
        ],
      }),
      Components({
        resolvers: [NaiveUiResolver()],
      }),
      topLevelAwait({
        // The export name of top-level await promise for each chunk module
        promiseExportName: '__tla',
        // The function to generate import names of top-level await promise in each chunk module
        promiseImportName: (i: any) => `__tla_${i}`,
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
  },

  build: {
    transpile:
      process.env.NODE_ENV === 'production'
        ? [
            'naive-ui',
            'vueuc',
            '@css-render/vue3-ssr',
            'juggle/resize-observer',
          ]
        : ['@juggle/resize-observer'],
  },

  // routeRules: {
  //   '/': { redirect: '/coinflip' },
  // },
  svgo: {
    svgoConfig: {
      multipass: true,
      plugins: [
        {
          name: 'preset-default',
          params: {
            overrides: {
              // customize default plugin options
              inlineStyles: {
                onlyMatchedOnce: false,
              },

              // or disable plugins
              removeDoctype: false,
              removeViewBox: false,
              // cleanupIds: {
              //   minify: true,         // 压缩 ID，生成短 ID 名称
              //   remove: true,         // 删除未使用的 ID
              //   prefix: 'svg-',
              // }
            },
          },
        },
        {
          name: 'prefixIds',
          params: {
            prefix: (_node, info) => {
              return `${info.path}-${info.multipassCount}`; // 以文件名作为前缀和重复次数
            },
          },
        },
      ],
    },
  },

  routeRules: {
    '/steam': {
      cors: true,
    },
  },

  i18n: {
    locales,
    defaultLocale: 'en',
    strategy: 'no_prefix',
    vueI18n: './i18n.config.ts', // if you are using custom path, default
    detectBrowserLanguage: {
      useCookie: true, // 使用 Cookie 保存语言
      cookieKey: 'i18n_redirected', // Cookie 名称
      redirectOn: 'all', // 何时重定向，可以是 'root' 或 'all'
      alwaysRedirect: false, // 如果为 false，只有第一次访问时会重定向
      fallbackLocale: 'en', // 如果浏览器语言不可用时的默认语言
    },
  },

  compatibilityDate: '2025-05-23',
});
