/** @type {import('tailwindcss').Config} */
module.exports = {
  target: 'browserslist',
  darkMode: 'no-dark', // 移除darkMode的支持。若需要适配darkMode，将该行移除后进行适配。
  content: [
    './src/components/**/*.{js,vue,ts}',
    './src/layouts/**/*.vue',
    './src/pages/**/*.vue',
    './src/plugins/**/*.{js,ts}',
  ],
  corePlugins: {
    aspectRatio: true,
  },
  // plugins: [require('@tailwindcss/aspect-ratio')],
  theme: {
    extend: {
      screens: {
        sm: '640px',
        md: '768px',
        // lg: '992px',
        xl: '1376px',
      },
      spacing: {
        2.9: '0.71rem',
      },
      width: {
        '1/10': '10%',
      },
      padding: {
        '100/100': '100%',
      },
      height: {
        23: '5.71rem',
        406: '102rem',
      },
      text: {
        colors: {
          sub: 'rgb(var(--sub-text-color))',
        },
      },
      borderWidth: {
        1: '1px',
        3: '3px',
      },
      backgroundImage: {},
      filter: {
        none: 'none',
        blur: 'blur(10px)',
      },
      flexGrow: {
        2: '2',
      },
      colors: {
        primary: {
          50: '#fffaeb',
          100: '#fdefc8',
          200: '#fbdf8c',
          300: '#f9c850',
          400: '#f8b838',
          500: '#f1910f',
          600: '#d56c0a',
          700: '#b14a0c',
          800: '#903a10',
          900: '#763011',
          950: '#441704',
        },
        'surface-0': 'rgb(var(--surface-0))',
        'surface-50': 'rgb(var(--surface-50))',
        'surface-100': 'rgb(var(--surface-100))',
        'surface-200': 'rgb(var(--surface-200))',
        'surface-300': 'rgb(var(--surface-300))',
        'surface-400': 'rgb(var(--surface-400))',
        'surface-500': 'rgb(var(--surface-500))',
        'surface-600': 'rgb(var(--surface-600))',
        'surface-700': 'rgb(var(--surface-700))',
        'surface-800': 'rgb(var(--surface-800))',
        'surface-900': 'rgb(var(--surface-900))',
        'surface-950': 'rgb(var(--surface-950))',
        // 字体
        'theme-color': 'rgb(var(--theme-color))',
        'sub-text': 'rgb(var(--sub-text))',
        'black-text': 'rgb(var(--black-text))',
        'highlight-text': 'rgb(var(--highlight-text))',
        'primary-text': 'rgb(var(--primary-text))',
        // 背景
        'bg-black': 'rgb(var(--bg-black))',
        'bg-gray': 'rgb(var(--bg-gray))',
        'bg-light-gray': 'rgb(var(--bg-light-gray))',
        // 自定义颜色
        'green-1': 'rgb(var(--green-1))',
        'red-1': 'rgb(var(--red-1))',
        'light-1': 'rgb(var(--light-1))',
        'light-2': 'rgb(var(--light-2))',
        'light-3': 'rgb(var(--light-3))',
        'gray-1': 'rgb(var(--gray-1))',
        'gray-2': 'rgb(var(--gray-2))',
        'gray-3': 'rgb(var(--gray-3))',
        'dark-1': 'rgb(var(--dark-1))',
        'dark-2': 'rgb(var(--dark-2))',
        'dark-3': 'rgb(var(--dark-3))',
        'dark-4': 'rgb(var(--dark-4))',
        'dark-5': 'rgb(var(--dark-5))',
        'dark-6': 'rgb(var(--dark-6))',
        'dark-7': 'rgb(var(--dark-7))',
        'purple-1': 'rgb(var(--purple-1))',
        'purple-2': 'rgb(var(--purple-2))',
        warning: {
          50: '#fff1f1',
          100: '#ffe1e1',
          200: '#ffc7c7',
          300: '#ffa0a0',
          400: '#ff5151',
          500: '#f83b3b',
          600: '#e51d1d',
          700: '#c11414',
          800: '#a01414',
          900: '#841818',
          950: '#480707',
        },
      },
    },
  },
};
