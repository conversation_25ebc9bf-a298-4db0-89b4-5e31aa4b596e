#!/bin/bash
# export OUTPUT_DIR=".output-temp"

pnpm install

echo "========= Install csdata-frontend-pc Finished ========="

pnpm run build

echo "========= Build csdata-frontend-pc Finished ========="

if [ $? -ne 0 ]; then
  echo "Build failed!"
  exit 1
fi

if [ -d ".output-temp" ]; then
  mv .output-temp .output-backup
fi

mv .output .output-temp

# 检查 PM2 进程是否存在
pm2 describe fusion-frontpc > /dev/null
if [ $? -ne 0 ]; then
  echo "PM2 process ./ecosystem.config.cjs   not found, starting it..."
  pm2 start  ./ecosystem.config.cjs   --update-env

  if [ $? -ne 0 ]; then
    echo "Failed to start PM2 process ./ecosystem.config.cjs "
    rm -rf .output
    mv .output-backup .output-temp
    exit 1
  fi
else
  pm2 reload ./ecosystem.config.cjs   --update-env

  if [ $? -ne 0 ]; then
    rm -rf .output
    mv .output-backup .output-temp
    echo "PM2 reload failed, restored the backup."
    exit 1
  fi
fi

rm -rf .output-backup

echo "Deployment completed successfully!"