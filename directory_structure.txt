.
├── README.md
├── app.vue
├── commitlint.config.cjs
├── directory_structure.txt
├── nuxt.config.ts
├── openapi.json
├── openapitools.json
├── package.json
├── pnpm-lock.yaml

├── server
│   └── tsconfig.json
├── src
│   ├── api
│   │   ├── home.ts
│   │   ├── index.ts
│   │   ├── login.ts
│   │   ├── openapi
│   │   └── user.ts
│   ├── assets
│   │   └── css
│   │       ├── main.css
│   │       └── tailwind.css
│   ├── components
│   │   ├── AppBanner.vue
│   │   ├── AppHeader.vue
│   │   └── base
│   │       ├── ItemCard.vue
│   │       └── NavMenu.vue
│   ├── components.d.ts
│   ├── composables
│   │   ├── useApi.ts
│   │   └── useRequest.ts
│   ├── index.d.ts
│   ├── layouts
│   │   ├── csgo.vue
│   │   └── default.vue
│   ├── pages
│   │   ├── index.vue
│   │   ├── market
│   │   │   ├── csgo
│   │   │   │   └── index.vue
│   │   │   └── index.vue
│   │   └── shop
│   │       └── index.vue
│   ├── plugins
│   │   ├── directives.ts
│   │   ├── global-components.ts
│   │   └── global-methods.ts
│   ├── public
│   │   ├── favicon.ico
│   │   └── images
│   │       └── logo.png
│   ├── scripts
│   │   └── genApiModel.sh
│   ├── stores
│   │   ├── index.ts
│   │   └── modules
│   │       └── user.ts
│   └── utils
│       ├── _index.vue
│       └── request.ts
├── stylelint.config.cjs
├── tailwind.config.js
└── tsconfig.json

143 directories, 251 files
