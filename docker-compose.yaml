services:
  skin-bucks-frontend-pc:
    image: skin-bucks-frontend-pc:latest
    ports:
      - '9199:3000'
    environment:
      HOST: '0.0.0.0'
      PORT: '3000'
    networks:
      - traefik-public
      - lksk-service-network
    logging:
      driver: 'json-file'
      options:
        max-size: '100m'
        max-file: '10'
    deploy:
      mode: global
      placement:
        constraints:
          - node.labels.lksk-service == true
      resources:
        limits:
          cpus: '3'
          memory: 1024M
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 1s
        max_failure_ratio: 0.3
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: continue
        monitor: 3s
        max_failure_ratio: 0.5
        order: start-first
      restart_policy:
        condition: on-failure

networks:
  traefik-public:
    external: true
  lksk-service-network:
    external: true
    driver: overlay
    attachable: true
    name: lksk-service-network
