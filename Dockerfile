FROM node:18-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NPM_CONFIG_REGISTRY=https://registry.npmmirror.com
ENV COREPACK_NPM_REGISTRY=https://registry.npmmirror.com
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate
RUN npm config set registry $NPM_CONFIG_REGISTRY
COPY . /app
WORKDIR /app


FROM base AS prod-deps
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile

FROM base AS build
ARG APP_ENV
RUN npm config set registry https://registry.npmmirror.com
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN export NODE_OPTIONS="--max-old-space-size=3000" && \
    if [ "$APP_ENV" = "production" ]; then \
    pnpm run build:prod; \
    else \
    pnpm run build:test; \
    fi


FROM node:18-slim
WORKDIR /app

COPY --from=build /app/.output /app/.output

EXPOSE ${PORT:-3000}

CMD ["node", ".output/server/index.mjs"]