#!/bin/bash

# 退出时清理
trap 'echo "脚本被中断，退出..."; exit 1' INT TERM

# 显示帮助信息的函数
show_help() {
  echo "用法: $0 [选项]"
  echo "选项:"
  echo "  build    - 仅构建Docker镜像"
  echo "  start    - 仅启动容器（不构建镜像）"
  echo "  stop     - 仅停止运行中的容器"
  echo "  rm       - 停止容器并删除容器"
  echo "  rmi      - 停止容器并删除镜像"
  echo "  help     - 显示此帮助信息"
  echo "  无参数   - 执行完整流程：构建镜像并启动容器"
  exit 0
}


SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="$SCRIPT_DIR/.env"

# 加载环境变量
if [ -f "$ENV_FILE" ]; then
  echo "正在加载环境变量文件: $ENV_FILE"
  export $(grep -v '^#' "$ENV_FILE" | xargs)
else
  echo "警告: 环境变量文件不存在: $ENV_FILE"
fi

# 环境变量定义
APP_ENV="${APP_ENV:-production}" 

# Docker相关变量定义
NAME="skin-bucks-frontend-pc"
PUSH_TAG="$NAME-$(git rev-parse --short HEAD 2>/dev/null || echo 'latest')"
CONTAINER_NAME="skin-bucks-frontend-pc-pre"

# 参数处理
MODE="full"  # 默认模式：构建并启动
if [ $# -gt 0 ]; then
  case "$1" in
    build)
      MODE="build"
      ;;
    start)
      MODE="start"
      ;;
    stop)
      MODE="stop"
      ;;
    rm|remove)
      MODE="remove"
      ;;
    rmi|remove-image)
      MODE="remove-image"
      ;;
    help)
      show_help
      ;;
    *)
      echo "未知参数: $1"
      show_help
      ;;
  esac
fi

echo "========= 开始处理 lksk 前端 ($APP_ENV 环境) ========="

# 构建Docker镜像
if [ "$MODE" = "build" ] || [ "$MODE" = "full" ]; then
  echo "正在构建Docker镜像..."
  docker build \
    --build-arg APP_ENV="$APP_ENV" \
    -t "$NAME" \
    -t "$PUSH_TAG" \
    -f ./Dockerfile \
    . || { echo "构建失败！"; exit 1; }

  echo "镜像构建成功: $NAME"
fi

# 仅停止容器
if [ "$MODE" = "stop" ]; then
  echo "停止运行中的容器..."
  docker stop $CONTAINER_NAME || { echo "停止容器失败或容器不存在"; }
  echo "容器停止操作完成"
fi

# 删除容器
if [ "$MODE" = "remove" ]; then
  echo "停止并删除容器..."
  docker stop $CONTAINER_NAME || true
  docker rm $CONTAINER_NAME || true
  echo "容器删除操作完成"
fi

# 删除镜像
if [ "$MODE" = "remove-image" ]; then
  echo "停止并删除容器..."
  docker stop $CONTAINER_NAME || true
  docker rm $CONTAINER_NAME || true
  
  echo "删除相关镜像..."
  docker rmi $NAME || { echo "删除镜像 $NAME 失败或镜像不存在"; }
  docker rmi $PUSH_TAG || { echo "删除镜像 $PUSH_TAG 失败或镜像不存在"; }
  echo "镜像删除操作完成"
fi

# 启动容器部分
if [ "$MODE" = "start" ] || [ "$MODE" = "full" ]; then
  # 停止并删除旧容器
  echo "停止并删除旧容器..."
  docker stop $CONTAINER_NAME || true
  docker rm $CONTAINER_NAME || true

  # 启动新容器
  echo "启动新容器..."
  docker run -d \
    --name $CONTAINER_NAME \
    --env-file "$ENV_FILE" \
    -p ${SERVER_PORT:-9199}:${PORT:-3000} \
    -e HOST=0.0.0.0 \
    --restart=always \
    $NAME || { echo "容器启动失败！"; exit 1; }
fi

# 清理无用镜像
if [ "$MODE" = "build" ] || [ "$MODE" = "full" ] || [ "$MODE" = "remove-image" ]; then
  echo "清理无用镜像..."
  # 清理退出状态的容器
  EXITED_CONTAINERS=$(docker ps -a -f status=exited -q)
  if [ -n "$EXITED_CONTAINERS" ]; then
    docker rm $EXITED_CONTAINERS
  else
    echo "没有需要清理的退出容器"
  fi

  # 清理悬空镜像
  DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
  if [ -n "$DANGLING_IMAGES" ]; then
    docker rmi $DANGLING_IMAGES
  else
    echo "没有需要清理的悬空镜像"
  fi
fi

echo "========= lksk 前端处理完成 ========="