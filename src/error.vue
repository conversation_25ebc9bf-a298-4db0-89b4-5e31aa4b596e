<script setup lang="ts">
const error = useError();
if (
  import.meta.client &&
  window.location.pathname !== '/' &&
  ![404, 403, 500].includes(error?.value?.statusCode ?? 0)
) {
  navigateTo('/', {
    redirectCode: 301,
    external: true,
  });
}
</script>

<template>
  <Layout>
    <div class="m-auto">
      <template v-if="error?.statusCode === 404">
        <n-image width="508" src="/imgs/404.png" :preview-disabled="true" />
      </template>
      <template v-if="error?.statusCode === 403">
        <n-image width="508" src="/imgs/403.png" :preview-disabled="true" />
      </template>
      <template v-if="error?.statusCode === 500">
        <n-image width="508" src="/imgs/500.png" :preview-disabled="true" />
      </template>
    </div>
  </Layout>
</template>
