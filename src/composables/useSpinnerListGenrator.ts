import type { CaseSkinItemType } from '~/types/cases';

export default function useSpinnerListGenrator(opt: {
  caseContain: CaseSkinItemType[];
  spinnerLength?: number;
}) {
  const { caseContain, spinnerLength = 71 } = opt;
  const generatorItem = () => {
    const firstItem = caseContain[0];
    const lastItem = caseContain[caseContain.length - 1];
    const firstNumberSpaceTo = firstItem.number_space_to || 0;
    const lastNumberSpaceTo = lastItem.number_space_to || 0;
    const maxTo = Math.max(firstNumberSpaceTo, lastNumberSpaceTo);
    const random = Math.floor(Math.random() * maxTo) + 1;
    for (const item of caseContain) {
      // 计算每个产品的区间
      if (item.number_space_from && item.number_space_to) {
        if (
          random >= item.number_space_from &&
          random <= item.number_space_to
        ) {
          return item;
        }
      }
    }
    return {};
  };
  const spinnerList = Array.from(
    {
      length: spinnerLength,
    },
    generatorItem,
  );
  return {
    spinnerList,
  };
}
