/**
 * 获取域名
 */
export function useSiteName() {
  const getCurrentDomain = (): string => {
    const { DOMAIN_CONFIG_DOMAIN } = useRuntimeConfig().public;
    if (import.meta.server) {
      return DOMAIN_CONFIG_DOMAIN;
    }

    // 客户端
    if (typeof window !== 'undefined' && window.location) {
      const hostname = window.location.hostname;
      // 本地开发环境
      if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        return DOMAIN_CONFIG_DOMAIN;
      }

      return hostname;
    }

    return 'skin-bucks.com';
  };

  /**
   * 获取站点名称
   */
  const getSiteName = (): string => {
    const domain = getCurrentDomain();
    return domain.replace(/^www\./, '').split('.')[0];
  };

  return {
    getCurrentDomain,
    getSiteName,
  };
}
