import { useRouter, useRoute } from 'vue-router';
import { onUnmounted, ref } from 'vue';

export type RouteChangeCallback = (
  to: any,
  from: any,
  type: 'enter' | 'leave' | 'update',
  isFirstCall: boolean,
) => void;

/**
 * @param onRouteChange 路由变化时的回调
 * @param immediate 是否立即执行
 * @returns
 */
export const useRouteObserver = (
  onRouteChange: RouteChangeCallback,
  immediate: boolean = false,
) => {
  const router = useRouter();
  const currentRoute = useRoute();
  const isListening = ref(true);
  const isFirstCall = ref(true);

  const stopListener = router.afterEach((to, from) => {
    if (!isListening.value) return;

    let changeType: 'enter' | 'leave' | 'update' = 'update';

    if (from.path === '/' && to.path !== '/') {
      changeType = 'enter';
    } else if (from.path !== '/' && to.path === '/') {
      changeType = 'leave';
    }

    onRouteChange(to, from, changeType, isFirstCall.value);
    isFirstCall.value = false;
  });

  // 立即执行
  if (immediate && onRouteChange) {
    onRouteChange(currentRoute, null, 'enter', true);
    isFirstCall.value = false;
  }

  // 暂停监听
  const pause = () => {
    isListening.value = false;
  };

  // 恢复监听
  const resume = () => {
    isListening.value = true;
  };

  onUnmounted(() => {
    stopListener();
  });

  return {
    currentRoute,
    pause,
    resume,
    stopListener,
  };
};

export default useRouteObserver;
