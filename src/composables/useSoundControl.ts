import { merge } from 'lodash-es';
import { Howl } from 'howler';
import { useSoundStore } from './../stores/modules/sound';
/**
 * 全局控制音乐声音开关,基于howler实现
 * 参数同howler
 */

interface HowlOptions {
  volume?: number; // 音量大小，范围是 0.0 到 1.0，默认 1.0
  loop?: boolean; // 是否循环播放音频，默认 false
  rate?: number; // 播放速度，范围 0.5 到 4.0，默认 1.0
  autoplay?: boolean; // 是否自动播放，默认 false
  mute?: boolean; // 是否静音，默认 false
  preload?: boolean | 'metadata'; // 是否预加载音频，默认 true
  html5?: boolean; // 是否使用 HTML5 模式，默认 false
  sprite?: { [key: string]: [number, number] }; // 定义音频精灵，起始时间和持续时间
  onload?: () => void; // 加载完成时触发的回调
  onend?: () => void; // 播放完成时触发的回调
  onplayerror?: () => void; // 播放出错时的回调
  onloaderror?: () => void; // 加载出错时的回调
  xhrWithCredentials?: boolean; // 请求是否携带凭据，默认 false
  interrupt?: boolean; // 声音是否可以重叠,默认false,可以
  id?: number; // 声音id,用于查找音乐,不传则为第一个
}

export default function useSoundControl(opt?: HowlOptions) {
  const soundStore = useSoundStore();
  const soundEnabled = computed(() => soundStore.soundEnabled);
  const isPlaying = ref(false);
  const sound = ref();
  const duration = ref<Number | null>(null);
  const defaultVolume = 0.2;

  const soundCache = new Map<string, Howl>();
  const soundPool: Howl[] = [];
  const MAX_SOUNDS = 20;
  const CACHE_LIMIT = 30;

  const play = (src: string | string[], option?: HowlOptions) => {
    // 全局静音
    if (!soundEnabled.value || !src) return;

    try {
      const cacheKey = Array.isArray(src) ? src[0] : src;

      if (!cacheKey || typeof cacheKey !== 'string') {
        return;
      }

      // 检查缓存
      if (soundCache.has(cacheKey)) {
        sound.value = soundCache.get(cacheKey);
        if (sound.value?.state() === 'loaded') {
          try {
            if (sound.value && typeof sound.value.volume === 'function') {
              sound.value.volume(option?.volume || defaultVolume);
              sound.value.play(option?.id);
              isPlaying.value = true;
            } else {
              soundCache.delete(cacheKey);
              createNewSound(src, option);
            }
          } catch (err) {
            console.error('播放缓存音频失败:', err);
            soundCache.delete(cacheKey);
            sessionStorage.setItem(`sound_${cacheKey}`, 'failed');
            createNewSound(src, option);
          }
          return;
        }
      }

      const playState = sessionStorage.getItem(`sound_${cacheKey}`);
      if (playState === 'failed') {
        // 如果之前播放失败，设置
        createNewSound(src, {
          ...option,
          html5: true,
        });
      } else {
        createNewSound(src, option);
      }
    } catch (err) {
      console.error('音频播放错误:', err);
      if (Array.isArray(src) && src.length > 0) {
        createNewSound(src, {
          ...option,
          html5: true,
        });
      }
    }
  };

  const createNewSound = (src: string | string[], option?: HowlOptions) => {
    // 清理缓存
    if (soundCache.size > CACHE_LIMIT) {
      const oldestKey = soundCache.keys().next().value;
      if (oldestKey) {
        const oldSound = soundCache.get(oldestKey);
        if (oldSound) {
          try {
            oldSound.unload();
          } catch (e) {}
        }
        soundCache.delete(oldestKey);
      }
    }

    const { loop, volume, ...delegated } = merge(opt || {}, option || {});

    try {
      sound.value = new Howl({
        src,
        mute: !soundEnabled.value,
        loop: loop || false,
        volume: volume || defaultVolume,
        html5: false,
        preload: true,
        pool: 5,
        xhr: {
          withCredentials: false,
          headers: {
            'Cross-Origin-Resource-Policy': 'cross-origin',
          },
        },
        onload: () => {
          try {
            if (sound.value) {
              duration.value =
                (duration.value || sound.value.duration() || 0) * 1e3;
              // 清除失败状态
              const cacheKey = Array.isArray(src) ? src[0] : src;
              if (cacheKey) {
                sessionStorage.removeItem(`sound_${cacheKey}`);
              }
            }
          } catch (e) {
            console.warn('设置音频持续时间失败:', e);
          }
        },
        onend: () => {
          if (sound.value && !sound.value.playing()) {
            isPlaying.value = false;
          }
        },
        onloaderror: function (_id, error) {
          console.error('加载声音时出错:', error);
          const cacheKey = Array.isArray(src) ? src[0] : src;
          if (cacheKey) {
            soundCache.delete(cacheKey);
            sessionStorage.setItem(`sound_${cacheKey}`, 'failed');
            if (!delegated.html5) {
              createNewSound(src, { ...option, html5: true });
            }
          }
        },
        onplayerror: function (_id, _error) {
          const cacheKey = Array.isArray(src) ? src[0] : src;
          if (cacheKey) {
            soundCache.delete(cacheKey);
          }
        },
        ...delegated,
      });

      // 创建sound失败
      if (!sound.value) return;

      const cacheKey = Array.isArray(src) ? src[0] : src;
      if (cacheKey) {
        // 缓存音频实例
        soundCache.set(cacheKey, sound.value);
      }

      if (soundPool.length >= MAX_SOUNDS) {
        const oldestSound = soundPool.shift();
        if (oldestSound && !oldestSound.playing()) {
          try {
            oldestSound.pause();
          } catch (e) {}
        }
      }

      soundPool.push(sound.value);

      if (delegated.interrupt) {
        try {
          sound.value.stop();
        } catch (e) {}
      }

      // 预加载播放
      try {
        if (sound.value.state() === 'loaded') {
          sound.value.play(delegated.id);
          isPlaying.value = true;
        } else {
          sound.value.once('load', () => {
            if (sound.value) {
              try {
                sound.value.play(delegated.id);
                isPlaying.value = true;
              } catch (e) {}
            }
          });
        }
      } catch (err) {}
    } catch (err) {
      console.error('创建音频实例失败:', err);
      // 记录失败状态
      const cacheKey = Array.isArray(src) ? src[0] : src;
      if (cacheKey) {
        sessionStorage.setItem(`sound_${cacheKey}`, 'failed');
      }
    }
  };

  // 停止
  const stop = (id?: number) => {
    if (!sound.value) {
      return;
    }
    try {
      sound.value.stop(typeof id === 'number' ? id : undefined);
      isPlaying.value = false;
    } catch (e) {
      console.warn('停止声音失败:', e);
    }
  };

  // 暂停
  const pause = (id?: number) => {
    if (!sound.value) {
      return;
    }
    try {
      sound.value.pause(typeof id === 'number' ? id : undefined);
      isPlaying.value = false;
    } catch (e) {
      console.warn('暂停声音失败:', e);
    }
  };

  onUnmounted(() => {
    try {
      soundPool.forEach((s) => {
        try {
          if (s && typeof s.playing === 'function' && !s.playing()) {
            s.unload();
          } else if (s && typeof s.on === 'function') {
            s.on('end', () => {
              try {
                s.off('end');
                s.unload();
              } catch (e) {}
            });
          }
        } catch (e) {}
      });
      soundCache.forEach((s) => {
        try {
          if (s && typeof s.playing === 'function' && !s.playing()) {
            s.unload();
          } else if (s && typeof s.on === 'function') {
            s.on('end', () => {
              try {
                s.off('end');
                s.unload();
              } catch (e) {}
            });
          }
        } catch (e) {}
      });

      soundPool.length = 0;
      soundCache.clear();
    } catch (e) {}
  });

  return {
    sound,
    isPlaying,
    duration,
    play,
    pause,
    stop,
  };
}
