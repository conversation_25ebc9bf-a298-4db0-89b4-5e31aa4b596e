import { ref, type ComputedRef } from 'vue';
import { roundNumber } from '~/utils/numberHelpers';
import { BigNumberCalc } from '~/utils/index';

interface DepositCalculationProps {
  freeConfig: ComputedRef<any>;
  exchangeRate: ComputedRef<number | undefined>;
}

export function useDepositCalculation(props: DepositCalculationProps) {
  const baseAmount = ref(0);
  const feeAmount = ref('0');
  const totalAmount = ref('0');

  // 格式化数字(最多展示2位小数)
  const formatNumber = (value: number) => {
    if (value % 1 === 0) {
      return value.toString();
    } else {
      return parseFloat(value.toFixed(2)).toString();
    }
  };

  // 金额计算
  const convertAmount = (value: number) => {
    if (!value || isNaN(value) || value <= 0) return '0';

    const rate = props.exchangeRate.value;
    if (!rate || rate <= 0) return '0';

    const feeRate = props.freeConfig.value?.fee_percentage || 0;
    const minFee = Number(props.freeConfig.value?.fee_minimum) || 0;

    // 基础金额
    const calculatedBaseAmount = Number(BigNumberCalc.divide(value, rate, 4));
    baseAmount.value = calculatedBaseAmount;

    // 手续费
    let calculatedFee = Number(
      BigNumberCalc.multiply(calculatedBaseAmount, feeRate, 4),
    );
    calculatedFee = Math.max(calculatedFee, minFee);

    // 向上取整
    const finalFee = roundNumber(calculatedFee, 2, 2);
    feeAmount.value = formatNumber(finalFee);

    // 总金额
    const totalPayment = Number(
      BigNumberCalc.add(calculatedBaseAmount, finalFee, 2),
    );
    return formatNumber(totalPayment);
  };

  const updateTotalAmount = (depositAmount: string) => {
    if (!depositAmount || depositAmount === '') {
      totalAmount.value = '0';
      baseAmount.value = 0;
      feeAmount.value = '0';
      return;
    }
    totalAmount.value = convertAmount(Number(depositAmount));
  };

  return {
    baseAmount,
    feeAmount,
    totalAmount,
    formatNumber,
    convertAmount,
    updateTotalAmount,
  };
}
