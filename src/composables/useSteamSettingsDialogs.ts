import { DOMAIN_CONFIG } from '~/constants';
import { useSettingStore } from '~/stores/modules/setting';

// steam 设置提醒
export const useSteamSettingsDialogs = () => {
  const { openEditDialog } = useProfileEditDialog();
  const { $i18n } = useNuxtApp();
  const { t } = $i18n;
  const { settingApi } = useApi();
  const toast = useAppToast();
  const dialog = useAppDialog();
  const settingStore = useSettingStore();
  const userInfo = computed(() => settingStore.userInfo);

  const handleSteamBindSettingDialog = () => {
    openEditDialog({
      title: t('update_steam_trade_link'),
      description: t('trade_link_required'),
      defaultValue: userInfo.value?.trade_url,
      inputPlaceholder: t('enter_trading_link'),
      content: () =>
        h('div', { class: 'text-center mb-4' }, [
          h(
            'a',
            {
              href: DOMAIN_CONFIG.TRADE_URL(userInfo.value?.steam64_id ?? ''),
              target: '_blank',
              class: 'text-blue-500',
            },
            t('get_link'),
          ),
        ]),
      validateFn: (value?: string) => {
        if (!value) {
          toast.error({ content: t('enter_link') });
          return false;
        }
        return true;
      },
      submitFn: (tradeUrl: string) => {
        return settingApi.setTradeUrl({
          trade_url: tradeUrl,
        });
      },
      onSubmit: () => {
        toast.success({ content: t('application_successful') });
        settingStore.getUserInfo();
        dialog.hideByKey();
      },
    });
  };

  return {
    handleSteamBindSettingDialog,
  };
};
