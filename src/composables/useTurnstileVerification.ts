import { useSettingStore } from '~/stores/modules/setting';
import { STORAGE_KEYS } from '~/constants';

export const useTurnstileVerification = () => {
  const { settingApi } = useApi();
  const isVerified = ref(false);
  const showVerification = ref(false);
  const lastActivityTime = ref(Date.now());
  const settingStore = useSettingStore();
  const isCheckingVerification = ref(false);
  let verificationCheckTimeout: NodeJS.Timeout | null = null;

  const userInfo = computed(() => settingStore.userInfo);

  // 验证有效期配置
  const VERIFICATION_DURATIONS = {
    GUEST: 24 * 60 * 60 * 1000, // 未登录用户1天
    LOGGED_IN: 30 * 24 * 60 * 60 * 1000, // 已登录用户30天
  };

  // 默认错误提示
  const DEFAULT_ERROR_MESSAGE = '验证失败，请刷新页面重试';

  // 计算当前用户的过期时间
  const getExpireTime = (baseTime: number = Date.now()) => {
    return (
      baseTime +
      (userInfo.value
        ? VERIFICATION_DURATIONS.LOGGED_IN
        : VERIFICATION_DURATIONS.GUEST)
    );
  };

  // 验证状态
  const verificationState = reactive({
    token: null as string | null,
    error: '',
    loading: false,
  });

  // 检查验证状态
  const checkVerificationStatus = () => {
    try {
      // 首次加载页面不验证，直接视为通过并设置状态
      if (localStorage.getItem(STORAGE_KEYS.TURNSTILE.VERIFIED) === null) {
        localStorage.setItem(STORAGE_KEYS.TURNSTILE.VERIFIED, 'true');
        const verificationTime = Date.now();
        setVerificationStatus(verificationTime);
        isVerified.value = true;
        return true;
      }

      // 从存储中获取验证状态
      const verified =
        localStorage.getItem(STORAGE_KEYS.TURNSTILE.VERIFIED) === 'true';
      const expiryStr = localStorage.getItem(STORAGE_KEYS.TURNSTILE.EXPIRY);
      const expiry = parseInt(expiryStr || '0');

      // 如果已验证，无论过期时间是否有效都视为通过
      if (verified) {
        // 如果过期时间无效或为空，重新设置过期时间
        if (!expiryStr || isNaN(expiry) || expiry <= 0) {
          const verificationTime = Date.now();
          setVerificationStatus(verificationTime);
        }
        isVerified.value = true;
        return true;
      }

      // 未验证状态，需要验证
      isVerified.value = false;
      return false;
    } catch (error) {
      console.error('检查验证状态失败:', error);
      isVerified.value = false;
      return false;
    }
  };

  // 防抖检查验证状态
  const debouncedCheckVerification = () => {
    if (isCheckingVerification.value) return false;

    if (verificationCheckTimeout) {
      clearTimeout(verificationCheckTimeout);
    }

    return new Promise<boolean>((resolve) => {
      verificationCheckTimeout = setTimeout(() => {
        isCheckingVerification.value = true;
        const result = checkVerificationStatus();
        isCheckingVerification.value = false;
        resolve(result);
      }, 300); // 300ms 防抖延迟
    });
  };

  // 显示验证
  const promptVerification = () => {
    showVerification.value = true;
  };

  // 隐藏验证
  const hideVerification = () => {
    showVerification.value = false;
  };

  // 处理验证成功
  const handleVerificationSuccess = async (token: string) => {
    if (!token) {
      verificationState.error = DEFAULT_ERROR_MESSAGE;
      return false;
    }

    verificationState.loading = true;
    verificationState.token = token;
    verificationState.error = '';

    try {
      const response = await settingApi.cfCheck({
        cf_token: token,
      });

      const data = response?.data?.value?.data;

      if (data?.is_valid) {
        // 更新验证状态
        isVerified.value = true;
        lastActivityTime.value = Date.now();

        // 保存验证状态和过期时间
        const verificationTime = Date.now();
        localStorage.setItem(STORAGE_KEYS.TURNSTILE.VERIFIED, 'true');
        setVerificationStatus(verificationTime);

        // 隐藏验证界面
        hideVerification();

        return true;
      } else {
        verificationState.error = DEFAULT_ERROR_MESSAGE;
        return false;
      }
    } catch (error) {
      console.error('验证API调用失败:', error);
      // 根据错误类型设置不同的错误信息
      if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
        verificationState.error = '网络连接失败，请检查网络后重试';
      } else if (error.name === 'TimeoutError') {
        verificationState.error = '请求超时，请重试';
      } else {
        verificationState.error = DEFAULT_ERROR_MESSAGE;
      }
      return false;
    } finally {
      verificationState.loading = false;
    }
  };

  // 设置验证状态和过期时间
  const setVerificationStatus = (verificationTime: number = Date.now()) => {
    localStorage.setItem(
      STORAGE_KEYS.TURNSTILE.EXPIRY,
      getExpireTime(verificationTime).toString(),
    );
  };

  // 重置验证状态
  const resetVerification = () => {
    isVerified.value = false;
    localStorage.removeItem(STORAGE_KEYS.TURNSTILE.VERIFIED);
    localStorage.removeItem(STORAGE_KEYS.TURNSTILE.EXPIRY);
  };

  return {
    isVerified,
    showVerification,
    verificationState,
    checkVerificationStatus,
    debouncedCheckVerification,
    promptVerification,
    hideVerification,
    handleVerificationSuccess,
    resetVerification,
  };
};
