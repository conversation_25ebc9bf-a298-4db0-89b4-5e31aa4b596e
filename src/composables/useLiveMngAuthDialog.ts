import promptsConfirm from '~/composables/dialog/useDialogPromptsConfirm';

export const useLiveMngAuthDialog = () => {
  /**
   * 主播后台未授权对话框
   */
  const showUnauthorizedDialog = () => {
    if (import.meta.server) return;

    nextTick(() => {
      const { openConfirm } = promptsConfirm('signin');
      openConfirm({
        theme: 'light',
        title: '',
        hideCancelBtn: true,
        content: 'You are not authorized to log in.',
        enablePrompts: true,
        confirmBg: '#419FFF',
        confirmText: 'Confirm',
        allowMultiple: false,
      });
    });
  };

  return {
    showUnauthorizedDialog,
  };
};
