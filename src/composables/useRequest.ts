const useRequest = {
  get: <T>(
    url: UrlType,
    params?: ApiRequestData,
    option?: RequestConfig,
  ): Promise<FetchAPIResponseData<T>> => {
    return request<T>(url, params, { method: 'get', ...option });
  },
  post: <T>(
    url: UrlType,
    params?: ApiRequestData,
    option?: RequestConfig,
  ): Promise<FetchAPIResponseData<T>> => {
    return request<T>(url, params, { method: 'post', ...option });
  },
};

export default useRequest;
