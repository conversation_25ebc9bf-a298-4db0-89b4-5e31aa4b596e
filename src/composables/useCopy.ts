type ClipboardOptType = {
  source?: string;
  legacyAppendEl?: Ref<Element>;
};
export function useCopy(options: ClipboardOptType = {}) {
  const { source, legacyAppendEl } = options;
  const permissionWrite = usePermission('clipboard-write');
  function isAllowed(status: string | undefined) {
    return status === 'granted' || status === 'prompt';
  }
  async function copy(value = source, element?: Element) {
    const isSupportted = navigator && 'clipboard' in navigator;
    if (value != null) {
      if (isSupportted && isAllowed(permissionWrite.value))
        await navigator.clipboard.writeText(value);
      else legacyCopy(value, element);
    }
  }
  function legacyCopy(value?: string, element?: Element) {
    const ta = document.createElement('textarea');
    ta.value = value != null ? value : '';
    ta.style.position = 'absolute';
    ta.style.opacity = '0';
    const el = element || legacyAppendEl?.value || document.body;
    el.appendChild(ta);
    ta.select();
    document.execCommand('copy');
    ta.remove();
  }
  return {
    copy,
  };
}
