/**
 * 轮询
 * @param cb 轮询api
 * @param interval 时间间隔
 */
export default function usePolling(
  cb: () => Promise<void>,
  interval: number = 1000,
) {
  let timerId: any = 0;
  let runningTime: number = 0; // 已经轮询总时长
  const running = ref<boolean>(false);

  function stop() {
    if (timerId) {
      clearTimeout(timerId);
    }
    timerId = 0;
    running.value = false;
  }

  async function start(stopTime?: number, immediately: boolean = true) {
    function _loop() {
      timerId = setTimeout(async () => {
        try {
          await cb();
        } catch (_err) {}
        runningTime += interval;
        if (stopTime && runningTime >= stopTime) {
          stop();
          try {
            await cb();
          } catch (_err) {}
        } else {
          if (!timerId) return;
          _loop();
        }
      }, interval);
    }
    if (timerId) {
      return;
    }
    running.value = true;
    if (immediately) {
      await cb();
    }
    _loop();
  }

  function reset(stopTime?: number, immediately: boolean = true) {
    stop();
    start(stopTime, immediately);
  }
  return { start, stop, reset, running };
}
