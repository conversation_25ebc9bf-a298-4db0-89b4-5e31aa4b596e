// 辅助函数通过求和字符的编码将字符串转换为数字
function stringToNumber(str: string) {
  if (typeof str === 'number') return str;
  let sum = 0;
  for (let i = 0; i < str.length; i++) {
    sum += str.charCodeAt(i);
  }
  return sum;
}

// 主随机数生成器函数
function createRandomGenerator(seed: string | undefined) {
  // 如果没有提供种子，返回默认的随机数生成器
  if (seed === undefined) {
    return function (min = 0, max = 1, floating = false) {
      if (floating) {
        const rand = Math.random();
        return min + rand * (max - min);
      } else {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
      }
    };
  }

  // 如果种子是字符串，将其转换为数字
  let seedValue = stringToNumber(seed);

  // 返回种子随机数生成器
  return function (min = 0, max = 1, floating = false) {
    // 使用正弦函数生成伪随机数
    const randomValue = Math.sin(seedValue++) * 10000;
    const fraction = randomValue - Math.floor(randomValue);
    const result = min + fraction * (max - min);

    return floating ? result : Math.round(result);
  };
}

export const useSpinnerConfig = function () {
  const spinnerConfig = ref<any[]>([]);
  const TOTAL_ITEMS = 70; // 旋转总物品数

  // 获取旋转动画参数
  function getSpinAnimation({
    isFastMode,
    customTime,
    hash,
  }: {
    isFastMode: boolean;
    customTime: number;
    hash: string | undefined;
  }) {
    const randomGenerator = createRandomGenerator(hash);
    return {
      // 动画时长：快速模式 1.5-2秒，普通模式 5-7秒
      time:
        customTime ||
        (isFastMode
          ? randomGenerator(1500, 2000)
          : randomGenerator(5000, 7000)),
      // 贝塞尔曲线控制点速度
      bezierFirstSpeed: isFastMode ? 0 : randomGenerator(40, 50) / 100,
      bezierSecondSpeed: isFastMode ? 0 : randomGenerator(5, 10) / 100,
    };
  }

  // 填充多个旋转配置
  function fillSpinnerConfig({
    selectedItem, // 选中物品
    caseItems, // 可选物品列表
    isFastMode, // 是否快速模式
    customTime, // 自定义时间
    length = 4, // 旋转数量
    initialArray = [], // 初始配置数组
    hash, // 随机种子
  }: {
    selectedItem: any;
    caseItems: any;
    isFastMode: boolean;
    customTime: number;
    length: number;
    initialArray: any;
    hash: string | undefined;
  }) {
    if (!selectedItem) {
      spinnerConfig.value = [];
      return;
    }

    // 创建指定数量的旋转配置
    spinnerConfig.value = Array.from({ length }, (_, index) =>
      initialArray[index]
        ? initialArray[index]
        : {
            ...generateNewConfig({
              selectedItem,
              caseItems,
              isFastMode,
              customTime,
              hash: hash ? `${hash}-${index}` : undefined,
            }),
            isMuted: true,
            isSpinning: false,
            spinEnded: false,
          },
    );
  }

  // 生成单个旋转配置
  function generateNewConfig({
    selectedItem,
    caseItems,
    isFastMode,
    customTime,
    hash,
  }: {
    selectedItem: any;
    caseItems: any;
    isFastMode: boolean;
    customTime: number;
    hash: string | undefined;
  }) {
    const { animation, selectedItemPosition, spinnerItems } = getSpinnerConfig({
      caseItems,
      isFastMode,
      selectedItem,
      customTime,
      hash,
    });

    return {
      animation, // 动画参数
      hash, // 随机种子
      isMuted: false, // 是否静音
      isSpinning: true, // 是否正在旋转
      selectedItemPosition, // 选中物品位置
      spinEnded: false, // 是否结束旋转
      spinnerItems, // 旋转物品列表
    };
  }

  // 计算旋转动画位置
  function getSpinnerAnimationPositions({
    cardSize, // 卡片大小
    minOffsetPercentage = 0.9, // 最小偏移百分比
    selectedItemPosition, // 选中物品位置
    hash, // 随机种子
  }: {
    cardSize: number;
    minOffsetPercentage: number;
    selectedItemPosition: number;
    hash: string;
  }) {
    const randomGenerator = createRandomGenerator(hash);

    // 验证偏移百分比
    if (minOffsetPercentage < 0 || minOffsetPercentage > 1) {
      // console.warn(
      //   `无效的最小偏移百分比: ${minOffsetPercentage}, 值必须在0到1之间. 新值: 0`,
      // );
      minOffsetPercentage = Math.max(0, Math.min(minOffsetPercentage, 1));
    }

    // 计算随机偏移量
    const maxOffset = cardSize * 0.45;
    const minOffset = maxOffset * minOffsetPercentage;
    const randomOffset =
      (randomGenerator(0, 1) ? -1 : 1) * randomGenerator(minOffset, maxOffset);

    // 计算起始位置和落点位置
    const startingPosition = cardSize * (TOTAL_ITEMS / 2 - 10);
    const landingPosition = (selectedItemPosition - TOTAL_ITEMS / 2) * cardSize;
    const landingPositionWithOffset = landingPosition + randomOffset;
    return {
      landingPosition,
      landingPositionWithOffset,
      startingPosition,
    };
  }

  // 获取旋转完整配置
  function getSpinnerConfig({
    caseItems,
    isFastMode,
    selectedItem,
    customTime,
    hash,
  }: {
    caseItems: any;
    isFastMode: boolean;
    selectedItem: any;
    customTime: number;
    hash: string | undefined;
  }) {
    const animation = getSpinAnimation({
      isFastMode,
      customTime,
      hash,
    });

    const randomGenerator = createRandomGenerator(hash);

    if (caseItems.length === 0) {
      return {
        animation,
        selectedItemPosition: 0,
        spinnerItems: [],
      };
    }

    // 计算额外物品数量
    const additionalItems = randomGenerator(25, TOTAL_ITEMS / 2) - 20;
    // 根据概率生成物品列表
    let items: any[] = [];
    const totalWeight = caseItems.reduce(
      (sum: number, item: any) => sum + (Number(item.chance) || 0.01),
      0,
    );
    const cumulativeProbabilities: number[] = [];
    let cumulativeProbability = 0;
    caseItems.forEach((item: any) => {
      const normalizedChance = (Number(item.chance) || 0.01) / totalWeight;
      cumulativeProbability += normalizedChance;
      cumulativeProbabilities.push(cumulativeProbability);
    });
    for (let i = 0; i < TOTAL_ITEMS; i++) {
      const randomValue = randomGenerator(0, 1, true);
      let selectedIndex = 0;
      for (let j = 0; j < cumulativeProbabilities.length; j++) {
        if (randomValue <= cumulativeProbabilities[j]) {
          selectedIndex = j;
          break;
        }
      }

      items.push(caseItems[selectedIndex]);
    }
    // 打乱顺序
    items = shuffleArrayWithHash(items, hash);
    // 在指定位置插入选中物品
    const insertPosition = TOTAL_ITEMS / 2 + additionalItems;
    items.splice(insertPosition, 0, selectedItem);

    return {
      animation,
      selectedItemPosition: insertPosition,
      spinnerItems: items,
    };
  }

  return {
    spinnerConfig,
    fillSpinnerConfig,
    generateNewConfig,
    getSpinAnimation,
    getSpinnerAnimationPositions,
    getSpinnerConfig,
  };
};

function shuffleArrayWithHash(array: any[], hash: string | undefined) {
  if (!array || array.length <= 1) return [...array];

  const filteredArray = array.filter((item) => item != null);
  if (filteredArray.length === 0) return [...array];

  const randomGenerator =
    hash !== undefined
      ? createRandomGenerator(hash)
      : (min: number, max: number) => Math.random() * (max - min) + min;

  const getItemId = (item: any) => {
    if (item == null) return 'null-or-undefined';
    return item.goods_id || item.uid || JSON.stringify(item);
  };

  const result = [...filteredArray];
  for (let i = result.length - 1; i > 0; i--) {
    const randomIndex = Math.floor(randomGenerator(0, i + 1));
    [result[i], result[randomIndex]] = [result[randomIndex], result[i]];
  }

  const maxConsecutiveSame = 3;
  if (result.length <= maxConsecutiveSame) return result;

  const itemIds = result.map(getItemId);

  for (let i = maxConsecutiveSame; i < result.length; i++) {
    const currentId = itemIds[i];
    let isConsecutive = true;

    for (let j = 1; j <= maxConsecutiveSame; j++) {
      if (itemIds[i - j] !== currentId) {
        isConsecutive = false;
        break;
      }
    }

    if (isConsecutive) {
      let swapped = false;
      for (let j = i + 1; j < result.length; j++) {
        if (itemIds[j] !== currentId) {
          [result[i], result[j]] = [result[j], result[i]];
          [itemIds[i], itemIds[j]] = [itemIds[j], itemIds[i]];
          swapped = true;
          break;
        }
      }
      if (!swapped) {
        const differentItems = [];
        for (let j = 0; j < i; j++) {
          if (itemIds[j] !== currentId) {
            differentItems.push(j);
          }
        }

        if (differentItems.length > 0) {
          const randomDiffIndex =
            differentItems[
              Math.floor(randomGenerator(0, differentItems.length))
            ];
          [result[i], result[randomDiffIndex]] = [
            result[randomDiffIndex],
            result[i],
          ];
          [itemIds[i], itemIds[randomDiffIndex]] = [
            itemIds[randomDiffIndex],
            itemIds[i],
          ];
        }
      }
    }
  }
  return result.filter((item) => item != null);
}
