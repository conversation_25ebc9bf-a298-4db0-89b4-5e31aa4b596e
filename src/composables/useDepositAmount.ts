import { ref } from 'vue';

interface DepositAmountOptions {
  initialAmount?: string;
  minAmount?: number;
  maxAmount?: number;
  onAmountChange?: (amount: string) => void;
}

export function useDepositAmount(options: DepositAmountOptions = {}) {
  const {
    initialAmount = '100',
    minAmount = 1,
    maxAmount = 999999,
    onAmountChange,
  } = options;

  const depositAmount = ref<string>(initialAmount);

  // 快捷档位
  const depositQuickOptions = ref<string[]>([
    '5',
    '10',
    '30',
    '50',
    '100',
    '300',
    '500',
    '1000',
  ]);

  const onlyAllowNumber = (value: string) => {
    return /^\d*$/.test(value);
  };

  // 金额验证
  const validateAmount = (value: string) => {
    if (!value) return '请输入金额';

    if (value.length > 1 && value.startsWith('0')) {
      return '请输入有效数字格式';
    }

    const num = Number(value);
    if (isNaN(num)) return '请输入有效数字';
    if (num < minAmount) return `最小金额为 ${minAmount}`;
    if (num > maxAmount) return `最大金额为 ${maxAmount}`;

    return null;
  };

  const createValidator = () => {
    return (_: any, __: any, callback: Function) => {
      const value = depositAmount.value;
      const error = validateAmount(value);
      if (error) {
        return callback(new Error(error));
      }
      return callback();
    };
  };

  const rules = {
    depositAmount: [
      {
        trigger: ['blur'],
        required: true,
        validator: createValidator(),
      },
    ],
  };

  // 金额输入
  const handleAmountInput = (value: string) => {
    if (value === '') {
      depositAmount.value = '';
      onAmountChange?.('');
      return;
    }

    const numericValue = value.replace(/[^0-9]/g, '');
    if (isNaN(Number(numericValue))) {
      return;
    }

    depositAmount.value = numericValue;

    onAmountChange?.(numericValue);
  };

  const handleQuickOptionClick = (option: string) => {
    handleAmountInput(option);
  };

  // 设置初始值
  const setInitialValue = (amount: string = initialAmount) => {
    depositAmount.value = amount;
    handleAmountInput(amount);
  };

  const resetAmount = () => {
    depositAmount.value = '';
    onAmountChange?.('');
  };

  return {
    depositAmount,
    depositQuickOptions,
    rules,
    onlyAllowNumber,
    validateAmount,
    handleAmountInput,
    handleQuickOptionClick,
    setInitialValue,
    resetAmount,
  };
}
