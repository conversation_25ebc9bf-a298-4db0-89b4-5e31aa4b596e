import BaseEditDialog from '~/components/dialog/profile/BaseEditDialog.vue';
import AvatarChangeDialog from '~/components/dialog/profile/AvatarChangeDialog.vue';

interface BaseEditDialogOptions {
  title: string;
  description?: string;
  submitText?: string;
  maxlength?: number | undefined;
  validateFn?: (value?: string) => boolean | Promise<boolean>;
  submitFn: (value: string) => Promise<any>;
  onInput?: (value: string, callback: (newValue: string) => void) => void;
  inputValidate?: boolean;
  width?: string;
  style?: Record<string, any>;
  showDefaultInput?: boolean;
  defaultValue?: string;
  inputPlaceholder?: string;
  loading?: boolean;
  onSubmit?: (data: any) => void;
  icon?: Component | (() => VNode);
  content?: Component | (() => VNode);
  form?: Component | (() => VNode);
}

export function useProfileEditDialog() {
  const dialog = useAppDialog();

  const openEditDialog = (options: BaseEditDialogOptions) => {
    const {
      title,
      description,
      submitText,
      maxlength,
      validateFn,
      submitFn,
      onInput,
      inputValidate,
      width = '445px',
      style = {},
      showDefaultInput = true,
      defaultValue = '',
      inputPlaceholder = '',
      loading = false,
      onSubmit,
      icon,
      content,
      form,
    } = options;
    return dialog.open(BaseEditDialog, {
      style: {
        width,
        ...style,
      },
      contentProps: {
        title,
        description,
        submitText,
        maxlength,
        loading,
        validateFn,
        submitFn,
        inputValidate,
        onInput,
        showDefaultInput,
        defaultValue,
        inputPlaceholder,
        onSubmit,
      },
      slots: {
        icon: icon
          ? typeof icon === 'function'
            ? icon
            : () => icon
          : undefined,
        content: content
          ? typeof content === 'function'
            ? content
            : () => content
          : undefined,
        form: form
          ? typeof form === 'function'
            ? form
            : () => form
          : undefined,
      },
    });
  };

  interface AvatarEditDialogOptions {
    width?: string;
    style?: Record<string, any>;
    defaultValue?: string;
  }
  const openAvatarDialog = (options: AvatarEditDialogOptions) => {
    const { width = '445px', style = {}, defaultValue = '' } = options;
    return dialog.open(AvatarChangeDialog, {
      style: {
        width,
        ...style,
      },
      contentProps: {
        defaultValue,
      },
    });
  };

  return {
    openEditDialog,
    openAvatarDialog,
  };
}
