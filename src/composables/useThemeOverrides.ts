/*
 * @Description: naiveui组件主题配置
 *
 */
import type { GlobalThemeOverrides } from 'naive-ui';

const useThemeOverrides = () => {
  const theme = ref(null);
  const primary = '#F8B838';
  const purple = '#7D90CA';
  const themeOverrides: GlobalThemeOverrides = {
    common: {
      primaryColor: primary,
    },
    Skeleton: {
      color: 'rgba(13, 17, 29, 0.7)',
      colorEnd: 'rgba(13, 17, 29, 1)',
    },
    Notification: {
      color: 'rgba(32,26,23,0.8)',
      textColor: '#fff',
      iconColorError: 'red',
    },
    Popover: {
      color: '#151A29',
      borderRadius: '4px',
      padding: '4px',
      textColor: '#FFFFFF',
      space: '0',
    },
    Dialog: {
      textColor: '#fff',
      closeIconColor: '#fff',
      closeIconSize: '19px',
      closeMargin: '33px 32px 0 0',
      contentMargin: '0',
    },
    Input: {
      caretColor: '#FFF',
      borderHover: '1px solid #49536F',
      borderFocus: '1px solid #49536F',
      border: '1px solid #49536F',
      borderRadius: '4px',
      color: '#141419',
      colorFocus: '#141419',
      textColor: '#FFF',
      boxShadowFocus: 'transparent',
      placeholderColor: '#5D688A',
      colorFocusError: '#141419',
      heightMedium: '42px',
    },
    Checkbox: {
      color: 'transparent',
      border: `1px solid ${purple}`,
      checkMarkColor: '#000',
      colorChecked: primary,
      colorTableHeader: primary,
      textColor: purple,
      borderChecked: `1px solid ${primary}`,
      labelFontWeight: 'bold',
    },
    InputNumber: {
      peers: {
        Input: {
          paddingLarge: '0',
          heightLarge: '42px',
          borderHover: '1px solid rgba(255, 255, 255, 0.2)',
          borderFocus: '1px solid rgba(255, 255, 255, 0.2)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          color: 'rgba(80, 100, 160, 0.15)',
          colorFocus: 'rgba(80, 100, 160, 0.15)',
          textColor: '#FFF',
          caretColor: '#FFF',
          boxShadowFocus: 'transparent',
          placeholderColor: '#929CA6',
        },
        Button: {
          heightMedium: '42px',
          color: 'rgba(80, 100, 160, 0.3)',
          iconSizeMedium: '42px',
        },
      },
    },
    Form: {
      labelTextColor: '#7D90CA',
      labelPaddingHorizontal: '0',
    },
    Select: {
      peers: {
        InternalSelectMenu: {
          color: '#151A29',
          optionTextColor: purple,
          optionTextColorActive: '#fff',
          optionTextColorPressed: purple,
          actionDividerColor: '',
          optionColorPending: '#25314B',
          optionColorActive: '#25314B',
          optionColorActivePending: '#25314B',
          actionTextColor: '#fff',
          optionPaddingMedium: '0  16px',
        },
        InternalSelection: {
          color: 'transparnet',
          colorActive: 'rgba(255, 255, 255, 0)',
          fontSizeLarge: '14px',
          border: `1px solid #323A51`,
          arrowColor: purple,
          textColor: '#7D90CA',
          caretColor: '',
          borderFocus: 'transparent',
          borderHover: 'transparent',
          borderActive: 'transparent',
          boxShadowActive: 'transparent',
          boxShadowHover: 'transparent',
          boxShadowFocus: 'transparent',
          placeholderColor: purple,
          paddingSingle: '0 28px 0 12px',
          heightMedium: '42px',
        },
      },
      Popover: {
        padding: '10px 0',
      },
    },
    Popselect: {
      peers: {
        InternalSelectMenu: {
          color: '#24282F',
          groupHeaderTextColor: '#929CA6',
          actionTextColor: '#929CA6',
          optionTextColor: '#929CA6',
        },
      },
    },
    Button: {
      color: primary,
      colorPrimary: primary,
      colorPrimaryHover: '#FBD488',
      colorPrimaryPressed: '#C6932D',
      colorPrimaryActive: '#C6932D',
      colorHover: '#C6932D',
      colorPressed: '#C6932D',
      colorActive: '#C6932D',
      colorFocus: '#C6932D',
      colorHoverPrimary: '#FBD488',
      colorPressedPrimary: '#C6932D',
      colorActivePrimary: '#C6932D',
      colorFocusPrimary: '#C6932D',
      colorTertiary: 'rgba(22, 27, 32)',
      colorTertiaryHover: 'rgba(9, 11, 13)',
      colorTertiaryPressed: 'rgba(9, 11, 13)',
      textColor: '#0A0D14',
      textColorPrimary: '#000',
      textColorTertiary: '#929ca6',
      textColorHover: '#0A0D14',
      textColorPressed: '#0A0D14',
      textColorActive: '#0A0D14',
      textColorFocus: '#0A0D14',
      textColorFocusPrimary: '#0A0D14',
      textColorHoverPrimary: '#0A0D14',
      textColorPressedPrimary: '#0A0D14',
      textColorActivePrimary: '#0A0D14',
      textColorDisabledPrimary: '#0A0D14',
      textColorGhostHoverPrimary: '#FBD488',
      borderHoverPrimary: '#C6932D',
      borderPressedPrimary: '0',
      borderActivePrimary: '0',
      borderFocusPrimary: '0',
      border: `1px solid ${primary}`,
      borderHover: `1px solid #FBD488`,
      borderPressed: `1px solid #C6932D`,
      borderActive: `1px solid #C6932D`,
      borderFocus: `1px solid ${primary}`,
      textColorTextHoverPrimary: '#FBD488',
      textColorTextPressedPrimary: '#C6932D',
      textColorTextFocusPrimary: '#C6932D',
      textColorTextDisabledPrimary: 'rgba(244, 179, 63, 0.9)',
      heightMedium: '42px',
      paddingMedium: '12px 16px',
      opacityDisabled: '1',
      borderDisabled: '0',
      textColorGhost: primary,
      textColorGhostHover: '#FBD488',
      textColorGhostPressed: '#C6932D',
      textColorGhostActive: '#C6932D',
      textColorGhostFocus: '#C6932D',
      textColorGhostDisabled: '#464C64',
      // fontWeight: '600',
    },
    AutoComplete: {
      peers: {
        InternalSelectMenu: {
          color: '#201A17',
          optionTextColor: 'rgba(176, 170, 167, 1)',
          optionColorActive: '',
          optionColorPending: '',
          optionColorActivePending: primary,
        },
        Input: {
          fontSizeMedium: '14px',
          paddingMedium: '8px',
          heightMedium: '42px',
          borderHover: '1px solid #323A51',
          borderFocus: '1px solid #323A51',
          border: '1px solid #323A51',
          borderRadius: '4px',
          color: 'transparent',
          colorFocus: 'transparent',
          textColor: '#7D90CA',
          caretColor: '#7D90CA',
          boxShadowFocus: 'transparent',
          placeholderColor: '#4B5679',
        },
      },
    },
    Tabs: {
      paneTextColor: '#7D90CA',
      panePaddingMedium: '16px 0 0 0',
      tabTextColorBar: '#7D90CA',
      tabTextColorActiveBar: '#FFFFFF',
      tabGapSmallBar: '40px',
      tabGapMediumBar: '40px',
      tabColor: '#F8B838',
      tabTextColorCard: '#7D90CA',
      tabBorderColor: '#F8B838',
      tabPaddingSmallBar: '11px 0',
      tabPaddingMediumBar: '11px 0',
    },
    DataTable: {
      thColor: '#151A29',
      thColorHover: '#151A29',
      thColorModal: '#20263B',
      thColorHoverModal: '#20263B',
      thTextColor: '#fff',
      tdTextColor: 'rgba(255, 255, 255, 1)',
      borderColor: 'rgba(80, 100, 160, 0.15)',
      borderColorModal: 'rgba(80, 100, 160, 0.15)',
      tdColor: 'rgba(255, 255, 255, 0)',
      tdColorModal: 'rgba(255, 255, 255, 0)',
      tdColorHover: 'rgba(255, 255, 255, 0.02)',
      tdColorHoverModal: 'rgba(255, 255, 255, 0.02)',
      fontSizeSmall: '12px',
    },
    Badge: {
      fontSize: '10px',
    },
    Spin: {
      color: primary,
    },
    Avatar: {
      borderRadius: '4px',
    },
    Pagination: {
      itemMarginMedium: '0 0 0 2px',
    },
  };
  return {
    theme,
    themeOverrides,
  };
};

export default useThemeOverrides;
