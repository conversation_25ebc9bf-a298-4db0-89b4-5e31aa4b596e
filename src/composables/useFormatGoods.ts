import { ref } from 'vue';

export const useFormatGoods = () => {
  const goodsInfosMap = ref(new Map());
  const formatSteaminventoryList = (res: any) => {
    if (!res?.items) return res;
    const { items = [], goods: goodsInfos = [] } = res;
    const goodsMap = new Map();
    goodsInfos.forEach((goods: any) => {
      goodsMap.set(goods.goods_id, goods);
      goodsMap.set(`${goods.goods_id}_${goods.style}`, goods);
    });
    items.forEach((item: any) => {
      item.goods_info =
        goodsMap.get(`${item.goods_id}_${item.style_id}`) ||
        goodsMap.get(item.goods_id);
      item.selected = false;
    });
    goodsInfosMap.value = goodsMap;
    return items;
  };

  return {
    formatSteaminventoryList,
    goodsInfosMap,
  };
};
