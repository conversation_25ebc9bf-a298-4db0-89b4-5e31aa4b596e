import { debounce } from 'lodash-es';
import { AnimateStatus, type PlayOptConfig } from '~/types/cases';
import { Sound } from '~/types/sound';

/**
 * 开箱动画
 * @param scrollBox Ref<HTMLDivElement> 动画盒子元素ref
 * @param opt 配置信息
 * @param opt.isCol 横屏还是竖屏滚动
 * @param opt.totalLength 动画元素总长度
 */
type OptConfig = {
  isCol?: boolean;
  finish?: boolean;
  totalLength?: number;
};
// 字符串转换为数字
function stringToNumber(str: string) {
  if (typeof str === 'number') return str;
  let sum = 0;
  for (let i = 0; i < str.length; i++) {
    sum += str.charCodeAt(i);
  }
  return sum;
}

function createRandomGenerator(seed: string | undefined) {
  // 如果没有提供种子，返回默认的随机数生成器
  if (seed === undefined) {
    return function (min = 0, max = 1, floating = false) {
      if (floating) {
        const rand = Math.random();
        return min + rand * (max - min);
      } else {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
      }
    };
  }

  // 如果种子是字符串，将其转换为数字
  let seedValue = stringToNumber(seed);
  // 返回种子随机数生成器
  return function (min = 0, max = 1, floating = false) {
    // 使用正弦函数生成伪随机数
    const randomValue = Math.sin(seedValue++) * 10000;
    const fraction = randomValue - Math.floor(randomValue);
    const result = min + fraction * (max - min);
    return floating ? result : Math.round(result);
  };
}
function generateOffset(w: number, isCol?: boolean) {
  function getRandom(min: number, max: number) {
    return Math.random() * (max - min) + min;
  }
  // 生成随机数选择负数或正数区间, 50%的概率选择负数区间
  const isNegative = Math.random() < 0.5;
  if (isCol) {
    // 生成 0.05 - 0.15 之间的随机数
    const random1 = getRandom(0.05, 0.15);
    // 生成 -0.4 到 -0.1 之间的随机数
    const random2 = getRandom(-0.4, -0.1);
    return isNegative ? random1 * w : random2 * w;
  } else {
    const random = getRandom(0.1, 0.3);
    return isNegative ? -random * w : random * w;
  }
}
export default function useOpenCase(
  scrollBox: Ref<HTMLDivElement>,
  hash: string | undefined,
  opt?: OptConfig,
) {
  let fast = false;
  let skip = false;
  const { totalLength = 70, isCol, finish } = opt || {};
  // 每个饰品宽度占比
  const skinBaseWidth = (1 / (totalLength + 1)) * 100;

  const randomGenerator = createRandomGenerator(hash);
  // 初始位置
  const startIndex = randomGenerator(25, totalLength / 2) - 15;
  // 在指定位置插入物品,等开箱时替换为选中物品
  const endIndex = totalLength / 2 + startIndex;
  // 初始位置 14 35 49
  const startX = (totalLength / 2 - startIndex) * skinBaseWidth;
  // 最终停下位置
  const cardEnd = isCol ? 0.25 : 0;
  const endX = (totalLength / 2 - endIndex - cardEnd) * skinBaseWidth;

  // 隐藏scrollBox.value的transition
  const hideBoxTransition = () => {
    scrollBox.value && (scrollBox.value.style.transition = 'none');
    setTimeout(() => {
      scrollBox.value && (scrollBox.value.style.transition = '');
    }, 40);
  };

  const { play } = useSoundControl();

  // 0无动画,1到达位置回弹位置,准备回弹,2回弹动画结束
  const animateStatus = ref(
    finish ? AnimateStatus.FINISH : AnimateStatus.UN_SATRT,
  );
  // 滚动偏移量
  const translate = ref(finish ? endX : startX);
  // 当前动画中心元素索引,即高亮的元素
  const highlightIndex = ref(finish ? endIndex : startIndex);
  // 开始滚动
  const start = (opt: PlayOptConfig) => {
    if (opt.fast !== undefined) {
      fast = opt.fast;
    }
    if (opt.skip !== undefined) {
      skip = opt.skip;
    }
    animateStatus.value = AnimateStatus.START;
    if (skip) {
      nextTick(() => {
        animateStatus.value = AnimateStatus.FINISH;
      });
      translate.value = endX;
      highlightIndex.value = endIndex;
      return;
    }
    const normalTime = 5 * 1000;
    const fastTime = 2 * 1000;
    // 音乐
    const offsetX = generateOffset(skinBaseWidth, isCol);

    // 加入回弹后停留的位置
    const endOffsetX = endX - offsetX;
    translate.value = endOffsetX;
    resume();
    // 滚动结束
    useTimeoutFn(transitionendFn, fast ? fastTime : normalTime);
  };
  // 帧动画更新透明度
  const updateOpacity = () => {
    if (animateStatus.value > 1 || !window || !scrollBox.value) return;
    const computedStyle = window.getComputedStyle(scrollBox.value);
    const matrix = computedStyle.transform;
    if (matrix && matrix !== 'none') {
      const matrixMatch = matrix.match(/matrix\(([^)]+)\)/);
      if (!matrixMatch) return;
      const matrixValues = matrixMatch[1].split(', ');
      // 当前盒子偏移量px
      const curEndX = parseFloat(matrixValues[isCol ? 5 : 4]);
      // 盒子总宽度/长度
      const boxSzie = isCol
        ? scrollBox.value.clientHeight
        : scrollBox.value.clientWidth;
      // 当前位置百分比
      const curEndXPersent = (curEndX / boxSzie) * 100;
      let moveIndex = Math.floor((startX - curEndXPersent) / skinBaseWidth);
      // 余数
      const moveYu = (startX - curEndXPersent) % skinBaseWidth;
      if (moveYu >= skinBaseWidth / 2) {
        moveIndex += 1;
      }
      const curIndex = startIndex + moveIndex;
      // 余数小于饰品宽度一半,大于等于饰品宽度一半则在下一个
      if (
        highlightIndex.value !== curIndex &&
        moveIndex > 0 &&
        curIndex <= endIndex
      ) {
        // 设置当前元素高亮
        highlightIndex.value = curIndex;
        debounce(() => play(Sound.CASE_OPEN_ROLL), 200)();
      }
    }
  };
  const { pause, resume } = useRafFn(updateOpacity, {
    immediate: false,
  });
  // 重置数据回原来的位置
  const reset = () => {
    // 重置滚动展示初始位置
    hideBoxTransition();
    translate.value = startX;
    highlightIndex.value = startIndex;
    animateStatus.value = AnimateStatus.UN_SATRT;
  };

  // 监听scrollBox.value动画结束
  const transitionendFn = () => {
    if (animateStatus.value === AnimateStatus.START) {
      // 滚动结束,开始回弹
      useTimeoutFn(transitionendFn, 800);
    }
    animateStatus.value += 1;
    // 回弹
    scrollBox.value.style.transitionTimingFunction = 'ease';
    scrollBox.value.style.transitionDuration = '800ms';
    translate.value = endX;
    highlightIndex.value = endIndex;
    pause();
  };
  return {
    start,
    reset,
    endIndex,
    highlightIndex,
    animateStatus,
    translate,
  };
}
