import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';

export function useEchart(elRef: Ref<HTMLElement | null>) {
  let chart: echarts.ECharts | null = null;

  const init = (option: EChartsOption) => {
    if (!elRef.value) return;
    chart = echarts!.init(elRef.value);
    chart.setOption({
      grid: {
        left: 20,
        right: 40,
        top: 20,
        bottom: 20,
        containLabel: true,
      },
      ...option,
    });
  };

  const resize = () => {
    chart?.resize();
  };

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', resize);
    chart?.dispose();
  });

  const setOption = (option: EChartsOption) => {
    if (!chart) return;
    chart.setOption({
      grid: {
        left: 20,
        right: 40,
        top: 20,
        bottom: 20,
        containLabel: true,
      },
      ...option,
    });
  };

  return {
    init,
    setOption,
  };
}
