import { NCheckbox } from 'naive-ui';
import Confirm from '~/components/dialog/Confirm.vue';
type OpenOptType = {
  title: string;
  content: string;
  contentClass?: string;
  onConfirm?: () => void;
  onClose?: () => void;
  onEsc?: () => void;
  promptKey?: string; // 新增promptKey用于区分不同场景
  enablePrompts?: boolean;
  confirmText?: string;
  confirmIcon?: Component;
  confirmBg?: string;
  confirmColor?: string;
  cancelText?: string;
  cancelIcon?: Component;
  cancelBg?: string;
  cancelColor?: string;
  hideConfirmBtn?: boolean;
  hideCancelBtn?: boolean;
  allowMultiple?: boolean;
  theme?: 'light' | 'dark';
};

export default function useDialogPromptsConfirm(localName: string) {
  const dialog = useAppDialog();
  const dialogRef = ref();
  const { $i18n } = useNuxtApp();
  const { t } = $i18n;
  const prompts = ref(false);

  return {
    openConfirm(opt: OpenOptType) {
      const {
        title,
        content,
        contentClass,
        onConfirm,
        onClose,
        onEsc,
        promptKey = 'default',
        enablePrompts = false,
        confirmText,
        confirmIcon,
        confirmBg,
        confirmColor,
        cancelText,
        cancelIcon,
        cancelBg,
        cancelColor,
        hideConfirmBtn,
        hideCancelBtn,
        allowMultiple = true,
        theme = 'dark',
      } = opt;

      const storageKey = `${localName}_${promptKey}`;
      const localPrompts = Number(localStorage.getItem(storageKey) || '');
      const textColor = theme === 'light' ? 'text-[#1B1E23]' : 'text-white';
      const bgColor =
        theme === 'light' ? 'bg-white border-white' : 'bg-[#1B1E23]';
      if (isToday(localPrompts)) {
        onConfirm?.();
      } else {
        dialogRef.value = dialog.open(Confirm, {
          class: `max-w-[90%] xl:max-w-5xl rounded-xl ${bgColor}`,
          closable: theme !== 'light',
          contentProps: {
            title,
            content: h(
              'div',
              null,
              [
                h('div', {
                  class: [`text-[16px] ${textColor} mb-[20px]`, contentClass],
                  innerHTML: content,
                }),
                !enablePrompts &&
                  h(
                    NCheckbox,
                    {
                      class: `${textColor} flex justify-center items-center text-center`,
                      size: 'large',
                      'v-model:checked': prompts.value,
                      'onUpdate:checked': (value: boolean) => {
                        prompts.value = value;
                      },
                    },
                    {
                      default: () => [
                        h('span', { class: `text-[16px] ${textColor}` }, [
                          t('not_displaying_prompts_within_1_day'),
                        ]),
                      ],
                    },
                  ),
              ].filter(Boolean),
            ),
            confirmText,
            confirmIcon,
            confirmBg,
            confirmColor,
            cancelText,
            cancelIcon,
            cancelBg,
            cancelColor,
            hideConfirmBtn,
            hideCancelBtn,
            onConfirm: () => {
              if (prompts.value && !enablePrompts) {
                localStorage.setItem(
                  storageKey,
                  new Date().getTime().toString(),
                );
              }
              dialogRef.value?.destroy();
              onConfirm?.();
            },
          },
          allowMultiple,
          onEsc: () => onEsc?.(),
          onClose: () => onClose?.(),
        });
      }
    },
  };
}
