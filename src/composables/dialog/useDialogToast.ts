import { merge } from 'lodash-es';
import Toast from '~/components/dialog/toast.vue';

export default function useDialogToast() {
  const dialog = useAppDialog();
  const dislogOpen = (
    message: string,
    time: number = 3000,
    type: string,
    opt?: any,
  ) => {
    const defaultOpt = {
      style: { width: '200px', padding: '25px 30px', borderRadius: '10px' },
      closable: false,
      allowed: true,
      contentProps: {
        message,
        time,
        type,
        autoClose: true,
      },
    };
    const mergeOpt = merge(defaultOpt, opt);
    return dialog.open(Toast, mergeOpt);
  };
  return {
    open(message: string, time?: number) {
      return dislogOpen(message, time, 'default', {
        maskClosable: true,
      });
    },
    openError(message: string, time?: number) {
      return dislogOpen(message, time, 'error', {
        maskClosable: true,
      });
    },
    openSuccess(message: string, time?: number) {
      return dislogOpen(message, time, 'success', {
        maskClosable: true,
      });
    },
    openWarning(message: string, time?: number) {
      return dislogOpen(message, time, 'warning', {
        maskClosable: true,
      });
    },
    openDialog(opt: {
      titleT: string;
      message: string;
      [key: string]: any;
      closable?: boolean;
    }) {
      const defaultOpt = {
        style: { width: '400px' },
        closable: opt.closable,
        title: () =>
          h(
            'div',
            {
              class: 'w-full text-white text-[20px] text-center',
            },
            opt.titleT,
          ),
        contentProps: {
          btns: opt?.btns,
          autoClose: false,
          contentClass: '!text-left',
        },
        onClose: () => {
          opt.onClose?.();
        },
        onConfirm: (e: any) => {
          opt.onConfirm?.();
          e();
        },
      };
      const mergeOpt = merge(defaultOpt, opt);
      return dislogOpen(opt.message, 0, 'default', mergeOpt);
    },
  };
}
