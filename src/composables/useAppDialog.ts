import {
  type AsyncComponentLoader,
  type Component,
  createVNode,
  Suspense,
  isRef,
  markRaw,
  watch,
  reactive,
  isReactive,
  toRaw,
} from 'vue';
import {
  type DialogReactive,
  type ConfigProviderProps,
  createDiscreteApi,
} from 'naive-ui';
import merge from 'lodash-es/merge';
import { createI18n } from 'vue-i18n';
import { debounce } from 'lodash-es';
import ProvideComponent from '~/components/dialog/ProvideComponent.vue';
import lozyload from '~/directives/lazyload';
import config from '@/i18n/index';
import { useAppStore } from '~/stores/modules/app';

export interface UseDialogOptions extends DialogReactive {
  onConfirm: (e?: any) => void;
  contentProps?: Record<string, any>;
  slots?: Record<string, Component | (() => VNode) | undefined>; // 传递插槽
  allowMultiple?: boolean;
}

const { themeOverrides } = useThemeOverrides();

const configProviderPropsRef = computed<ConfigProviderProps>(() => ({
  themeOverrides,
}));

export const { dialog: baseDialog, app } = createDiscreteApi(['dialog'], {
  configProviderProps: configProviderPropsRef,
});

export const singleDialogMap = reactive(new Map());

export const dialogReactiveListMap = reactive(new Map());

// 将工具函数移到单独的对象中
const DialogUtils = {
  isAsyncComponent(
    component: Component | AsyncComponentLoader<Component>,
  ): component is AsyncComponentLoader<Component> {
    return typeof component === 'function';
  },

  generateKey(
    component: Component | AsyncComponentLoader<Component>,
  ): string | null {
    if (this.isAsyncComponent(component)) {
      const match = component.toString().match(/import\("(.+)"\)/);
      return match ? match[1] : null;
    }
    return (
      (component as Component & { __name?: string }).__name?.toString() ?? null
    );
  },

  isDialogOpen(content: Component | AsyncComponentLoader<Component>): boolean {
    const key = this.generateKey(content);
    return Boolean(key && singleDialogMap.has(key));
  },
};

const DEFAULT_DIALOG_OPTIONS = {
  showIcon: false,
  blockScroll: true,
  header: ' ',
  class:
    '!bg-[#151A29] rounded-xl text-white max-sm:p-[12px] py-[31px] box-border n-dialog__close-position',
  titleClass: 'text-[24px]/7  pb-[24px] text-center text-white justify-center',
  style: {},
  maskClosable: false,
  allowMultiple: true,
  zIndex: 9999,
} as const;

let isInitialized = false;

let i18nInstance: any = null;

// interface DialogOptions extends Partial<UseDialogOptions> {
//   slots?: Record<string, Component | (() => VNode) | undefined>;
// }

const getLang = () => {
  const appStore = useAppStore();
  const { $i18n } = useNuxtApp();
  const languages = appStore.languages;
  const currentLang = languages.findIndex(
    (el) => el.upload_filename === appStore.currentLang,
  );
  if (currentLang > -1) {
    return appStore.currentLang;
  }
  return $i18n.defaultLocale;
};

export default function useAppDialog() {
  const { $i18n, vueApp, $td } = useNuxtApp();
  if (!isInitialized) {
    if (!i18nInstance) {
      i18nInstance = createI18n({
        ...config,
        locale: $i18n.locale,
        locales: $i18n.locales,
        messages: $i18n.messages,
      } as any);
    }
    app.use(i18nInstance);
    app.use(lozyload);
    app.config.globalProperties.$td = $td;
    app.provide('td', $td);
    const componentNames = Object.keys(vueApp._context.components || {});
    componentNames.forEach((name) => {
      if (name.startsWith('Svgo')) {
        app.component(name, vueApp._context.components[name]);
      }
    });
    isInitialized = true;
  }

  // 是否有正在显示的弹窗
  const isAnyDialogOpen = () => {
    return dialogReactiveListMap.size > 0;
  };

  // 队列
  const dialogQueue = ref<
    {
      content: Component | AsyncComponentLoader<Component>;
      options?: Partial<UseDialogOptions>;
      delay?: number;
    }[]
  >([]);

  const processNextDialog = () => {
    if (!isAnyDialogOpen() && dialogQueue.value.length > 0) {
      const nextDialog = dialogQueue.value.shift();
      if (nextDialog) {
        if (nextDialog.delay && nextDialog.delay > 0) {
          setTimeout(() => {
            _openDialog(nextDialog.content, nextDialog.options);
          }, nextDialog.delay);
        } else {
          _openDialog(nextDialog.content, nextDialog.options);
        }
      }
    }
  };

  const cleanupDialog = (k: string | null) => {
    if (k) {
      singleDialogMap.delete(k);
      dialogReactiveListMap.delete(k);
    }
  };

  watch(
    () => dialogReactiveListMap.size,
    (newSize) => {
      if (newSize === 0 && dialogQueue.value.length > 0) {
        processNextDialog();
      }
    },
  );

  const _openDialog = (
    content: Component | AsyncComponentLoader<Component> | null,
    options?: Partial<UseDialogOptions>,
  ) => {
    try {
      if (i18nInstance) {
        i18nInstance.global.locale.value = getLang() as any;
      }
      if (!content) return null;
      if (DialogUtils.isDialogOpen(content)) return null;
      const key = DialogUtils.generateKey(content);
      const component = DialogUtils.isAsyncComponent(content)
        ? markRaw(defineAsyncComponent(content))
        : markRaw(content);

      const mergedOptions = merge({}, DEFAULT_DIALOG_OPTIONS, options);
      // 给dialog增加默认边框
      mergedOptions.class =
        'border-[1.5px] border-[#323A51] ' + mergedOptions.class;

      const dialogInstance = baseDialog.info({
        content: () => {
          const contentProps = mergedOptions?.contentProps || {};
          const processedProps = Object.entries(contentProps).reduce(
            (acc, [key, value]) => {
              let processedValue = value;
              if (isRef(value)) {
                processedValue = value.value;
              } else if (isReactive(value)) {
                processedValue = toRaw(value);
              }
              return {
                ...acc,
                [key]: processedValue,
              };
            },
            {},
          );

          return createVNode(
            markRaw(ProvideComponent), // 注入dialogRef  const dialogRef = inject('dialogRef') as UseDialogOptions
            { dialogRef: dialogInstance },
            {
              default: () =>
                createVNode(Suspense, null, {
                  default: () =>
                    component
                      ? createVNode(
                          component,
                          processedProps,
                          mergedOptions?.slots || {},
                        )
                      : null,
                  fallback: () =>
                    createVNode(
                      'div',
                      { class: 'dialog-loading' },
                      'Loading...',
                    ),
                }),
            },
          );
        },
        onClose: () => {
          options?.onClose?.();
          cleanupDialog(key);
        },
        onAfterLeave: () => {
          options?.onAfterLeave?.();
          cleanupDialog(key);
          setTimeout(() => {
            processNextDialog();
          }, 300);
        },
        ...mergedOptions,
      });

      // 记录弹窗实例
      if (key) {
        const dialogData = {
          ...dialogInstance,
          time: Date.now(),
        };
        if (!mergedOptions.allowMultiple) {
          singleDialogMap.set(key, dialogData);
        }
        debounce(() => {
          dialogReactiveListMap.set(key, dialogData);
        }, 300)();
      }

      return dialogInstance;
    } catch (error) {
      processNextDialog();
      return null;
    }
  };

  /**
   * @param content 自定义组件或异步组件
   * @param options 配置项。参考 https://www.naiveui.com/zh-CN/os-theme/components/dialog
   */
  const open = (
    content: Component | AsyncComponentLoader<Component> | null,
    options?: Partial<UseDialogOptions>,
  ) => {
    const safeContent = content ? markRaw(content) : content;
    if (isAnyDialogOpen() && safeContent) {
      const queueItem = { content: safeContent, options };
      dialogQueue.value.push(queueItem);
      return {
        destroy: () => {
          const index = dialogQueue.value.findIndex(
            (item) => item === queueItem,
          );
          if (index !== -1) {
            dialogQueue.value.splice(index, 1);
            return true;
          }
          const key = DialogUtils.generateKey(safeContent);
          if (key && dialogReactiveListMap.has(key)) {
            return dialogReactiveListMap.get(key)?.destroy();
          }
          return () => destroyAppDialogs();
        },
        contentProps: options?.contentProps || {},
      };
    }

    return _openDialog(safeContent, options);
  };

  /**
   * 隐藏指定的弹窗实例
   * @param dialogInstance 弹窗实例
   */
  const close = (dialogInstance: DialogReactive | null) => {
    if (!dialogInstance) return;
    dialogInstance.destroy();
    Promise.resolve().then(() => {
      processNextDialog();
    });
  };

  /**
   * 隐藏指定 key 的弹窗
   * @param key 弹窗的 key
   */
  const hideByKey = (key?: string) => {
    if (key) {
      const dialog = dialogReactiveListMap.get(key);
      if (dialog) {
        dialog.destroy();
        dialogReactiveListMap.delete(key);
        singleDialogMap.delete(key);
      }
    } else {
      // 找到最近创建的弹窗
      let latestTime = 0;
      let latestKey = '';
      dialogReactiveListMap.forEach((dialog, k) => {
        if (dialog.time > latestTime) {
          latestTime = dialog.time;
          latestKey = k;
        }
      });

      if (latestKey) {
        const dialog = dialogReactiveListMap.get(latestKey);
        dialog?.destroy();
        dialogReactiveListMap.delete(latestKey);
        singleDialogMap.delete(latestKey);
      }
    }
  };

  return {
    open,
    close,
    hideByKey,
    isAnyDialogOpen,
    closeAll: () => destroyAppDialogs(),
    closeAllExcept: (keysToKeep: string[] = []) =>
      destroyAppDialogs(keysToKeep),
  };
}

export function destroyAppDialogs(keysToKeep: string[] = []) {
  for (const [key, dialog] of dialogReactiveListMap.entries()) {
    if (!keysToKeep.includes(key)) {
      dialog?.destroy();
      dialogReactiveListMap.delete(key);
    }
  }
}
