import { ref, computed } from 'vue';
import {
  BattleModesType,
  BattlePlayerModesType,
  GameModesType,
  type V1CasesItemWithQuantity,
} from '@/types/battles';

export const MAX_CASES = 50;

export function useBattleSelectedCases() {
  const caseSelections = ref<V1CasesItemWithQuantity[]>([]);
  const currentMode = ref<BattleModesType>(BattleModesType.REGULAR);
  const currentModePlayers = ref<BattlePlayerModesType>(
    BattlePlayerModesType['1v1'],
  );
  const isCreateBattleLoading = ref(false);
  const showInsufficientBalance = ref(false);
  const privateBattleEnabled = ref<BattleModesType>(BattleModesType.REGULAR);
  const unoModeEnabled = ref<GameModesType>(GameModesType.NORMAL); // 1-价高者胜;2-价低者胜
  const { battleApi } = useApi();

  const totalSelectedCases = computed(() =>
    caseSelections.value.reduce(
      (sum: number, item: V1CasesItemWithQuantity) => sum + item.quantity,
      0,
    ),
  );

  const totalValue = computed(() =>
    caseSelections.value.reduce((acc, cur) => {
      return BigNumberCalc.add(
        acc,
        BigNumberCalc.multiply(cur.total_price ?? '0', cur.quantity),
      );
    }, '0'),
  );

  function increment(item: V1CasesItemWithQuantity) {
    if (totalSelectedCases.value >= MAX_CASES) return;

    const index = caseSelections.value.findIndex(
      (i: V1CasesItemWithQuantity) => i.slug === item.slug,
    );
    if (index === -1) {
      caseSelections.value.push({ ...item, quantity: 1 });
    } else {
      caseSelections.value[index].quantity++;
    }
  }

  function decrement(item: V1CasesItemWithQuantity) {
    const index = caseSelections.value.findIndex(
      (i: V1CasesItemWithQuantity) => i.slug === item.slug,
    );
    if (index === -1) return;
    caseSelections.value[index].quantity--;
    if (caseSelections.value[index].quantity === 0) {
      caseSelections.value.splice(index, 1);
    }
  }

  function updateQuantity(
    item: V1CasesItemWithQuantity,
    quantity: number,
  ): void {
    const index = caseSelections.value.findIndex(
      (i: V1CasesItemWithQuantity) => i.slug === item.slug,
    );
    if (index === -1) return;

    const totalOtherCases = caseSelections.value.reduce(
      (sum: number, i: V1CasesItemWithQuantity) =>
        i.slug === item.slug ? sum : sum + i.quantity,
      0,
    );
    const maxAllowed = MAX_CASES - totalOtherCases;
    if (maxAllowed <= 0) return;
    quantity = Math.min(quantity, maxAllowed);

    if (quantity === 0) {
      caseSelections.value.splice(index, 1);
      return;
    }
    caseSelections.value[index].quantity = quantity;
  }

  async function createBattle(): Promise<{ arena_id: string } | boolean> {
    try {
      isCreateBattleLoading.value = true;
      const data = {
        battle_mode: privateBattleEnabled.value,
        game_mode: unoModeEnabled.value,
        player_number: currentModePlayers.value,
        total_price: totalValue.value,
        case_slug: caseSelections.value.map((item, index) => ({
          slug: item.slug,
          qty: item.quantity,
          csort: index,
        })) as V1CreateBattleCaseItem[],
      };
      const res = await battleApi.createBattle(data);
      if (res.data.value.code === 0) {
        return {
          arena_id: res.data.value.data.arena_id ?? '',
        };
      } else {
        // showInsufficientBalance.value = true;
        return false;
      }
    } catch (error) {
      // showInsufficientBalance.value = true;
      return false;
    } finally {
      isCreateBattleLoading.value = false;
    }
  }

  function reset() {
    caseSelections.value = [];
    currentMode.value = BattleModesType.REGULAR;
    currentModePlayers.value = BattlePlayerModesType['1v1'];
    privateBattleEnabled.value = BattleModesType.REGULAR;
    unoModeEnabled.value = GameModesType.NORMAL;
  }

  return {
    caseSelections,
    currentMode,
    currentModePlayers,
    isCreateBattleLoading,
    privateBattleEnabled,
    showInsufficientBalance,
    totalValue,
    unoModeEnabled,
    totalSelectedCases,
    MAX_CASES,
    increment,
    decrement,
    updateQuantity,
    createBattle,
    reset,
  };
}
