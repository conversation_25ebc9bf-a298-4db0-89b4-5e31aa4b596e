function colorValue(min: number) {
  return Math.floor(Math.random() * 255 + min);
}

function createColorStyle(r: number, g: number, b: number) {
  return 'rgba(' + r + ',' + g + ',' + b + ', 0.8)';
}

class Color {
  r: number;
  g: number;
  b: number;
  style: string;

  constructor(min = 0) {
    this.r = colorValue(min);
    this.g = colorValue(min);
    this.b = colorValue(min);
    this.style = createColorStyle(this.r, this.g, this.b);
  }
}
class Dot {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  color: Color;

  constructor(width: number, height: number) {
    this.x = Math.random() * width;
    this.y = Math.random() * height;
    this.vx = -0.5 + Math.random();
    this.vy = -0.5 + Math.random();
    this.radius = Math.random() * 2 + 0.5;
    this.color = new Color();
  }

  draw(ctx: CanvasRenderingContext2D) {
    ctx.beginPath();
    ctx.fillStyle = this.color.style;
    ctx.arc(this.x, this.y, this.radius, 0, 2 * Math.PI, false);
    ctx.fill();
  }
}

export function useLiveParticles(canvas: Ref<HTMLCanvasElement | null>) {
  const mousePosition = {
    x: 0,
    y: 0,
  };
  const mousemove = (e: MouseEvent) => {
    mousePosition.x = e.pageX;
    mousePosition.y = e.pageY;
  };
  onMounted(() => {
    if (!canvas.value) return;
    const color = new Color(150);
    const canvasEl = canvas.value;
    const width = (canvasEl.width = window.innerWidth);
    const height = (canvasEl.height = window.innerHeight);
    mousePosition.x = Math.floor(Math.random() * width);
    mousePosition.y = Math.floor(Math.random() * height);
    const ctx = canvasEl.getContext('2d') as CanvasRenderingContext2D;
    ctx.lineWidth = 0.3;
    ctx.strokeStyle = color.style;
    const screenScale = (window.innerWidth / 1600).toFixed(2);
    const nb = Number(screenScale) * 300;
    const dots: {
      nb: number;
      distance: number;
      d_radius: number;
      array: Dot[];
    } = {
      nb,
      distance: 100,
      d_radius: 150,
      array: [],
    };

    function mixComponents(
      comp1: number,
      weight1: number,
      comp2: number,
      weight2: number,
    ) {
      return (comp1 * weight1 + comp2 * weight2) / (weight1 + weight2);
    }

    function averageColorStyles(dot1: Dot, dot2: Dot) {
      const color1 = dot1.color;
      const color2 = dot2.color;
      const r = mixComponents(color1.r, dot1.radius, color2.r, dot2.radius);
      const g = mixComponents(color1.g, dot1.radius, color2.g, dot2.radius);
      const b = mixComponents(color1.b, dot1.radius, color2.b, dot2.radius);
      return createColorStyle(Math.floor(r), Math.floor(g), Math.floor(b));
    }

    function createDots() {
      for (let i = 0; i < dots.nb; i++) {
        dots.array.push(new Dot(width, height));
      }
    }

    function moveDots() {
      for (let i = 0; i < dots.nb; i++) {
        const dot = dots.array[i];
        if (dot.y < 0 || dot.y > height) {
          const vx = dot.vx;
          dot.vx = vx;
          dot.vy = -dot.vy;
        } else if (dot.x < 0 || dot.x > width) {
          const vy = dot.vy;
          dot.vx = -dot.vx;
          dot.vy = vy;
        }
        dot.x += dot.vx;
        dot.y += dot.vy;
      }
    }

    function connectDots() {
      for (let i = 0; i < dots.nb; i++) {
        for (let j = 0; j < dots.nb; j++) {
          const iDot = dots.array[i];
          const jDot = dots.array[j];

          if (
            iDot.x - jDot.x < dots.distance &&
            iDot.y - jDot.y < dots.distance &&
            iDot.x - jDot.x > -dots.distance &&
            iDot.y - jDot.y > -dots.distance
          ) {
            if (
              iDot.x - mousePosition.x < dots.d_radius &&
              iDot.y - mousePosition.y < dots.d_radius &&
              iDot.x - mousePosition.x > -dots.d_radius &&
              iDot.y - mousePosition.y > -dots.d_radius
            ) {
              ctx.beginPath();
              ctx.strokeStyle = averageColorStyles(iDot, jDot);
              ctx.moveTo(iDot.x, iDot.y);
              ctx.lineTo(jDot.x, jDot.y);
              ctx.stroke();
              ctx.closePath();
            }
          }
        }
      }
    }

    function drawDots() {
      for (let i = 0; i < dots.nb; i++) {
        const dot = dots.array[i] as Dot;
        dot.draw(ctx);
      }
    }
    function animateDots() {
      ctx.clearRect(0, 0, width, height);
      moveDots();
      connectDots();
      drawDots();
      requestAnimationFrame(animateDots);
    }
    createDots();
    requestAnimationFrame(animateDots);
  });
  return {
    mousemove,
  };
}
