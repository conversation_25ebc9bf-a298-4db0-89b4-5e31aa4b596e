export default function useCodeCheck(code: number, _res: ApiResponseData) {
  if (code < 301000 || code > 301013) {
    return true;
  }

  const dialogToast = useDialogToast();

  const msg = '';
  const title = '';
  switch (code) {
    default:
      break;
  }
  const dialogRef = dialogToast.openDialog({
    titleT: title,
    message: msg,
    contentProps: {
      btns: {
        confirmBtn: '我知道了',
      },
    },
    onClose: () => {},
    onConfirm: () => {
      dialogRef?.destroy();
    },
  });

  return false;
}
