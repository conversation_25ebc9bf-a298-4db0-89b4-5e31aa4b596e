import { debounce } from 'lodash-es';
import type { Ref } from 'vue';

// 预定义转换器
const transformers = {
  trim: (value: string) => value.trim(),
  number: (value: string) => Number(value),
  boolean: (value: any) => Boolean(value),
} as const;

// 预定义验证器
const validators = {
  required: (value: any) => value != null && value !== '',
  maxLength: (max: number) => (value: string) => value.length <= max,
  minLength: (min: number) => (value: string) => value.length >= min,
} as const;

// 配置参数
type FieldConfig<T> = {
  transform?: keyof typeof transformers | ((value: T[keyof T]) => T[keyof T]);
  validate?: keyof typeof validators | ((value: T[keyof T]) => boolean);
  validateOptions?: Record<string, any>;
  debounce?: number;
};

type FormConfig<T> = {
  [K in keyof T]?: FieldConfig<T>;
};

export function useFormUpdate<T extends Record<string, any>>(
  formState: T | Ref<T>,
  config?: FormConfig<T> | (() => void),
  globalOptions?: {
    debounceTime?: number;
    onUpdate?: () => void;
  },
) {
  if (typeof config === 'function') {
    globalOptions = { onUpdate: config };
    config = {};
  }

  const handleUpdate = (key: keyof T, value: T[keyof T]) => {
    try {
      const fieldConfig = config?.[key];

      // 处理转换
      let transformedValue = value;
      if (fieldConfig?.transform) {
        const transformer =
          typeof fieldConfig.transform === 'string'
            ? transformers[fieldConfig.transform]
            : fieldConfig.transform;
        transformedValue = transformer(value) as T[keyof T];
      }

      // 处理验证
      if (fieldConfig?.validate) {
        const validator =
          typeof fieldConfig.validate === 'string'
            ? validators[fieldConfig.validate]
            : fieldConfig.validate;

        const isValid = validator(transformedValue);
        if (!isValid) return;
      }

      // 更新状态
      if (isRef(formState)) {
        formState.value[key] = transformedValue;
      } else {
        formState[key] = transformedValue;
      }

      // 回调
      if (fieldConfig?.debounce) {
        debounce(globalOptions?.onUpdate || (() => {}), fieldConfig.debounce)();
      } else if (globalOptions?.debounceTime) {
        debounce(
          globalOptions?.onUpdate || (() => {}),
          globalOptions.debounceTime,
        )();
      } else {
        globalOptions?.onUpdate?.();
      }
    } catch (error) {
      console.error('Form update failed:', error);
    }
  };

  return {
    handleUpdate,
  };
}
