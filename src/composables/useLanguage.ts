import { loadRemoteDecorationLanguage, loadRemoteLanguage } from '~/i18n';
import type { Locale } from '~/vue-i18n';

export function useLanguage() {
  const { $i18n } = useNuxtApp();
  const i18n = $i18n;

  async function loadLanguage(lang: Locale) {
    try {
      const messages = await loadRemoteLanguage(lang);
      i18n.setLocaleMessage(lang, {
        ...i18n.getLocaleMessage(lang),
        ...messages,
      });
      return true;
    } catch (error) {
      console.error(`Failed to load language: ${lang}`, error);
      return false;
    }
  }

  async function loadDecorationLanguage(lang: Locale) {
    try {
      const messages = await loadRemoteDecorationLanguage(lang);
      i18n.setLocaleMessage(lang, {
        ...i18n.getLocaleMessage(lang),
        ...messages,
      });
      return true;
    } catch (error) {
      console.error(`Failed to load decoration language: ${lang}`, error);
      return false;
    }
  }

  return {
    currentLocale: i18n.locale,
    availableLocales: i18n.availableLocales,
    loadLanguage,
    loadDecorationLanguage,
  };
}
