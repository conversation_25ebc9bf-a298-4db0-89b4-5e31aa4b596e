import { isArray } from 'lodash-es';
import type { CaseSkinItemType } from '~/types/cases';

interface BroadcastUpdateParams {
  list: Ref<any[]> | any[]; // 需要更新的列表
  slug?: string; // 需要判断是否是当前箱子,当前箱子才更新
  valuable?: boolean | ComputedRef<boolean>; // 是否值只展示高价值
}
export function useBroadcastUpdate(opt: BroadcastUpdateParams) {
  const { list, slug, valuable } = opt;
  const waitUpdateItems = ref<CaseSkinItemType[]>([]);
  const hover = ref(false);
  let buffer: CaseSkinItemType[] = [];
  let timer: NodeJS.Timeout | null = null;
  // 填充items
  function setItem(goodItem?: CaseSkinItemType[]) {
    const item = goodItem || waitUpdateItems.value;
    const items = isArray(list) ? list : list.value;
    if (!item || !item.length) return;
    const pageSize = 15;
    // console.log(
    //   'valuable:',
    //   valuable || false,
    //   'record_id: ',
    //   item[0].record_id,
    // );
    items.unshift(item);
    if (items.length >= pageSize) {
      items.pop();
    }
    if (!goodItem) {
      waitUpdateItems.value = [];
    }
  }
  const update = (data: any) => {
    // 开箱记录
    if (!data.record_id) return;
    // 开箱页面,当前箱子并且开出饰品价格大于开箱价格
    if (
      slug &&
      !(
        data.case_info.slug === slug &&
        Number(data.win_amount) > Number(data.case_info.total_price)
      )
    )
      return;
    // 方法传递参数,是否需要只展示高价值,is_valuable字段判断
    const boolValuable = typeof valuable === 'boolean' && valuable === true;
    const refValuable =
      typeof valuable !== 'boolean' && valuable?.value === true;
    // 是否是高价值,通过字段判断
    if ((boolValuable || refValuable) && data.is_valuable !== 1) return;

    const goodItem = {
      record_id: `${data.record_id}_${Date.now()}`,
      slug: data.case_slug,
      ...data.user,
      ...data.goods,
      case: slug ? undefined : data.case_info,
      update: true,
    };
    // 如果鼠标划上lucky items 取消划上展示结果
    if (hover.value) {
      waitUpdateItems.value.unshift(goodItem);
    } else {
      buffer.push(goodItem);
      if (!timer) {
        timer = setTimeout(() => {
          const dataToSet = buffer.slice();
          buffer = [];
          timer = null;
          setItem(dataToSet);
        }, 500);
      }
    }
  };
  return {
    update,
    setItem,
    hover,
  };
}
