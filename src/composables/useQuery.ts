export default function useQuery() {
  const replaceUrl = (path: string, query: any, replace: boolean = true) => {
    const { cloned } = useCloned(query);
    for (const key in cloned.value) {
      if (Object.prototype.hasOwnProperty.call(cloned.value, key)) {
        if (
          Object.prototype.toString.call(cloned.value[key]) ===
            '[object Object]' &&
          cloned.value[key]
        ) {
          cloned.value[key] = encodeURI(JSON.stringify(cloned.value[key]));
        } else if (
          Object.prototype.toString.call(cloned.value[key]) ===
            '[object Array]' &&
          cloned.value[key].length
        ) {
          cloned.value[key] = cloned.value[key].join(',');
        } else {
          cloned.value[key] = query[key];
        }
      }
    }
    navigateTo({
      replace,
      path,
      query: cloned.value,
    });
  };

  const getRouteQuery = (key?: string) => {
    const route = useRoute();
    return key ? route.query[key] : route.query;
  };
  return {
    replaceUrl,
    getRouteQuery,
  };
}
