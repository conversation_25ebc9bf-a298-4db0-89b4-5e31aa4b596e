import Confirm from '~/components/dialog/Confirm.vue';

interface LiveMngOptions {
  title: string;
  class: string;
  content: VNode | string | (() => VNode);
  confirmText?: string;
  buttonClass?: string;
  onConfirm: () => void;
}

export function useLiveMngDialog() {
  const dialog = useAppDialog();
  const dialogInstance = ref();
  const openLiveMngDialog = (options: LiveMngOptions) => {
    const {
      title,
      class: className,
      content,
      onConfirm,
      confirmText = 'Confirm',
      buttonClass = 'flex-1 ',
    } = options;
    dialogInstance.value = dialog.open(Confirm, {
      class: `${className}  custom-dialog`,
      style: {
        background: '#fff',
        border: 'none',
      },
      contentProps: {
        title: h('span', { class: 'text-[#1D2129]' }, title),
        content:
          typeof content === 'string'
            ? h('div', { class: 'text-[#1D2129] text-center' }, content)
            : content,
        confirmBg: '#3b82f6',
        confirmText,
        cancelBg: '#3b82f6',
        cancelColor: '#3b82f6',
        buttonClass,
        cancelGhost: true,
        onConfirm,
      },
    });
  };
  const closeLiveMngDialog = () => {
    dialogInstance.value.destroy();
  };
  return { openLiveMngDialog, closeLiveMngDialog };
}
