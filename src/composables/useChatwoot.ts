import type {
  ChatwootSetUserProps,
  ChatwootToggleState,
  ChatwootVisibility,
} from '@/types/chatwoot';

const isLoadTimer = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    let loadNumber = 0;
    const timer = setInterval(() => {
      const data = document.querySelector('.woot-widget-holder');
      const widgetElm = document.querySelector('.woot--bubble-holder');
      loadNumber += 1;
      if (
        data &&
        window.chatwootSDK &&
        widgetElm &&
        window.$chatwoot &&
        window
      ) {
        clearInterval(timer);
        resolve('Chatwoot loaded');
      } else if (loadNumber === 200) {
        clearInterval(timer);
        reject(new Error('Chatwoot not loaded'));
      }
    }, 150);
  });
};

export const useChatWoot = () => {
  const observer = ref<MutationObserver | null>(null);
  const start = ref<number>(1);
  let timer: ReturnType<typeof setInterval>;
  const isModalVisible = ref<boolean>(false);

  function observerStart(data: Element | null): void {
    try {
      const callback: MutationCallback = (mutationList) => {
        for (const mutation of mutationList) {
          if (mutation.type === 'attributes') {
            const isHidden = (mutation.target as Element).className.includes(
              'hide',
            );
            isModalVisible.value = !isHidden;
          }
        }
      };
      if (data) {
        observer.value = new MutationObserver(callback);
        observer.value.observe(data, { attributes: true });
      }
    } catch (e) {
      console.error(e);
    }
  }

  onMounted(() => {
    timer = setInterval(() => {
      start.value += 1;
      const data = document.querySelector('.woot-widget-holder');
      if (data || start.value > 100) {
        clearInterval(timer);
        observerStart(data);
      }
    }, 100);
  });

  onBeforeUnmount(() => {
    if (observer.value) observer.value.disconnect();
  });

  const toggle = (state?: ChatwootToggleState): void => {
    isLoadTimer().then(() => window.$chatwoot?.toggle(state));
  };

  const setUser = (key: string, args: ChatwootSetUserProps): void => {
    isLoadTimer().then(() => window.$chatwoot?.setUser(key, args));
  };

  const setCustomAttributes = (attributes: Record<string, string>): void => {
    isLoadTimer().then(() => window.$chatwoot?.setCustomAttributes(attributes));
  };

  const deleteCustomAttribute = (key: string): void => {
    isLoadTimer().then(() => window.$chatwoot?.deleteCustomAttribute(key));
  };

  const setLocale = (locale: string): void => {
    isLoadTimer().then(() => window.$chatwoot?.setLocale(locale));
  };

  const setLabel = (label: string): void => {
    isLoadTimer().then(() => window.$chatwoot?.setLabel(label));
  };

  const removeLabel = (label: string): void => {
    isLoadTimer().then(() => window.$chatwoot?.removeLabel(label));
  };

  const reset = (): void => {
    isLoadTimer().then(() => window.$chatwoot?.reset());
  };

  const toggleBubbleVisibility = (visibility: ChatwootVisibility): void => {
    isLoadTimer().then(() => {
      window.$chatwoot?.toggleBubbleVisibility(visibility);
    });
  };

  const popoutChatWindow = (): void => {
    isLoadTimer().then(() => window.$chatwoot?.popoutChatWindow());
  };

  return {
    isModalVisible,
    toggle,
    setUser,
    setCustomAttributes,
    deleteCustomAttribute,
    setLocale,
    setLabel,
    removeLabel,
    reset,
    toggleBubbleVisibility,
    popoutChatWindow,
  };
};
