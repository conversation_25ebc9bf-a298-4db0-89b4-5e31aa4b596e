import { COOKIE_CONFIGS, COOKIE_TYPE_MAP } from '~/constants/cookieConfig';

export function useCookieManager() {
  const { getCurrentDomain } = useSiteName();

  /**
   * Cookie 配置
   * @param name
   * @returns 配置类型
   */
  const getCookieConfigType = (name: string): keyof typeof COOKIE_CONFIGS => {
    return (COOKIE_TYPE_MAP as any)[name] || 'AUTH';
  };

  /**
   * 设置 Cookie
   * @param name Cookie 名称
   * @param value Cookie 值
   * @param options Cookie 选项
   */
  const setCookie = (name: string, value: any, options?: any) => {
    const domain = getCurrentDomain();

    if (value === null || value === undefined) {
      deleteCookie(name, options);
    } else {
      const configType = getCookieConfigType(name);
      const baseConfig = COOKIE_CONFIGS[configType];
      const defaultOptions = {
        domain,
        ...baseConfig,
        secure: baseConfig.secure && import.meta.env.PROD,
      };

      // 正常设置 cookie
      const cookie = useCookie(name, { ...defaultOptions, ...options });
      cookie.value = value;
    }
  };

  /**
   * 删除 Cookie
   * @param name Cookie 名称
   * @param options Cookie 选项
   */
  const deleteCookie = (name: string, options?: any) => {
    const domain = getCurrentDomain();
    if (import.meta.client) {
      const expireDate = 'Thu, 01 Jan 1970 00:00:00 UTC';

      try {
        // 删除不包含域名的 Cookie
        document.cookie = `${name}=; expires=${expireDate}; path=/`;
        // 删除包含域名的 Cookie
        if (domain && domain !== 'localhost') {
          // 删除完整域名的 Cookie
          document.cookie = `${name}=; expires=${expireDate}; path=/; domain=${domain}`;
          // 删除点前缀域名的 Cookie
          document.cookie = `${name}=; expires=${expireDate}; path=/; domain=.${domain}`;
          // 子域名，删除主域名Cookie
          const parts = domain.split('.');
          if (parts.length > 2) {
            const mainDomain = parts.slice(-2).join('.');
            document.cookie = `${name}=; expires=${expireDate}; path=/; domain=${mainDomain}`;
            document.cookie = `${name}=; expires=${expireDate}; path=/; domain=.${mainDomain}`;
          }
        }
      } catch (error) {}
    } else {
      const cookie = useCookie(name, {
        domain,
        ...options,
      });
      cookie.value = null;
    }
  };

  /**
   * 获取 Cookie 值
   * @param name Cookie 名称
   * @returns Cookie 值
   */
  const getCookie = (name: string) => {
    return useCookie(name);
  };

  /**
   * 检查 Cookie 是否存在
   * @param name Cookie 名称
   * @returns 是否存在
   */
  const hasCookie = (name: string): boolean => {
    if (import.meta.server) {
      return false;
    }

    const cookie = getCookie(name);
    return (
      cookie.value !== null && cookie.value !== undefined && cookie.value !== ''
    );
  };

  /**
   * 清除多个 Cookie
   * @param names Cookie 名称数组
   * @param options Cookie 选项
   */
  const deleteCookies = (names: string[], options?: any) => {
    names.forEach((name) => deleteCookie(name, options));
  };

  /**
   * 批量设置 Cookie
   * @param cookieMap Cookie 键值对
   * @param options Cookie 选项
   */
  const setCookies = (cookieMap: Record<string, any>, options?: any) => {
    Object.entries(cookieMap).forEach(([key, value]) => {
      setCookie(key, value, options);
    });
  };

  return {
    setCookie,
    deleteCookie,
    getCookie,
    hasCookie,
    deleteCookies,
    setCookies,
  };
}
