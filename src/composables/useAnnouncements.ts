// 获取公告列表
import { useAppStore } from '@/stores/modules/app';

export const useAnnouncements = () => {
  const appStore = useAppStore();
  const error = ref<Error | null>(null);
  const isLoading = ref(false);

  const fetchAnnouncements = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      await appStore.getAnnouncements();
    } catch (e: any) {
      error.value = e instanceof Error ? e : new Error(String(e));
    } finally {
      isLoading.value = false;
    }
  };

  onMounted(() => {
    fetchAnnouncements();
  });

  const announcements = computed(() => appStore.announcements);

  return {
    announcements,
    isLoading,
    error,
    refresh: fetchAnnouncements,
  };
};
