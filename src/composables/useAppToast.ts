import merge from 'lodash-es/merge';
import { createDiscreteApi, type ConfigProviderProps } from 'naive-ui';

interface ToastOptions {
  content?: string;
  title?: string;
  duration?: number;
  closable?: boolean;
  description?: string;
  meta?: string;
}

const { themeOverrides } = useThemeOverrides();
const configProviderPropsRef = computed<ConfigProviderProps>(() => ({
  themeOverrides,
}));

const { notification } = createDiscreteApi(['notification'], {
  configProviderProps: configProviderPropsRef,
  notificationProviderProps: {
    placement: 'top',
    containerClass: '!top-[35%] toast-avatar',
  },
});

export default function useAppToast() {
  const defaultOptions: ToastOptions = { duration: 2500 };
  return {
    info: function (options?: ToastOptions) {
      const opts = merge(defaultOptions, options || {});
      notification.info(opts);
    },
    success: function (options?: ToastOptions) {
      const opts = merge(defaultOptions, options || {});
      notification.success(opts);
    },
    warn: function (options?: ToastOptions) {
      const opts = merge(defaultOptions, options || {});
      notification.warning(opts);
    },
    error: function (options?: ToastOptions) {
      const opts = merge(defaultOptions, options || {});
      notification.error(opts);
    },
  };
}
