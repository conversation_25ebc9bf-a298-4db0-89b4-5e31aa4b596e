import { createVNode, render } from 'vue';
import AppSpin from '~/components/AppSpin.vue';

/**
 * 全局遮罩
 */
export default function useAppSpin(props?: Record<string, any>) {
  let container: any = null;
  let vm: any = null;

  function destroy() {
    if (vm) {
      render(null, container);
      vm = null;
    }
    if (container.parentNode) {
      container.parentNode.removeChild(container);
    }
  }

  function open() {
    if (document.querySelector('.spin-nested-loading')) {
      return;
    }
    vm = createVNode(AppSpin, props);
    container = document.createElement('div');
    container.className = 'spin-nested-loading';
    render(vm, container);
    document.body.appendChild(container);
  }

  return {
    open,
    destroy,
  };
}
