import type { Router } from 'vue-router';

export interface Tab {
  title: string;
  value: string;
  route?: string;
  parent?: string;
  children?: string[] | Tab[];
  total_key?: string;
  event?: string;
  badge?: string;
  icon?: string;
  action_icon?: string;
  type?: 'group';
}

export const useTabRouteConfig = (tabs: Tab[]) => {
  const selectedTab = ref<string>(tabs[0].value);

  const handleRouteChange = (router: Router) => {
    const path = router.currentRoute.value.fullPath;
    const pathParts = path.split('/');
    const lastPart = pathParts[pathParts.length - 1];
    const parentPart = pathParts[pathParts.length - 2];

    // 匹配完整路由
    let matchedTab = tabs.find(
      (tab) => tab.route === `${parentPart}/${lastPart}`,
    );

    // 匹配子路由
    if (!matchedTab) {
      for (const tab of tabs) {
        const children = tab.children;
        if (!Array.isArray(children) || children.length === 0) continue;

        if (typeof children[0] === 'string') {
          if ((children as string[]).includes(lastPart)) {
            matchedTab = tab;
            break;
          }
        } else {
          const matchedChild = (children as Tab[]).find(
            (child) => child.value === lastPart,
          );
          if (matchedChild) {
            matchedTab = matchedChild; // ✅ 匹配子项
            break;
          }
        }
      }
    }

    // 匹配 parent
    if (!matchedTab) {
      matchedTab = tabs.find((tab) => tab.parent === lastPart);
    }

    // 匹配 route
    if (!matchedTab) {
      matchedTab = tabs.find((tab) => tab.route?.startsWith(parentPart));
    }

    // 匹配 value
    if (!matchedTab) {
      matchedTab = tabs.find((tab) => tab.value === lastPart);
    }
    return matchedTab?.value || tabs[0].value;
  };

  return {
    tabs,
    selectedTab,
    handleRouteChange,
  };
};
