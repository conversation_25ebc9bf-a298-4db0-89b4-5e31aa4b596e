import { TagColor } from '@/types/battles';

export function useCaseTags() {
  function getCaseTags(item: any, firstOnly: boolean = false) {
    const tags: Array<{ label: string; color: TagColor }> = [];

    // 如果没有tags属性或tags为空数组，返回空数组
    if (
      !item ||
      !item.tags ||
      (Array.isArray(item.tags) && item.tags.length === 0)
    ) {
      return [];
    }

    // 将tags对象转换为键值对数组
    const tagEntries = Object.entries(item.tags) as [string, string][];

    if (tagEntries.length === 0) {
      return [];
    }

    // 遍历所有标签
    tagEntries.forEach(([label, color]) => {
      const normalizedColor = color.toLowerCase();

      // 检查颜色值是否在预定义的颜色列表中
      if (Object.values(TagColor).includes(normalizedColor as TagColor)) {
        tags.push({
          label,
          color: normalizedColor as TagColor,
        });
      }
    });

    // 如果firstOnly为true，只返回第一个标签
    return firstOnly ? tags.slice(0, 1) : tags;
  }

  return {
    getCaseTags,
  };
}
