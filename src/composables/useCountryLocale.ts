import pkg from 'i18n-iso-countries';
import {
  getI18nCountriesLocale,
  registerCountryLocale,
  initializeCommonLocales,
  isLocaleRegistered,
  getRegisteredLocales,
} from '~/utils/countryLocales';

// 确保只初始化一次
let isInitialized = false;

const { getName, getAlpha2Codes } = pkg;

// 同步加载语言包
const loadCountryLocale = (localeCode: string): boolean => {
  const result = registerCountryLocale(localeCode);
  return result;
};

// 根据当前语言获取国家名称
const getCountryName = (code: string, locale: string): string => {
  const mappedLocale = getI18nCountriesLocale(locale);
  try {
    const name = getName(code, mappedLocale) || getName(code, 'en') || code;
    return name;
  } catch (error) {
    console.warn(
      `Failed to get country name for ${code} in locale ${mappedLocale}:`,
      error,
    );
    return getName(code, 'en') || code;
  }
};

// 获取所有国家选项
const getCountryOptions = (locale: string) => {
  try {
    // 确保语言包已加载
    if (!isLocaleRegistered(locale)) {
      loadCountryLocale(locale);
    }

    // 获取所有国家代码生成选项
    const countryCodes = getAlpha2Codes();
    const options = Object.keys(countryCodes)
      .map((code) => ({
        label: getCountryName(code, locale),
        value: code,
      }))
      .filter((option) => option.label && option.label !== option.value) // 过滤掉没有翻译的选项
      .sort((a, b) => a.label.localeCompare(b.label));
    return options;
  } catch (error) {
    console.error('Failed to get country options:', error);
    return [];
  }
};

export function useCountryLocale() {
  // 初始化一次常用语言包
  if (!isInitialized) {
    initializeCommonLocales();
    isInitialized = true;
  }

  return {
    loadCountryLocale,
    getCountryName,
    getCountryOptions,
    getI18nCountriesLocale,
    initializeCommonLocales,
    isLocaleRegistered,
    getRegisteredLocales,
  };
}
