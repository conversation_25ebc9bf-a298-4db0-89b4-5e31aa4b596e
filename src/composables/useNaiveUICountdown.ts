import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import type { CountdownProps } from 'naive-ui';
import type { DurationType } from '~/types/rollroom';
// eslint-disable-next-line import/no-named-as-default-member
dayjs.extend(utc);
export default function useNaiveUICountdown(endTime?: string) {
  const time = ref<DurationType>({
    d: '00',
    h: '00',
    m: '00',
    s: '00',
  });
  const duration = computed(() => {
    if (!endTime) return 0;
    const end = dayjs.utc(endTime);
    const cur = dayjs.utc();
    const diff = end.diff(cur);
    return diff >= 0 ? diff : 0;
  });

  // 倒计时dom
  const renderCountDown: CountdownProps['render'] = ({
    hours,
    minutes,
    seconds,
  }: {
    hours: number;
    minutes: number;
    seconds: number;
    milliseconds: number;
  }) => {
    const d = String(Math.floor(hours / 24)).padStart(2, '0');
    const h = String(hours % 24).padStart(2, '0');
    const m = String(minutes).padStart(2, '0');
    const s = String(seconds).padStart(2, '0');
    time.value = {
      d,
      h,
      m,
      s,
    };
    return `${d}d ${h}h ${m}m ${s}s`;
  };
  return { time, duration, renderCountDown };
}
