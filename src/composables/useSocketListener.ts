import { debounce } from 'lodash-es';
import { useAppStore } from '~/stores/modules/app';
import { useSocketStore } from '~/stores/modules/socket';

interface BattleEventHandlers {
  onCreate?: (data: any) => void;
  onJoin?: (data: any) => void;
  onCancel?: (data: any) => void;
  onRunning?: (data: any) => void;
  onNewRound?: (data: any) => void;
  onLeave?: (data: any) => void;
  onFinish?: (data: any) => void;
  onInit?: (data: any) => void;
}
interface CasesEventHandlers {
  onRecord?: (data: any) => void;
}
interface CoinflipEventHandlers {
  onCreate?: (data: any) => void;
  onResult?: (data: any) => void;
  onCancel?: (data: any) => void;
  onChallenge?: (data: any) => void;
}
interface NotificationEventHandlers {
  onBalance?: (data: any) => void;
}

const eventMap = {
  onCreate: 'create',
  onJoin: 'join_battle',
  onCancel: 'cancel',
  onRunning: 'running',
  onNewRound: 'new_round',
  onLeave: 'leave',
  onFinish: 'finish',
  onInit: 'init',
  // cases
  onRecord: 'record',
  // coinflip
  onResult: 'result',
  onChallenge: 'challenge',
  // 钱包金额
  onBalance: 'balance',
};

export function useSocketListener(
  namespace: string,
  handlers: BattleEventHandlers &
    CasesEventHandlers &
    CoinflipEventHandlers &
    NotificationEventHandlers,
) {
  const socketStore = useSocketStore();
  const appStore = useAppStore();
  const socket = computed(() => socketStore.socket[namespace]);

  const registerEvents = () => {
    if (!socket.value) return;
    Object.entries(handlers).forEach(([key, handler]) => {
      const eventName = eventMap[key as keyof typeof eventMap];
      if (eventName && handler) {
        socket.value.on(eventName, handler);
      }
    });
  };

  const removeEvents = () => {
    if (!socket.value) return;

    Object.entries(handlers).forEach(([key, handler]) => {
      const eventName = eventMap[key as keyof typeof eventMap];
      if (eventName && handler) {
        socket.value.off(eventName, handler);
      }
    });
  };

  const emit = (eventName: string, data?: any) => {
    if (!socket.value) {
      console.warn(`[Socket] ${namespace} not connected`);
      return;
    }
    socket.value.emit(eventName, data);
  };

  const subscribeNamespace = debounce(async () => {
    if (
      !socket.value?.connected &&
      (socket.value as any)?.nsp !== `/${namespace}`
    ) {
      await socketStore.subscribeSocket(namespace);
    }
    registerEvents();
  }, 300);

  const removeListener = () => {
    removeEvents();
    socketStore.unSubscribeSocket(namespace);
  };

  const reSubscribe = () => {
    removeListener();
    subscribeNamespace();
  };

  onMounted(() => {
    const restrictIPName = ['cases', 'coinflip', 'casebattle'];
    const ipPermission = appStore.ipPermission;
    if (restrictIPName.includes(namespace) && !ipPermission) return;
    // 检查socket是否已存在且已连接
    subscribeNamespace();
  });

  onBeforeUnmount(() => {
    removeListener();
  });

  return {
    registerEvents,
    removeEvents,
    emit,
    removeListener,
    reSubscribe,
  };
}
