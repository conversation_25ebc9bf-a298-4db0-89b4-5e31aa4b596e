export const useBalanceNotEnoughDialog = () => {
  const { openConfirm } = useDialogPromptsConfirm('balanceNotEnoughDialog');

  const router = useRouter();
  const { $i18n } = useNuxtApp();
  const { t } = $i18n;
  openConfirm({
    title: t('insufficient_balance'),
    content: t('recharge_confirmation'),
    confirmText: t('deposit'),
    enablePrompts: true,
    onConfirm: () => {
      router.push('/profile/deposit');
    },
  });
};
