import { ref, inject } from 'vue';

export function useDepositPolling() {
  const rechargeNo = ref<string>('');
  const loading = ref(false);

  const { depositApi } = useApi();
  const { t } = useI18n();
  const toast = useAppToast();
  const dialog = useAppDialog();
  const dialogRef = inject('dialogRef') as UseDialogOptions;

  // 充值查询
  const handleDepositQuery = async () => {
    if (!rechargeNo.value) {
      stopPolling();
      return;
    }

    try {
      const res = await depositApi.getDepositByRechargeNo(rechargeNo.value);
      if (res.data.value.code === 0) {
        const status = res.data.value.data.recharge_status;
        // 1-充值/提现中;2-充值/提现成功;3-充值/提现失败
        if (status === 2) {
          toast.success({
            content: t('deposit_success', 'Deposit successful'),
          });
          stopPolling();
          dialogRef.onConfirm?.(true);
          dialog.hideByKey();
          loading.value = false;
        }
        if (status === 3) {
          toast.error({
            content: t('deposit_failed', 'Deposit failed'),
          });
          stopPolling();
          loading.value = false;
        }
      } else {
        stopPolling();
        loading.value = false;
      }
    } catch (error) {
      console.error('Deposit query error:', error);
      stopPolling();
      loading.value = false;
    }
  };

  const { start: startPolling, stop: stopPolling } = usePolling(
    handleDepositQuery,
    1000,
  );

  const startDepositProcess = (rechargeNumber: string) => {
    rechargeNo.value = rechargeNumber;
    loading.value = true;
    startPolling();
  };

  const stopDepositProcess = () => {
    stopPolling();
    loading.value = false;
    rechargeNo.value = '';
  };

  return {
    rechargeNo,
    loading,
    startPolling,
    stopPolling,
    handleDepositQuery,
    startDepositProcess,
    stopDepositProcess,
  };
}
