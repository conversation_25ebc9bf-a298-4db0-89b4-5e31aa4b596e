import { ref } from 'vue';
import dayjs from 'dayjs';
import {
  BattleStatusType,
  type BattleItem,
  type Player,
} from '@/types/battles';

interface BattlesData {
  battles?: BattleItem[];
  cases?: Record<string, any>;
}

export function useBattleList() {
  const { battleApi } = useApi();
  const allBattlesData = ref<BattlesData>({});
  const dismissedBattles = ref<string[]>([]);
  const hiddenBattles = ref<string[]>([]);
  const totalBattlesCount = ref(0);

  function setBattleInitialList(battleData: V1GetBattleListReply) {
    // 如果没有战斗数据，直接返回
    if (!battleData) return;
    const battles = battleData.list?.map((battle) => {
      // 创建玩家数组，初始化为undefined
      const players = Array(battle?.battle_info?.player_number || 0).fill(
        undefined,
      ) as any;

      battle.players?.forEach((player: V1BattlePlayer) => {
        players[player.position! - 1] = {
          ...player,
          ...(battleData?.user?.find((u: any) => u.uid === player.uid) ?? {}),
        };
      });

      return {
        ...battle,
        ...battle.battle_info,
        players,
      };
    });

    const cases = battleData.cases?.reduce(
      (acc, cur) => {
        if (cur && cur.slug) {
          acc[cur.slug] = cur;
        }
        return acc;
      },
      {} as Record<string, any>,
    );

    const uniqueBattles = new Map();

    battles?.forEach((battle) => {
      uniqueBattles.set(battle.arena_id, battle);
    });

    // allBattlesData.value.battles?.forEach((battle) => {
    //   uniqueBattles.set(battle.arena_id, battle);
    // });

    return {
      battles: Array.from(uniqueBattles.values()),
      cases: {
        // ...allBattlesData.value.cases,
        ...cases,
      },
    };
  }

  // 根据 slug 查找对战索引
  const findBattleIndex = (arenaId: string): number => {
    const index = allBattlesData.value.battles?.findIndex(
      (battle) => battle.arena_id === arenaId,
    );
    return typeof index === 'undefined' ? -1 : index;
  };

  // 取消对战
  const cancelBattle = (arenaId: string) => {
    const index = findBattleIndex(arenaId);
    if (index < 0) return;
    if (!allBattlesData.value.battles) return;
    allBattlesData.value.battles[index].status = BattleStatusType.CANCELLED;
    allBattlesData.value.battles[index].ended_at = dayjs(
      getLocalTimeAsUTCTimestamp(),
    ).format('YYYY-MM-DD HH:mm:ss');
    // allBattlesData.value.battles.splice(index, 1);
  };

  // 创建新对战
  const createBattle = (battleData: any) => {
    const newBattlesData: any = {
      ...battleData,
      cases: battleData.case_info,
      list: [
        {
          ...battleData,
          battle_info: {
            ...battleData.battle_info,
            started_at: dayjs(getLocalTimeAsUTCTimestamp()).format(
              'YYYY-MM-DD HH:mm:ss',
            ),
          },
          players: [battleData.players],
        },
      ],
      user: [battleData.user],
      records: [],
      tiebreaker: [],
    };
    const data = setBattleInitialList(newBattlesData) as any;
    allBattlesData.value.battles?.unshift(...data.battles);
    allBattlesData.value.cases = {
      ...allBattlesData.value.cases,
      ...data.cases,
    };
  };

  // 开始运行对战
  const startBattle = (arenaId: string) => {
    const index = findBattleIndex(arenaId);
    if (index < 0) return;
    if (!allBattlesData.value.battles) return;
    allBattlesData.value.battles[index].status = BattleStatusType.RUNNING;
    allBattlesData.value.battles[index].active_round = 0;
  };

  // 更新对战玩家信息
  const fetchAndUpdateBattlePlayers = async (
    arenaId: string,
  ): Promise<boolean> => {
    const index = findBattleIndex(arenaId);
    if (
      index < 0 ||
      !allBattlesData.value.battles ||
      allBattlesData.value.battles[index].players.every(Boolean)
    ) {
      return false;
    }

    const res = await battleApi.getBattleInfo({
      arena_id: arenaId,
    });

    if (res.data.value.code === 0) {
      const data = res.data.value.data;
      if (data?.players?.length) {
        const battle = allBattlesData.value.battles[index];
        if (!battle.players) {
          battle.players = [];
        }

        // 更新玩家信息
        data.players.forEach((player: any) => {
          if (player?.position) {
            const playerIndex = player.position - 1;
            while (battle.players.length <= playerIndex) {
              battle.players.push(undefined);
            }
            battle.players[playerIndex] = {
              ...player,
              ...(data?.user?.find((u: any) => u.uid === player.uid) ?? {}),
            };
          }
        });
        return true;
      }
    }
    return false;
  };

  // 新回合
  const startNewRound = (arenaId: string, roundNumber: number) => {
    // 更新玩家信息
    fetchAndUpdateBattlePlayers(arenaId);

    const index = findBattleIndex(arenaId);
    if (index < 0) return;
    if (!allBattlesData.value.battles) return;
    if (
      allBattlesData.value.battles[index].status !== BattleStatusType.RUNNING
    ) {
      allBattlesData.value.battles[index].status = BattleStatusType.RUNNING;
    }
    allBattlesData.value.battles[index].active_round = roundNumber;
  };

  // 加入对战
  const joinBattle = (arenaId: string, player: Player) => {
    const index = findBattleIndex(arenaId);
    if (index < 0 || !allBattlesData.value.battles) return;

    const battle = allBattlesData.value.battles[index];
    if (!battle || !player?.position) return;

    const playerIndex = player.position - 1;
    if (!battle.players) {
      battle.players = [];
    }

    while (battle.players.length <= playerIndex) {
      battle.players.push(undefined);
    }

    battle.players[playerIndex] = player;
  };

  // 离开对战
  const leaveBattle = (arenaId: string, position: number) => {
    const index = findBattleIndex(arenaId);
    if (index < 0 || !allBattlesData.value.battles) return;

    const battle = allBattlesData.value.battles[index];
    if (!battle || !battle.players) return;

    if (position > 0 && position <= battle.players.length) {
      battle.players[position - 1] = undefined;
    }
  };

  // 结束对战
  const finishBattle = (arenaId: string, winners: string, endTime?: string) => {
    const index = findBattleIndex(arenaId);
    if (index < 0) return;
    if (!allBattlesData.value.battles) return;
    allBattlesData.value.battles[index].status = BattleStatusType.FINISHED;
    allBattlesData.value.battles[index].winners = winners;
    allBattlesData.value.battles[index].ended_at = endTime;
  };

  // 忽略对战
  const dismissBattle = (arenaId: string) => {
    if (!dismissedBattles.value.includes(arenaId)) {
      dismissedBattles.value.push(arenaId);
    }
  };

  const hideBattle = (arenaId: string) => {
    if (!hiddenBattles.value.includes(arenaId)) {
      hiddenBattles.value.push(arenaId);
    }
  };

  const setTotalBattlesCount = (count: number) => {
    totalBattlesCount.value = count;
  };

  // 清空对战
  const clearBattle = () => {
    allBattlesData.value = {};
    dismissedBattles.value = [];
    hiddenBattles.value = [];
  };
  return {
    setBattleInitialList,
    allBattlesData,
    dismissedBattles,
    hiddenBattles,
    totalBattlesCount,
    findBattleIndex,
    cancelBattle,
    createBattle,
    startBattle,
    startNewRound,
    joinBattle,
    leaveBattle,
    finishBattle,
    dismissBattle,
    hideBattle,
    clearBattle,
    setTotalBattlesCount,
  };
}
