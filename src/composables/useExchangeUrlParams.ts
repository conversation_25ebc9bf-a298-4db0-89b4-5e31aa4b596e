import type { RouteLocation } from 'vue-router';
import { useExchangeStore } from '~/stores/modules/exchange';

const { replaceUrl } = useQuery();

export default function useExchangeUrlParams() {
  const exchangeStore = useExchangeStore();
  const valueToIdMap = computed(() => exchangeStore.exchangeFilterValueMap);
  const idToValueMap = computed(() => exchangeStore.exchangeFilterIdMap);
  const categories = computed(() => exchangeStore.categories);
  const route = useRoute();

  const getRouteQuery: () => RouteLocation['query'] = () => {
    return route.query;
  };

  const setRouteQuery = (
    path?: string,
    params?: any,
    replace: boolean = true,
  ) => {
    replaceUrl(path ?? '/', params, replace);
  };

  const flattenData = (data: any) => {
    const result: any[] = [];

    function processEntry(entry: any) {
      result.push(entry);
      if (entry.list && Array.isArray(entry.list)) {
        entry.list.forEach((innerEntry: any) => processEntry(innerEntry));
      }
    }

    data.forEach((entry: any) => processEntry(entry));
    return result;
  };

  /** 生成顶部饰品筛选 */
  const getGategoryData = (query: Record<string, any>) => {
    const queryCategory: any = [];
    const category = [
      ...((query?.category_group as string)?.split(',') ?? []),
      ...((query?.category as string)?.split(',') ?? []),
      ...((query?.capsule_group as string)?.split(',') ?? []),
    ];
    flattenData(categories.value)?.reduce((acc: string[], cur: any) => {
      if (category.includes(`${cur.id ?? 0}`)) {
        acc.push(cur);
      }
      return acc;
    }, queryCategory);
    return queryCategory;
  };

  const convertIdsToValues = (parsedQuery: any) => {
    const result = {} as any;
    /* eslint-disable prefer-const */
    for (let [key, ids] of Object.entries(parsedQuery) as any) {
      if (ids.includes(',')) {
        ids = ids.split(',');
      }
      if (!Array.isArray(ids)) {
        ids = [ids];
      }
      const values = ids
        .map((id: any) => idToValueMap.value.get(parseInt(id)))
        .filter((value: any) => value !== undefined);
      result[key] = values;
    }

    return result;
  };

  const convertValuesToIds = (parsedQuery: any) => {
    const result = {} as any;
    for (let [key, values] of Object.entries(parsedQuery) as any) {
      if (!Array.isArray(values)) {
        values = [values];
      }
      const ids = values
        .map((value: any) => valueToIdMap.value.get(value))
        .filter((id: any) => id !== undefined);
      result[key] = key === 'category_group' ? [ids[0][0]] : ids;
    }
    return result;
  };

  const initFilterParamsData = (allowedKeys: string[] = []) => {
    let originalQuery = getRouteQuery();
    // 过滤原始查询对象，只保留允许的键
    if (allowedKeys.length) {
      originalQuery = Object.keys(originalQuery)
        .filter((key) => allowedKeys.includes(key))
        .reduce((obj: any, key) => {
          obj[key] = originalQuery[key];
          return obj;
        }, {});
    }
    /* eslint-disable camelcase */
    const { exterior, category_select, convertible, sort, ...query } =
      originalQuery;
    // 解析并转换 URL 参数
    const params = convertIdsToValues(query);

    // 构造返回对象
    let filterParams = {
      originalQuery,
      exterior: exterior || '',
      category_select: category_select || '',
      convertible: convertible ? convertible === 'true' : null,
      sort: sort || '',
      ...params,
    };

    return filterParams;
  };

  return {
    flattenData,
    getRouteQuery,
    setRouteQuery,
    getGategoryData,
    initFilterParamsData,
    convertValuesToIds,
    convertIdsToValues,
  };
}
