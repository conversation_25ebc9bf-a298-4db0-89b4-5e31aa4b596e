import { ref } from 'vue';
import { useTimeoutFn } from '@vueuse/core';
import { useIsNarrowScreen } from './useIsNarrowScreen';

/**
 * 控制自动滚动行为
 * @param preventRestart 是否阻止自动重启滚动
 */
export function useCasesAutoScrollControl(preventRestart = false) {
  const isAutoScrollEnabled = ref(true);
  const isDragging = ref(false);
  const isNarrowScreen = useIsNarrowScreen();

  // 4秒后重新启用自动滚动
  const { start: startTimer, stop: stopTimer } = useTimeoutFn(
    () => {
      isAutoScrollEnabled.value = true;
    },
    4000,
    { immediate: false },
  );

  const disableAutoScroll = () => {
    isAutoScrollEnabled.value = false;
    isDragging.value = true;
  };

  const enableAutoScroll = () => {
    // 如果设置了阻止重启且当前在窄屏幕上正在拖动，则不重启自动滚动
    if (!preventRestart || (preventRestart && !isNarrowScreen.value)) {
      stopTimer();
      startTimer();
    }
    isDragging.value = false;
  };

  return {
    isAutoScrollEnabled,
    disableAutoScroll,
    enableAutoScroll,
    isDragging,
  };
}
