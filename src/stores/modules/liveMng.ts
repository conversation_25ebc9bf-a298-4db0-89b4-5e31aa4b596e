import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useSettingStore } from './setting';
import { StreamerType, type PermissionModel } from '~/types/liveMng';
import { STORAGE_KEYS } from '~/constants';

interface PermissionInfo {
  streamer_type: StreamerType;
  guild_name: string;
  guild_leader: string;
  guild_permission: PermissionModel | null;
}
interface SetPermissionParams {
  streamer_uid: string;
  guild_permission: PermissionModel;
}
interface GuildStreamer {
  streamer_uid: string;
  remark: PermissionModel;
}

export const useLiveMngStore = defineStore('liveMng', () => {
  const { liveMngApi } = useApi();
  const settingStore = useSettingStore();
  const permissionInfo = ref<PermissionInfo>();
  const isPersona = ref(false);
  const isMaster = ref(false);
  const toast = useAppToast();
  const getStreamerPermission = async (server: boolean = false) => {
    const userAuth = useCookie(STORAGE_KEYS.COOKIES.TOKEN);
    if (
      (server && permissionInfo.value) ||
      (import.meta.server && !userAuth.value)
    )
      return;
    try {
      const { data: res } = await liveMngApi.getStreamerPermission(server);
      permissionInfo.value = res.value.data;
      if (permissionInfo.value?.streamer_type === StreamerType.PERSONAL) {
        isPersona.value = true;
      }
      if (permissionInfo.value?.streamer_type === StreamerType.MASTER) {
        isMaster.value = true;
      }
    } catch (error) {
      console.error('获取信息失败:', error);
    }
  };

  // 将其他人踢出公会
  const removeStreamerGuildLeave = async (uid: string) => {
    const { data: res } = await liveMngApi.removeStreamerGuildLeave({
      streamer_uid: uid,
    });
    if (res.value.code === 0) {
      toast.success({ content: 'operate successfully' });
      return true;
    }
  };
  // 退出公会
  const leaveGuild = async () => {
    const uid = settingStore.userInfo.uid;
    const success = await removeStreamerGuildLeave(uid);
    if (success) {
      settingStore.clearUserInfo();
      location.href = '/live-mng/login';
    }
  };
  // 修改直播权限
  const setStreamerPermission = async (params: SetPermissionParams) => {
    const { data: res } = await liveMngApi.setStreamerPermissionSave(params);
    if (res.value.code === 0) {
      toast.success({ content: 'operate successfully' });
    }
  };
  // 修改主播信息
  const setStreamerRemark = async (params: GuildStreamer) => {
    const { data: res } = await liveMngApi.setStreamerGuildStreamerSave(params);
    if (res.value.code === 0) {
      toast.success({ content: 'operate successfully' });
    }
  };

  // 添加成员
  const addStreamerGuildStreamer = async (params: GuildStreamer) => {
    const { data: res } = await liveMngApi.addStreamerGuildStreamer(params);
    if (res.value.code === 0) {
      toast.success({ content: 'operate successfully' });
    }
  };

  return {
    permissionInfo,
    getStreamerPermission,
    setStreamerPermission,
    setStreamerRemark,
    removeStreamerGuildLeave,
    addStreamerGuildStreamer,
    leaveGuild,
    isPersona,
    isMaster,
  };
});
