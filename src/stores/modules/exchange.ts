import { defineStore } from 'pinia';
import ExchangeSuccess from '~/components/dialog/ExchangeSuccess.vue';

const DEFAULT_CATEGORIES: any[] = [
  {
    title: 'all',
    icon_url: '',
    value: 'all',
  },
];

const DEFAULT_EXTERIOR: any[] = [
  {
    title: 'all',
    value: '',
  },
];

const DEFAULT_QUALITY: any[] = [
  {
    title: 'all',
    value: '',
  },
];

const getCategoriesAttributes = (categories: any) => {
  return [
    ...new Set(
      categories.reduce(
        (accumulator: string[], filter: { list: any[]; attribute: string }) => {
          if (filter.list && filter.list.length > 0) {
            filter.list.forEach((list: any) => {
              accumulator.push(list.attribute);
            });
          } else {
            accumulator.push(filter.attribute);
          }
          return accumulator;
        },
        [],
      ),
    ),
  ] as string[];
};

function createValueIdMapping(data: any) {
  const idToValueMap = new Map();
  const valueToIdMap = new Map();
  function traverseAndMap(obj: Record<string, any>) {
    if (Array.isArray(obj)) {
      obj.forEach((item) => {
        if (item.id && item.value) {
          idToValueMap.set(item.id, item.value);
          if (valueToIdMap.get(item.value)) {
            valueToIdMap.get(item.value).push(item.id);
          } else {
            valueToIdMap.set(item.value, [item.id]);
          }
        }
        traverseAndMap(item);
      });
    } else if (typeof obj === 'object' && obj !== null) {
      Object.values(obj).forEach((value) => {
        traverseAndMap(value);
      });
    }
  }

  traverseAndMap(data);
  return { idToValueMap, valueToIdMap };
}

export const useExchangeStore = defineStore('exchange', () => {
  const dialog = useAppDialog();
  const { exchangeApi } = useApi();
  const dialogRef = ref();
  const exchangeFilterIdMap = ref(new Map());
  const exchangeFilterValueMap = ref(new Map());
  const categoriesAttributes = ref<string[]>([]);
  const exchangeFilterData = reactive({
    categories: [] as V1GetGoodsFilterReply['categories'], // 所有分类
    exterior: [] as V1GetGoodsFilterReply['exterior'], // 外观
    quality: [] as V1GetGoodsFilterReply['quality'], // 品质
    defaultCategories: DEFAULT_CATEGORIES,
  });
  const categories = ref<any[]>([]);

  const getExchangeFilterData = async () => {
    if (categories.value.length) return;
    try {
      const { data: req } = await exchangeApi.getHotExchangeFilter();
      if (req?.value?.data) {
        const data = req.value.data;
        exchangeFilterData.categories = DEFAULT_CATEGORIES.concat(
          data.categories as any,
        );
        exchangeFilterData.exterior = DEFAULT_EXTERIOR.concat(
          data.exterior as any,
        );
        exchangeFilterData.quality = DEFAULT_QUALITY.concat(
          data.quality as any,
        );
        const { idToValueMap, valueToIdMap } = createValueIdMapping(
          req?.value?.data,
        );
        exchangeFilterIdMap.value = idToValueMap;
        exchangeFilterValueMap.value = valueToIdMap;
        categoriesAttributes.value = getCategoriesAttributes(data.categories);
      }
    } catch (error) {}
  };
  const cdkRedeem = (component: Component) => {
    dialogRef.value = dialog.open(component, {
      style: { background: '#151a29', width: '436px' },
      contentProps: {
        onConfirm: async (data: any) => {
          const { data: res } = await exchangeApi.cdkRedeem(data);
          if (res.value.code !== 0) return;
          const goodsList = res.value.data.goods_list;
          const coinAmount = res.value.data.coin_amount;
          await dialogRef.value?.destroy();
          dialogRef.value = dialog.open(ExchangeSuccess, {
            style: { background: '#151a29', width: '568px' },
            contentProps: {
              goodsList,
              coinAmount,
              onConfirm: () => {
                dialogRef.value?.destroy();
              },
            },
          });
        },
      },
    });
  };
  return {
    ...toRefs(exchangeFilterData),
    categoriesAttributes,
    exchangeFilterIdMap,
    exchangeFilterValueMap,
    getExchangeFilterData,
    cdkRedeem,
  };
});
