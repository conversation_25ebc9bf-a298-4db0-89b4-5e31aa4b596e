import { Socket, Manager } from 'socket.io-client';
import { defineStore } from 'pinia';
import { useSettingStore } from './setting';
import { useAppStore } from './app';

export const useSocketStore = defineStore('socket', () => {
  const appStore = useAppStore();
  const settingStore = useSettingStore();
  const manager = ref<Manager | null>(null);
  const { BASE_URL: baseUrl } = useRuntimeConfig().public;
  const socket = ref<{
    [key: string]: Socket;
  }>({});
  const uid = computed(() => settingStore.userInfo?.uid ?? 'guest');
  // 建立连接
  const connect = () => {
    const token = appStore.metadata?.socket_token;
    manager.value = new Manager(`${baseUrl}/`, {
      transports: ['websocket'],
      query: {
        ...{ uid: uid.value },
        ...(token && { token }),
      },
      reconnectionAttempts: 3,
      path: '/wss',
      autoConnect: true,
    });
  };
  // 验证订阅
  const initNameSp = async (name: string, identifying: boolean) => {
    if (!identifying) {
      const nowTime = new Date().getTime();
      const tokenTime = appStore.metadata?.time || 0;
      // token存在超过30s,需要重新获取
      if (nowTime - tokenTime >= 28 * 1000) {
        await appStore.getMetadata();
      }
      const authorizationToken = appStore.metadata?.socket_token;
      const signature = appStore.metadata?.socket_signature;
      const deviceId = await getDeviceId.getFinger();
      socket.value[name]?.emit('identify', {
        uid: uid.value,
        authorizationToken,
        signature,
        deviceid: deviceId,
      });
    } else {
      // console.log(`identify success: ${name}`);
    }
  };
  // 订阅消息
  const subscribeSocket = (name: string) => {
    const nameSp = `/${name}`;
    if (socket.value[name]) return;
    if (!manager.value) {
      connect();
    }
    const socketItem = (socket.value[name] = manager.value!.socket(nameSp));
    socketItem.on('init', (data) => {
      initNameSp(name, data.identifying);
    });
    socketItem.on('error', (error) => {
      console.error('socket error', error);
    });
  };
  // 取消订阅消息
  const unSubscribeSocket = (name: string) => {
    if (socket.value[name]) {
      socket.value[name]?.off();
      socket.value[name]?.disconnect();
      delete socket.value[name];
    }
  };
  return {
    manager,
    socket,
    subscribeSocket,
    unSubscribeSocket,
    connect,
  };
});
