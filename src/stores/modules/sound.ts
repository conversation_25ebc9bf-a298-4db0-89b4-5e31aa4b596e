import { defineStore } from 'pinia';

export const useSoundStore = defineStore('sound', () => {
  const soundEnabledCookie = useCookie('soundEnabled', {
    maxAge: 60 * 60 * 24 * 365,
  });
  let sound = true;
  if (soundEnabledCookie.value !== undefined) {
    sound = Boolean(soundEnabledCookie.value) !== false;
  }
  const soundEnabled = ref<boolean>(sound);
  const switchSound = () => {
    soundEnabled.value = !soundEnabled.value;
    soundEnabledCookie.value = String(soundEnabled.value);
  };
  return {
    soundEnabled,
    switchSound,
  };
});
