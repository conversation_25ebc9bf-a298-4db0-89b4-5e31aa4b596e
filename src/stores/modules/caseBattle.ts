import { defineStore } from 'pinia';
import dayjs from 'dayjs';
import { useSettingStore } from './setting';
// import { useSocketStore } from './socket';
import { useSoundStore } from './sound';
import {
  BattleModesType,
  BattleStatusType,
  type BattleType,
  GameModesType,
  type Player,
} from '@/types/battles';
import { ROUND_DURATION } from '@/constants/timing';
import { Sound } from '~/types/sound';

const INIT_BATTLE_INFO = {
  creator_uid: '0',
  players: [],
  game_mode: GameModesType.NORMAL,
  is_private: 1,
  battle_mode: BattleModesType.REGULAR,
  battle_cost: 0,
  status: BattleStatusType.INIT,
  player_number: 4,
  active_round: -1,
  win_amount: 0,
  seed: '-',
  seed_committed_at: 0,
  private_seed: '-',
  private_seed_committed_at: 0,
  private_seed_hash: '-',
  seed_index: '-',
  seed_index_committed_at: 0,
  tiebreaker: {
    result: 0,
    participantPositions: [],
  },
  started_at: '0',
  ended_at: 0,
  cases: [],
  battleCaseList: [],
  battlePlayers: [],
  arena_id: '',
  status_desc: '',
  total_round: 0,
  seed_source: '',
  is_tiebreaker: false,
};

export const useCaseBattleStore = defineStore('caseBattle', () => {
  const battleInfo = ref<BattleType>(INIT_BATTLE_INFO) as any;
  const INIT_COUNT_DOWN = 6;

  // 定义其他响应式状态
  // const socketStore = useSocketStore();
  const { battleApi } = useApi();
  const awaitingPlayers = computed(() =>
    battleInfo.value.battlePlayers?.some((player: any) => player === undefined),
  );
  const caseBattleWins = ref(0);
  const countdown = ref(INIT_COUNT_DOWN);
  const currentRound = computed(() => battleInfo.value.active_round ?? -1);
  const forceRefresh = ref(0);
  const spinnerVisible = ref<any>(false);
  const delayTimerRef = ref<any>(null);

  // 计算平局信息
  const tiebreakerInfo = computed(() => {
    if (!battleInfo.value.tiebreaker) return [];
    return battleInfo.value.tiebreaker.participantPositions
      .map((pos: number) => battleInfo.value.battlePlayers[pos - 1])
      .filter((player: any) => !!player);
  });
  const timeSinceRoundStarted = ref<any>(0);

  // 用户store
  const userStore = useSettingStore();

  const soundStore = useSoundStore();

  const soundEnabled = computed(() => soundStore.soundEnabled);

  const { play } = useSoundControl({ mute: !soundEnabled.value });

  const user = computed(() => userStore.userInfo);

  // 获取决胜局结果
  function generateTiebreakerData(data: any, players: any[]) {
    const winnerUid = data.tiebreaker?.find(
      (item: any) => item.is_win === 1,
    )?.uid;
    if (!winnerUid) return null;
    const participantUsers = data.tiebreaker?.map((item: any) =>
      players?.find((p: any) => p.uid === item.uid),
    );
    const participantPositions = participantUsers?.map((p: any) => p.position);
    const result = participantUsers?.findIndex((p: any) => p.uid === winnerUid);
    return {
      result,
      participantPositions,
      arena_id: data.arena_id || battleInfo.value.arena_id,
    };
  }

  // 获取战斗轮次兜底信息
  const getBattleRoundInfo = async (
    arenaId: string,
    round: number,
    isTiebreaker: number,
  ) => {
    const res = await battleApi.battleRound({
      arena_id: arenaId,
      round,
      is_tiebreaker: isTiebreaker,
    });
    const resData = res.data.value;
    if (resData.code === 0) {
      if (!resData.data.records?.length) return;
      return resData.data;
    }
  };

  // 设置战斗初始状态
  async function setBattleInitialState(battleData: V1GetBattleInfoReply) {
    // 如果没有战斗数据，直接返回
    if (!battleData) return;
    // 隐藏旋转器
    spinnerVisible.value = false;

    battleInfo.value.status = BattleStatusType.INIT;
    countdown.value = INIT_COUNT_DOWN;

    const casesMap = new Map();
    const casesList = battleData?.cases?.map((c: any) => {
      const caseSlug = battleData.case_slug?.find(
        (e: any) => e?.slug === c.slug,
      );
      const caseItems = battleData?.case_contain?.[c.slug]?.list?.map(
        (item: any) => {
          const goodsInfo =
            battleData?.goods?.find((r: any) => r.goods_id === item.goods_id) ??
            {};
          return { ...item, ...goodsInfo };
        },
      );

      const caseInfo = {
        ...c,
        qty: caseSlug?.qty || 1,
        items: caseItems || [],
      };
      casesMap.set(c.slug, caseInfo);
      return caseInfo;
    });

    // 更新战斗信息，并将当前回合数减1（因为回合从0开始计数）
    battleInfo.value = {
      ...battleInfo.value,
      ...battleData.battle_info,
      players: battleData?.players,
      cases: casesList,
      active_round: (battleData?.battle_info?.active_round || 0) - 1,
    };

    // 创建玩家数组，初始化为undefined
    const players = Array(battleData?.battle_info?.player_number || 0).fill(
      undefined,
    );

    const caseList =
      battleData?.case_slug?.flatMap((caseItem: any) =>
        Array(caseItem.qty)
          .fill(null)
          .map(() => ({
            ...caseItem,
            ...(casesMap.get(caseItem.slug) || {}),
            qty: 1,
          })),
      ) || [];

    // 计算自战斗开始以来经过的时间
    const timeSinceStart = Math.max(
      getLocalTimeAsUTCTimestamp() -
        new Date(battleInfo.value.status_at).getTime() -
        currentRound.value * ROUND_DURATION -
        7000,
      0,
    );

    // 如果战斗正在进行中
    if (battleInfo.value.status === BattleStatusType.RUNNING) {
      try {
        const data = await getBattleRoundInfo(
          battleInfo.value.arena_id,
          (battleData.battle_info?.active_round ?? 0) + 1,
          battleData.tiebreaker?.length ? 1 : 2,
        );
        if (data && battleData?.records) {
          battleData.records = battleData?.records?.concat(data.records ?? []);
          battleData.tiebreaker = data.tiebreaker;
          if (battleData.battle_info) {
            battleData.battle_info.active_round =
              (battleData.battle_info.active_round || 0) + 1;
          }
        }
      } catch (error) {
        console.error('获取战斗回合信息失败:', error);
      }
    }

    const goodsMap = new Map();
    battleData?.goods?.forEach((g: any) => goodsMap.set(g.goods_id, g));

    // 处理玩家数据
    battleData?.players?.forEach((player: any) => {
      const userData =
        battleData?.user?.find((u: any) => u.uid === player.uid) ?? {};
      const playerItems =
        battleData?.records
          ?.filter((r: any) => player.position === r.position)
          ?.filter(Boolean)
          ?.map((r: any) => {
            const caseInfo = caseList?.find((c: any) => c.slug === r.case_slug);
            const goodsInfo = goodsMap.get(r.goods_id) || {};
            return { ...r, ...caseInfo, ...goodsInfo };
          }) ?? [];

      players[player.position - 1] = {
        ...player,
        ...userData,
        itemsData: playerItems,
      };
    });
    // 处理战斗状态
    if (battleInfo.value.status === BattleStatusType.RUNNING) {
      nextTick(() => {
        timeSinceRoundStarted.value = timeSinceStart;
        spinnerVisible.value = true;
        if (battleData?.records?.length) {
          setStatus(BattleStatusType.RUNNING);
        }
      });
    }

    // 如果战斗正在等待EOS区块，且未强制刷新
    const diff = dayjs(getLocalTimeAsUTCTimestamp()).diff(
      dayjs(new Date(battleInfo.value.status_at).getTime()),
      'seconds',
    );
    if (
      battleInfo.value.status === BattleStatusType.RUNNING &&
      battleInfo.value.active_round <= 0 &&
      (battleInfo.value.seed !== '' || countdown.value === INIT_COUNT_DOWN) &&
      forceRefresh.value === 0
    ) {
      const safeDiff = Math.max(0, diff);
      const remainingTime = Math.max(0, countdown.value - safeDiff);
      const actualWaitTime = Math.min(countdown.value, remainingTime);
      countdown.value = actualWaitTime;
      if (actualWaitTime > 5) {
        if (delayTimerRef.value) {
          clearTimeout(delayTimerRef.value);
        }
        const delayTime = (actualWaitTime - 5) * 1000;
        delayTimerRef.value = setTimeout(() => {
          delayTimerRef.value = null;
          startCountdown();
          setStatus(BattleStatusType.WAITING_FOR_EOS);
        }, delayTime);
      } else {
        startCountdown(actualWaitTime);
        setStatus(BattleStatusType.WAITING_FOR_EOS);
      }
    }

    // 处理平局信息
    const tiebreakerData = generateTiebreakerData(battleData, players);
    if (tiebreakerData) {
      setTiebreaker(tiebreakerData);
    }

    // 更新战斗信息中的玩家和箱子列表
    battleInfo.value = {
      ...battleInfo.value,
      battlePlayers: players,
      battleCaseList: caseList,
    };
  }

  // 玩家加入战斗
  function playerJoin(data: any, isBot: number = 1, position: number) {
    if (data.arena_id === battleInfo.value.arena_id) {
      battleInfo.value.players.push({
        ...data.user,
        is_bot: isBot,
      });
      battleInfo.value.battlePlayers[position - 1] = {
        ...data,
        ...data?.user,
        itemsData: [],
        is_bot: isBot,
      };
    }
  }

  // 玩家离开战斗
  function playerLeave(data: any) {
    if (data.arena_id === battleInfo.value.arena_id) {
      battleInfo.value.players = battleInfo.value.players.filter(
        (player: any) => player.position !== data.position,
      );
      battleInfo.value.battlePlayers[data.players.position - 1] = undefined;
    }
  }

  // 取消战斗
  function cancelBattle(arenaId: string) {
    if (arenaId === battleInfo.value.arena_id) {
      setStatus(BattleStatusType.CANCELLED);
    }
  }

  // 设置EOS区块
  function setEosBlock(data: {
    arena_id: string;
    seed_index: string;
    seed_index_committed_at: string;
    seed: string;
    seed_committed_at: string;
  }) {
    if (data.arena_id === battleInfo.value.arena_id) {
      battleInfo.value.seed_index = data?.seed_index;
      battleInfo.value.seed_index_committed_at = data?.seed_index_committed_at;
      battleInfo.value.seed = data?.seed;
      battleInfo.value.seed_committed_at = data?.seed_committed_at;
      if (countdown.value === INIT_COUNT_DOWN) {
        startCountdown();
      }
    }
  }

  // 设置服务器种子
  function setServerSeed(data: any) {
    if (data.arena_id === battleInfo.value.arena_id) {
      // battleInfo.value.private_seed = data.private_seed;
      battleInfo.value.seed = data.seed;
      battleInfo.value.seed_committed_at = data.seed_committed_at;
      setStatus(BattleStatusType.RUNNING);
    }
  }

  // 设置私有种子
  function setPrivateSeed(data: any) {
    if (data.arena_id === battleInfo.value.arena_id) {
      battleInfo.value.private_seed = data.private_seed;
    }
  }

  // 设置平局
  function setTiebreaker(data: any) {
    if (data.arena_id === battleInfo.value.arena_id) {
      battleInfo.value.tiebreaker = data;
      setStatus(BattleStatusType.TIEBREAKER);
    }
  }

  // 推送新回合
  function pushNewRound(data: any) {
    if (!data || data.arena_id !== battleInfo.value.arena_id) return;
    if ('currentRound' in data || currentRound.value === data.round - 1) return;

    // 强制刷新
    if (currentRound.value + 1 < data.round - 1) {
      retryGetBattleInfo();
      return;
    }

    try {
      timeSinceRoundStarted.value = 0;
      battleInfo.value.active_round = data.round - 1;
      // 获取当前回合的箱子信息
      const currentRoundCaseList =
        battleInfo.value.battleCaseList[data.round - 1];
      if (!currentRoundCaseList) {
        console.warn(`回合${data.round}的箱子信息不存在`);
        return;
      }

      // 处理玩家数据
      data.players.forEach((player: any) => {
        if (!player || !player.position) return;

        // 获取玩家打开的物品信息
        const playerPosition = player.position - 1;
        if (
          playerPosition < 0 ||
          playerPosition >= battleInfo.value.battlePlayers.length
        )
          return;

        let openedItem = currentRoundCaseList.items.find(
          (item: any) => item.goods_id === player.opened_item_id,
        );

        if (!openedItem) return;

        const recordData = data.records?.find(
          (r: any) => r.position === player.position,
        );
        openedItem = { ...openedItem, ...(recordData || {}) };

        // 更新玩家数据
        const currentPlayer = battleInfo.value.battlePlayers[playerPosition];
        if (!currentPlayer) return;

        battleInfo.value.battlePlayers[playerPosition] = {
          ...currentPlayer,
          ...player,
          items: [...(currentPlayer.items || []), openedItem.goods_id],
          itemsData: [...(currentPlayer.itemsData || []), openedItem],
        };
      });

      spinnerVisible.value = true;
      setStatus(BattleStatusType.RUNNING);
    } catch (error) {
      console.error('处理新回合数据出错:', error);
      spinnerVisible.value = false;
    }
  }

  // 设置战斗状态
  function setStatus(status: any) {
    battleInfo.value.status = status;
  }

  // 结束战斗
  function finishBattle(data: any) {
    if (data.arena_id === battleInfo.value.arena_id) {
      if (user.value && data.winners === user.value.uid) {
        caseBattleWins.value = caseBattleWins.value + 1;
      }
      battleInfo.value.battlePlayers = battleInfo.value.battlePlayers.map(
        (player: Player) => {
          if (!player) return player;
          return {
            ...player,
            itemsData: (player.itemsData ?? []).map((item: any) => ({
              ...item,
              ...data.records.find((r: any) => r.record_id === item.record_id),
            })),
          };
        },
      );
      battleInfo.value.winners = data.winner;
      setPrivateSeed(data);
      setStatus(BattleStatusType.FINISHED);
    }
  }

  function retryGetBattleInfo() {
    spinnerVisible.value = false;
    forceRefresh.value += 1;
  }

  // 倒计时相关
  const countdownTimer = useIntervalFn(decrementCountdown, 1000, {
    immediate: false,
  });

  function startCountdown(time = 5) {
    if (soundEnabled.value) {
      play(Sound.COUNT_DOWN_TICK);
    }
    countdown.value = time;
    countdownTimer.resume();
  }

  function decrementCountdown() {
    if (countdown.value > 0) {
      play(Sound.COUNT_DOWN_TICK);
    }
    countdown.value = countdown.value - 1;

    if (countdown.value <= 0) {
      play(Sound.COUNT_DOWN_END);
      countdown.value = 0;
      countdownTimer.pause();

      setTimeout(() => {
        setStatus(BattleStatusType.RUNNING);
      }, 1000);
    }
  }

  // 获取玩家物品
  function getPlayerItems(playerIndex: number) {
    if (
      [
        BattleStatusType.FINISHED,
        // BattleStatusType.PAYOUT_STARTED,
        BattleStatusType.TIEBREAKER,
      ].includes(battleInfo.value.status)
    ) {
      return battleInfo.value.battlePlayers[playerIndex]?.itemsData || [];
    }
    return (
      battleInfo.value.battlePlayers[playerIndex]?.itemsData.slice(0, -1) || []
    );
  }

  // 离开时战斗页面
  function clearBattleInfo() {
    countdownTimer.pause();
    // 重置状态
    battleInfo.value = INIT_BATTLE_INFO;
    forceRefresh.value = 0;
    countdown.value = INIT_COUNT_DOWN;
    timeSinceRoundStarted.value = 0;
    spinnerVisible.value = false;
  }

  return {
    battleInfo,
    awaitingPlayers,
    caseBattleWins,
    countdown,
    currentRound,
    forceRefresh,
    spinnerVisible,
    tiebreakerInfo,
    timeSinceRoundStarted,
    setBattleInitialState,
    playerJoin,
    playerLeave,
    cancelBattle,
    setEosBlock,
    setServerSeed,
    setTiebreaker,
    pushNewRound,
    setStatus,
    finishBattle,
    getPlayerItems,
    clearBattleInfo,
    retryGetBattleInfo,
    generateTiebreakerData,
  };
});
