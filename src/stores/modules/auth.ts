import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useSettingStore } from './setting';
import { STORAGE_KEYS } from '~/constants';

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref(false);
  const settingStore = useSettingStore();

  function checkAuth() {
    const token = useCookie(STORAGE_KEYS.COOKIES.TOKEN);
    if (token.value) {
      isAuthenticated.value = true;
    } else {
      isAuthenticated.value = false;
    }
    return isAuthenticated.value;
  }

  function redirectToLogin() {
    checkAuth();
    if (!isAuthenticated.value) {
      const $router = useRouter();
      // 是否是主播后台
      const isLive = $router.currentRoute.value.path.includes('live-mng');
      if (isLive) {
        return navigateTo('/live-mng/login');
      }
      nextTick(() => {
        settingStore.clearUserInfo();
      });
      return navigateTo('/');
    }
  }

  return { isAuthenticated, checkAuth, redirectToLogin };
});
