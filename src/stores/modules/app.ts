import { defineStore } from 'pinia';
export const useAppStore = defineStore('appInfo', () => {
  const spinShow = ref<boolean>(false);
  const { homeApi, settingApi } = useApi();
  // 元数据
  const metadata = ref<
    (ApiBffInterfaceV1GetMetadataReply & { time: number }) | null
  >(null);
  const languages = ref<V1LanguageTypeInfo[]>([]);
  const currentLang = ref<string>('');

  // 公告数据
  const announcements = ref<ApiV1NotifyNoticeGet200ResponseData['list']>([]);
  // 消息等红点数据
  // 正在取回
  const extractingTotal = computed(
    () => metadata.value?.notify?.bag_total?.extracting_total || 0,
  );
  // 已取回(成功/失败)
  const extractTotal = computed(
    () => metadata.value?.notify?.bag_total?.extract_total || 0,
  );
  // 消息未读红点
  const msgTotal = computed(() => metadata.value?.notify?.msg_total || 0);
  // 我的皮肤红点 = 未查看的ROLL房奖励的饰品个数
  const skinTotal = computed(
    () => metadata.value?.notify?.bag_total?.roll_total || 0,
  );
  // 背包取回红点 = 正在取回和取回记录的红点总和
  const backpackTotal = computed(
    () => extractTotal.value + extractingTotal.value,
  );
  // header背包数量红点 = ROLL房获得饰品未查看的个数+未查看的取回订单数
  const headerBackpackTotal = computed(
    () => skinTotal.value + backpackTotal.value,
  );

  // 等级信息
  const levelConfig = ref<V1LevelConfigItem[]>([]);
  const levelMap = ref<Map<number, V1LevelConfigItem>>(new Map());
  // 限制国家白名单
  const ipPermission = ref(false);

  const globalRefreshKey = ref(0);

  // 费率比
  const exchangeRateMap = computed(() => {
    const map = new Map<string, number>();
    Object.entries(metadata.value?.exchange_rate?.rates || {}).forEach(
      ([key, value]) => {
        map.set(key, value);
      },
    );
    return map;
  });

  // 轮询消息数量
  const {
    start: startPollingNotify,
    running: notifyPollingRunning,
    stop: stopPollingNotify,
    reset,
  } = usePolling(async () => {
    if (!checkLogin(false) && notifyPollingRunning.value) {
      stopPollingNotify();
    }
    await getMetadata(false);
  }, 1000 * 60);

  // 获取元数据
  const getMetadata = async (
    isReset: boolean = true,
    server: boolean = false,
  ) => {
    const { data: req } = await homeApi.getMetadata(server);
    const data = req.value?.data;
    if (data) {
      if (isReset) {
        reset(undefined, false);
      }
      setMetaData(data);
      return data;
    }
  };

  const setMetaData = (data: any) => {
    metadata.value = {
      ...data,
      time: new Date().getTime(),
    };
    languages.value = data.language_list || [];
    ipPermission.value = !data.is_country_blocked;
  };

  const setCurrentLang = (lang: string) => {
    currentLang.value = lang;
  };
  const openSpin = () => {
    spinShow.value = true;
  };
  const closeSpin = () => {
    spinShow.value = false;
  };

  // 获取等级配置信息
  const getLevelConfig = async () => {
    if (levelMap.value?.size) return;
    const res = await settingApi.getLevelConfig();
    if (res.data.value?.code === 0) {
      const list = res.data.value.data.list ?? [];
      levelConfig.value = list;
      list.forEach((el) => {
        if (el.level_id !== undefined) {
          levelMap.value.set(el.level_id, el);
        }
      });
    }
  };

  // 获取公告列表
  const getAnnouncements = async () => {
    try {
      const { data: req } = await homeApi.getNotice();
      if (req?.value?.code === 0) {
        announcements.value = req.value.data?.list || [];
      }
    } catch (e) {
      console.error('获取公告列表失败:', e);
    }
  };

  return {
    // 加载动画
    spinShow,
    openSpin,
    closeSpin,
    // 元数据
    metadata,
    getMetadata,
    setMetaData,
    // 等级
    levelConfig,
    levelMap,
    getLevelConfig,
    // ip权限
    ipPermission,
    // 消息红点
    msgTotal,
    headerBackpackTotal,
    backpackTotal,
    skinTotal,
    extractTotal,
    extractingTotal,
    // 消息轮询
    notifyPollingRunning,
    startPollingNotify,
    globalRefreshKey,
    // 语言列表
    languages,
    setCurrentLang,
    currentLang,
    exchangeRateMap,
    // 公告列表
    announcements,
    getAnnouncements,
  };
});
