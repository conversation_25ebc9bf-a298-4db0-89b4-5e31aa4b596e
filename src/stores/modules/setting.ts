import { defineStore } from 'pinia';
import { useAppStore } from './app';
import Confirm from '~/components/dialog/Confirm.vue';
import AuthModal from '~/components/dialog/auth/AuthModal.vue';
import SvgoLogout from '~/assets/icons/logout.svg';
import promptsConfirm from '~/composables/dialog/useDialogPromptsConfirm';
import BindEmailModal from '~/components/dialog/auth/BindEmailModal.vue';
import ResetPasswordModal from '~/components/dialog/auth/ResetPasswordModal.vue';
import { STORAGE_KEYS } from '~/constants';

export const useSettingStore = defineStore('settingInfo', () => {
  const { settingApi, loginApi } = useApi();
  const dialog = useAppDialog();
  const { $i18n } = useNuxtApp();
  const toast = useAppToast();
  const appStore = useAppStore();
  const cookieManager = useCookieManager();
  const { t } = $i18n;
  const dialogRef = ref();
  const userInfo = ref<V1GetUserInfoReply | null | undefined>(null);
  const clientSeed = ref('');
  const inviteCode = ref('');
  const platform = ref(0);
  const { resetVerification } = useTurnstileVerification();
  let openConfirmTimer: ReturnType<typeof setTimeout> | null = null;

  // Steam登录轮询
  let steamLoginPolling: {
    running: Ref<boolean>;
    stop: () => void;
    start: (stopTime?: number, immediately?: boolean) => Promise<void>;
  } | null = null;
  // 等级进度展示数据
  const levelProgression = computed(() => {
    if (!userInfo.value)
      return {
        exp: 0,
        step: 0,
      };
    const levelInfo = userInfo.value?.level_info ?? ({} as V1UserInfoLevelInfo);
    const curLevelNum = levelInfo.now_level_id || 0;
    const nextLevelNum = levelInfo.next_level_id || 1;
    const curIndex = levelInfo?.list?.findIndex(
      (el) => el.level_id === curLevelNum,
    );
    const nextIndex = levelInfo?.list?.findIndex(
      (el) => el.level_id === nextLevelNum,
    );
    const curLevel =
      levelInfo.list && typeof curIndex === 'number' && curIndex >= 0
        ? levelInfo.list[curIndex]
        : {};
    const nextLevel =
      levelInfo.list && typeof nextIndex === 'number' && nextIndex >= 0
        ? levelInfo.list[nextIndex]
        : {};
    const diff = (levelInfo?.exp_value || 0) - (curLevel.start_score || 0);
    const expProgress = Math.min(diff, nextLevel.step_score || 0);

    return {
      exp: expProgress,
      step: nextLevel.step_score || 0,
    };
  });

  const getUserInfo = async (server: boolean = false): Promise<void> => {
    const userAuth = useCookie(STORAGE_KEYS.COOKIES.TOKEN);
    if ((server && userInfo.value) || (import.meta.server && !userAuth.value))
      return;
    try {
      const params: {
        platform?: string;
      } = {};
      const router = useRouter();
      if (router.currentRoute.value.fullPath.includes('/live-mng')) {
        params.platform = '1';
      }
      const { data: req } = await settingApi.getUserInfo(server, params);
      userInfo.value = req?.value?.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  /**
   * 清除Steam登录轮询
   */
  const clearSteamLoginPolling = (): void => {
    if (steamLoginPolling?.running.value) {
      steamLoginPolling.stop();
    }
    steamLoginPolling = null;
  };

  /**
   * 清除用户信息
   */
  const clearUserInfo = ({
    closeDialog = true,
  }: Partial<{
    closeDialog: boolean;
  }> = {}): void => {
    userInfo.value = null;
    setToken(null);
    clearErrorCookies();
    localStorage.removeItem(STORAGE_KEYS.LOCAL_STORAGE.BIND_STATUS);
    closeDialog && dialog.closeAll();
    resetVerification();
    getDeviceId.clearFinger();
    // 清除Steam登录轮询
    clearSteamLoginPolling();
  };

  const setToken = (token: string | null = null) => {
    setCookie(STORAGE_KEYS.COOKIES.TOKEN, token);
  };

  const setTokenSafely = async (newToken: string | null | undefined) => {
    setToken(null);
    await nextTick();
    setToken(newToken || null);
    await nextTick();
  };

  const setCookie = (name: string, value: any, options?: any) => {
    cookieManager.setCookie(name, value, options);
  };

  const clearErrorCookies = () => {
    cookieManager.deleteCookies([
      STORAGE_KEYS.COOKIES.ERROR_CODE,
      STORAGE_KEYS.COOKIES.ERROR_MSG,
    ]);
  };

  const setCookies = (cookieMap: Record<string, any>, options?: any) => {
    cookieManager.setCookies(cookieMap, options);
  };

  const clearCookies = (cookieKeys: string[]) => {
    cookieManager.deleteCookies(cookieKeys);
  };

  /**
   * 用户登出
   * @param logoutFn 登出方法
   */
  const handleLogout = async (
    logoutFn: () => Promise<any> = () =>
      Promise.resolve({
        data: { value: { code: 0 } },
      }),
    isLive: boolean = false,
  ): Promise<void> => {
    try {
      const res = await logoutFn();
      if (res?.data?.value?.code === 0) {
        clearUserInfo();
        const { reset } = useChatWoot();
        reset();
        if (!isLive) {
          appStore.globalRefreshKey++;
        } else {
          location.href = '/live-mng/login';
        }
      } else {
        console.error('登出失败:', res.data.value.message);
      }
    } catch (error) {
      console.error('登出请求出错:', error);
    }
  };

  /**
   * 登出当前设备
   */
  const logout = async (isLive: boolean = false): Promise<void> => {
    await handleLogout(loginApi.logout, isLive);
  };

  /**
   * 登出所有设备
   */
  const logoutAll = async (): Promise<void> => {
    await handleLogout(loginApi.logoutAll);
  };

  /**
   * 登录注册
   * @param defaultTab 显示标签 'login' | 'register'
   */
  const openAuthModal = (defaultTab: 'login' | 'register' = 'login') => {
    return dialog?.open(AuthModal, {
      style: {
        width: '436px',
      },
      allowMultiple: false,
      contentProps: {
        defaultTab,
        onSuccess: () => {
          getUserInfo();
          dialog?.hideByKey();
        },
      },
    });
  };

  /**
   * 绑定邮箱弹窗
   */
  const openBindEmailModal = () => {
    return dialog?.open(BindEmailModal, {
      style: {
        width: '436px',
      },
      allowMultiple: false,
      contentProps: {
        email: userInfo.value?.email,
        onSuccess: () => {
          dialog?.hideByKey();
          getUserInfo();
        },
      },
    });
  };

  /**
   * 重置密码弹窗
   * @param
   */
  const openResetPasswordModal = () => {
    return dialog?.open(ResetPasswordModal, {
      style: {
        width: '436px',
      },
      allowMultiple: false,
      contentProps: {
        email: userInfo.value?.email,
        onSuccess: () => {
          dialog?.hideByKey();
          handleLogout();
        },
      },
    });
  };

  /**
   * Steam登录
   * @param isLive 是否是直播后台登录
   */
  const doSteamLogin = async ({
    isLive = false,
    closeDialog = true,
    event,
  }: Partial<{
    isLive: boolean;
    closeDialog: boolean;
    event: 'login' | 'bind';
  }> = {}) => {
    try {
      let token = ref();
      let errCode = ref();
      let errMsg = ref();
      const { $getCookie, $i18n } = useNuxtApp();
      const { t } = $i18n;
      const loginStatus = async () => {
        const isLogin = event === 'login';

        let bindStatus: any;
        if (isLogin) {
          token = await $getCookie(STORAGE_KEYS.COOKIES.TOKEN);
        } else {
          bindStatus =
            localStorage.getItem(STORAGE_KEYS.LOCAL_STORAGE.BIND_STATUS) ===
            '1';
        }
        errCode = await $getCookie(STORAGE_KEYS.COOKIES.ERROR_CODE);
        errMsg = await $getCookie(STORAGE_KEYS.COOKIES.ERROR_MSG);
        const hasToken =
          token.value && token.value !== '' && token.value !== null;
        const hasError =
          errCode.value && errCode.value !== '' && errCode.value !== null;

        if (
          ((hasToken && isLogin) || (bindStatus && !isLogin) || hasError) &&
          steamLoginPolling?.running.value
        ) {
          steamLoginPolling?.stop();
          dialogRef.value?.destroy();
          isLogin && setToken(token.value);
          closePopupWindow();

          if (hasError) {
            const { openConfirm } = promptsConfirm('signin');
            dialog?.closeAll();
            if (openConfirmTimer) {
              clearTimeout(openConfirmTimer);
              openConfirmTimer = null;
            }
            openConfirmTimer = setTimeout(async () => {
              openConfirm({
                theme: isLive ? 'light' : 'dark',
                title: '',
                hideCancelBtn: true,
                content: t(errCode.value, errMsg.value || errCode.value || ''),
                enablePrompts: true,
                confirmBg: isLive ? '#419FFF' : undefined,
                confirmText: t('confirm'),
                allowMultiple: false,
              });
              await nextTick();
              clearErrorCookies();
              openConfirmTimer = null;
            }, 300);
          } else if (isLive) {
            location.replace('/live-mng');
          } else if (isLogin) {
            getUserInfo();
            dialog?.closeAll();
            appStore.globalRefreshKey++;
          } else {
            toast.success({
              content: t('bind_success', 'Binding successful'),
            });
            localStorage.removeItem('bind_status');
            getUserInfo();
          }
        }
      };

      if (event === 'login') {
        const { reset } = useChatWoot();
        reset();
        clearUserInfo({ closeDialog });
      }
      await getDeviceId.getFinger();

      // 创建轮询
      steamLoginPolling = usePolling(loginStatus, 1000);

      const url = new URL('/login', location.origin);
      const currentPath = window?.location?.pathname || '';
      const _inviteCode = currentPath.split('/r/')[1];
      if (_inviteCode) {
        inviteCode.value = _inviteCode;
        url.searchParams.set('invite_code', _inviteCode);
      }
      if (isLive) {
        platform.value = 1;
        url.searchParams.set('platform', '1');
      }
      if (event) {
        url.searchParams.set('event', event);
        if (event === 'bind') {
          clearErrorCookies();
          url.searchParams.set('uid', userInfo.value?.uid ?? '');
        }
      }
      popupOpenLink(url.toString());
      await nextTick();
      steamLoginPolling.start();
    } catch (error) {
      console.error('Steam登录失败:', error);
    }
  };

  /**
   * 登录功能
   * @param openDialog 是否打开登录对话框
   * @param isLive 是否是直播后台登录
   * @returns
   */
  const signin = ({
    openDialog = true,
    isLive = false,
    event,
  }: Partial<{
    openDialog: boolean;
    isLive: boolean;
    event: 'login' | 'bind';
  }> = {}) => {
    try {
      if (!openDialog || event) {
        return doSteamLogin({ isLive, closeDialog: false, event });
      }

      return openAuthModal('login');
    } catch (error) {
      console.error('打开登录对话框失败:', error);
    }
  };

  const signout = (isLive: boolean = false) => {
    return (dialogRef.value = dialog.open(Confirm, {
      class: 'max-w-[90%] bg-[#151A29] rounded-[8px]',
      style: {
        width: '436px',
      },
      contentProps: {
        title: t('log_out'),
        content: t('please_log_in_and_use'),
        confirmText: t('log_out').toUpperCase(),
        footer: h(
          'div',
          {
            class:
              'w-full cursor-pointer hover:text-theme-color text-[16px] flex items-center justify-center gap-x-2 mt-6 ',
            onClick: () => {
              // 全部登出
              logoutAll();
            },
          },
          [
            h(SvgoLogout, {
              class: 'mb-0',
            }),
            t('logout_from_all_devices'),
          ],
        ),
        onConfirm: () => {
          logout(isLive);
        },
      },
    }));
  };

  return {
    userInfo,
    clientSeed,
    levelProgression,
    inviteCode,
    platform,
    setToken,
    setTokenSafely,
    setCookie,
    clearErrorCookies,
    setCookies,
    clearCookies,
    getUserInfo,
    signin,
    signout,
    logout,
    logoutAll,
    clearUserInfo,
    clearSteamLoginPolling,
    openAuthModal,
    openBindEmailModal,
    openResetPasswordModal,
    doSteamLogin,
  };
});
