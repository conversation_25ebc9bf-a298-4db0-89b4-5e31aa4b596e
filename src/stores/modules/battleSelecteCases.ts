import { defineStore } from 'pinia';

export const MAX_CASES = 50;

export const useBattleSelectedCasesStore = defineStore('battleCases', () => {
  const {
    caseSelections,
    currentMode,
    currentModePlayers,
    isCreateBattleLoading,
    showInsufficientBalance,
    privateBattleEnabled,
    unoModeEnabled,
    totalSelectedCases,
    totalValue,
    increment,
    decrement,
    updateQuantity,
    createBattle,
    reset,
  } = useBattleSelectedCases();

  return {
    caseSelections,
    currentMode,
    currentModePlayers,
    isCreateBattleLoading,
    privateBattleEnabled,
    showInsufficientBalance,
    totalValue,
    unoModeEnabled,
    totalSelectedCases,
    MAX_CASES,
    increment,
    decrement,
    updateQuantity,
    createBattle,
    reset,
  };
});
