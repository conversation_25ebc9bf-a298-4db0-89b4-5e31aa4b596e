import { defineStore } from 'pinia';
import { useSettingStore } from './setting';
import { createFormSchema } from '~/models/coinflipFilterModel';
import type { RoomInfoType } from '~/types/coinflip';
export const useCoinflipStore = defineStore('coinflip', () => {
  const { coinflipApi } = useApi();
  const settingStore = useSettingStore();
  // list 数据
  const openList = ref<(RoomInfoType | null)[]>([]);
  const myList = ref<RoomInfoType[]>([]);
  const historyList = ref<RoomInfoType[]>([]);
  // map list
  const openListMap = computed(() => {
    const arr = openList.value.filter((item) => !!item);
    return new Map(arr.map((item) => [item.id, item]));
  });
  const myListMap = computed(
    () => new Map(myList.value.map((item) => [item.id, item])),
  );

  const formFilter = createFormSchema();
  const modules = reactive({
    my: {
      total: 0,
      loading: false,
      emptyMsg: 'empty_coinflip_description',
    },
    history: {
      page: 1,
      page_size: 15,
      total: 0,
      loading: false,
      emptyMsg: 'empty_coinflip_description',
    },
    open: {
      total: 0,
      loading: false,
      emptyMsg: 'no_open_coinflip_games',
    },
  });
  // 将开放游戏长度扩展为选择的长度
  const extendArray = (arr: RoomInfoType[], length: number) => {
    return arr.concat(Array(Math.max(length - arr.length, 0)).fill(null));
  };

  // 开放游戏列表
  const getOpenList = async (
    params: ApiV1CoinflipListGetRequest,
    server: boolean = false,
  ) => {
    modules.open.loading = true;
    const { data: req } = await coinflipApi.openGameList(
      {
        ...params,
        page_size: undefined,
      },
      server,
    );
    const data = req.value?.data;
    modules.open.loading = false;
    if (data) {
      const pageSize = params.page_size || 10;
      const list = data.list || [];
      // 页面展示长度数据
      const sizeList = list.slice(0, pageSize);
      openList.value = extendArray(sizeList, params.page_size || 10);
      modules.open.total = data.total || 0;
      // 剩余长度进入等待插入列表
      const lastList = list.slice(pageSize);
      waitPushMap.wait = new Map(
        lastList.map((item) => [item.id as number, item]),
      );
      waitPushMap.playing = new Map();
      waitPushMap.final = new Map();
    }
  };
  // 当前进行中我的游戏列表
  const getMyList = async (server: boolean = false) => {
    if (!settingStore.userInfo?.uid) return;
    modules.my.loading = true;
    const { data: req } = await coinflipApi.myGameList(server);
    const data = req.value?.data;
    modules.my.loading = false;
    if (data) {
      myList.value = data.list || [];
      modules.my.total = data.total || 0;
    }
  };
  // 我的历史游戏列表
  const getHistoryList = async () => {
    modules.history.loading = true;
    const { data: req } = await coinflipApi.historyGameList({
      page: modules.history.page,
      page_size: modules.history.page_size,
    });
    const data = req.value?.data;
    modules.history.loading = false;
    if (data) {
      historyList.value = data.list || [];
      modules.history.total = data.total || 0;
    }
  };

  // socket
  // 等待对手的房间＞正在游戏的房间＞已结束但是结束时间未超过5秒的房间
  // status 0待加入,1已加入,2 准备开奖(后端用状态),6已开奖结算,9已退款取消
  const waitPushMap = reactive<{
    wait: Map<number, RoomInfoType>;
    playing: Map<number, RoomInfoType>;
    final: Map<number, RoomInfoType>;
  }>({
    wait: new Map(),
    playing: new Map(),
    final: new Map(),
  });
  const waitPushList = () => {
    const { wait, playing, final } = waitPushMap;
    const waitList = Array.from(wait.values());
    const playingList = Array.from(playing.values());
    const finalList = Array.from(final.values());
    return [
      ...filterAndSort(filterAndSort(waitList)),
      ...playingList,
      ...filterAndSort(finalList),
    ];
  };
  const isMatchfilterPrice = (data: RoomInfoType) => {
    const filter = formFilter.formState;
    const minmax = filter.price_range.split(',');
    const min = Number(minmax[0]);
    const max = Number(minmax[1]) || 9999999999;
    return (
      Number(data.bet_amount || 0) >= min && Number(data.bet_amount || 0) <= max
    );
  };
  // 筛选满足条件的并排序
  const filterAndSort = (data: RoomInfoType[]) => {
    const filter = formFilter.formState;
    return data
      .filter((item) => {
        // 该游戏已结束超过5s
        const nowTime = new Date().getTime();
        const isFinish = item.result_time && nowTime - item.result_time >= 5000;
        if (isFinish) {
          waitPushMap.final.delete(item.id as number);
          modules.open.total--;
          return false;
        }
        return true;
      }) // 筛选去掉结束超过5s游戏
      .sort((a, b) =>
        filter.amount_key_desc !== 1
          ? Number(a.bet_amount || 0) - Number(b.bet_amount || 0)
          : Number(b.bet_amount || 0) - Number(a.bet_amount || 0),
      ); // 排序
  };

  const changePageSize = (curSize: number, preSize: number) => {
    if (preSize < curSize) {
      const count = curSize - preSize;
      const nullArr = new Array(count).fill(null);
      openList.value.push(...nullArr);
      createOpenGame();
    } else {
      const count = curSize - preSize;
      const deleteArr = openList.value.splice(count);
      deleteArr.forEach((el) => {
        if (!el || !el.id) return;
        if (el.status === 0) {
          waitPushMap.wait.set(el.id, el);
        } else if (el.status === 1 || el.status === 2) {
          waitPushMap.playing.set(el.id, el);
        } else if (el.status === 6) {
          waitPushMap.final.set(el.id, {
            ...el,
            result_time: new Date().getTime(),
          });
        }
      });
    }
  };

  // 创建游戏
  const createOpenGame = () => {
    // 当前开放列表没有空位
    if (openListMap.value.size >= openList.value.length) {
      const finalList = Array.from(waitPushMap.final.values());
      filterAndSort(finalList);
      return;
    }
    // 找到open列表中null对应的数据索引
    const nullIndices = openList.value
      .map((el, index) => (el === null ? index : -1))
      .filter((index) => index !== -1);
    // 当前符合条件的等待创建列表,已排序
    const pushList = waitPushList();
    // 当前没有等待插入的数据
    if (!pushList.length) return;
    // 在开放列表插入新游戏
    const minCreate = Math.min(nullIndices.length, pushList.length);
    for (let index = 0; index < minCreate; index++) {
      const nullIndex = nullIndices[index];
      const roomInfo = pushList[index];
      openList.value[nullIndex] = roomInfo;
      waitPushMap.wait.delete(roomInfo.id as number);
      waitPushMap.playing.delete(roomInfo.id as number);
      waitPushMap.final.delete(roomInfo.id as number);
    }
  };
  // 是否是自己创建
  const isSelfCreate = (data: RoomInfoType) => {
    return settingStore.userInfo?.uid === data.creator_uid;
  };
  // 展示结果和加入
  const resultAndChallenge = (data: RoomInfoType) => {
    if (!data.id) return;
    // 不在价格区间不展示
    if (!isMatchfilterPrice(data)) return;
    if (myListMap.value.has(data.id)) {
      // 推送的数据展示在我的游戏区,更新数据
      const myIndex = myList.value.findIndex((el) => el?.id === data.id);
      myList.value[myIndex] = {
        ...myList.value[myIndex],
        ...data,
      };
    } else if (openListMap.value.has(data.id)) {
      // 推送的数据展示在开放游戏区,更新数据
      const openIndex = openList.value.findIndex((el) => el?.id === data.id);
      openList.value[openIndex] = {
        ...openList.value[openIndex],
        ...data,
      };
    } else {
      if (
        !waitPushMap.wait.has(data.id) &&
        !waitPushMap.playing.has(data.id) &&
        !waitPushMap.final.has(data.id)
      ) {
        modules.open.total++;
      }
      waitPushMap.wait.delete(data.id);
      if (data.status === 6) {
        // 推送的数据展示在等待插入列表并且已有结果,删除等待加入和进行中,放在已结束
        waitPushMap.playing.delete(data.id);
        waitPushMap.final.set(data.id, {
          ...data,
          result_time: new Date().getTime(),
        });
      } else {
        // 推送的数据展示在等待插入列表正在进行中,删除等待加入,放在进行中
        waitPushMap.wait.delete(data.id);
        waitPushMap.playing.set(data.id, {
          ...data,
        });
      }
    }
    createOpenGame();
  };
  const socketCreate = async (data: any) => {
    if (!data.id) return;
    // 本人创建的
    if (isSelfCreate(data)) {
      myList.value.push(data);
      return;
    }
    // 不在价格区间不展示
    if (!isMatchfilterPrice(data)) return;
    await delay(600);
    modules.open.total++;
    waitPushMap.wait.set(data.id, data);
    nextTick(createOpenGame);
  };
  const socketCancel = (data: any) => {
    if (!data.id) return;
    // 本人创建的
    if (isSelfCreate(data)) {
      if (myListMap.value.has(data.id)) {
        const myIndex = myList.value.findIndex((el) => el?.id === data.id);
        myList.value.splice(myIndex, 1);
      }
      return;
    }
    // 不在价格区间不展示
    if (!isMatchfilterPrice(data)) return;
    modules.open.total--;
    // 删除等待插入中对应id的数据,如果等待插入中没有该数据删除也不会报错
    waitPushMap.wait.delete(data.id);
    if (openListMap.value.has(data.id)) {
      const openIndex = openList.value.findIndex((el) => el?.id === data.id);
      openList.value[openIndex] = null;
    }
  };
  const socketResult = (data: any) => resultAndChallenge(data);
  const socketChallenge = (data: any) => {
    resultAndChallenge(data);
  };

  return {
    openList,
    myList,
    historyList,
    modules,
    openListMap,
    myListMap,
    formFilter,
    getOpenList,
    getMyList,
    getHistoryList,
    createOpenGame,
    socketCreate,
    socketCancel,
    socketResult,
    socketChallenge,
    changePageSize,
  };
});
