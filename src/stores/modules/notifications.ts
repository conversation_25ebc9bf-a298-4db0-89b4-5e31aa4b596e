import { defineStore } from 'pinia';
import { useSocketStore } from './socket';
export const useNotificationsStore = defineStore('notifications', () => {
  const socketStore = useSocketStore();
  // 未读消息列表
  const unreadList = ref([]);
  // 全部消息列表
  const allList = ref([]);
  // 未读消息数量
  const unreadTotal = ref(120);
  // 背包提醒数量
  const backpack = ref(30);
  // 所有消息数量
  const allTotal = ref(150);
  const updateData = (_data: any) => {};
  watch(
    () => socketStore.socket.notifications,
    (socket) => {
      if (socket) {
        socket.on('create', (data) => {
          updateData(data);
        });
        socket.on('read', (data) => {
          updateData(data);
        });
      }
    },
  );
  return {
    unreadList,
    allList,
    unreadTotal,
    allTotal,
    backpack,
  };
});
