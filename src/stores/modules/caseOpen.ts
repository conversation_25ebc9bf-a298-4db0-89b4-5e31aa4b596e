import { defineStore } from 'pinia';
import { useSettingStore } from './setting';
import type { CaseSkinItemType, SpinnerInfoType } from '~/types/cases';
const tagsParse = (tags: MarketGoodsInfotagList) => {
  const tag: {
    [key: string]: string[];
  } = {
    shortTag: [],
    normalTag: [],
  };
  Object.values(tags).forEach((el) => {
    if (el.is_show) {
      tag.shortTag.push(el.short_title);
      tag.normalTag.push(el.title);
    }
  });
  return {
    shortTag: tag.shortTag.join(' | '),
    normalTag: tag.normalTag.join(' | '),
  };
};
export const useCaseOpenStore = defineStore('caseOpen', () => {
  const { casesApi } = useApi();
  const $router = useRouter();
  const settingStore = useSettingStore();
  const caseContain = ref<CaseSkinItemType[]>([]);
  const caseLuck = ref<CaseSkinItemType[][]>([]);
  const caseIdMap = ref<Map<number, V1MarketGoodsInfo>>(new Map());
  const caseInfo = ref<V1CasesItem>({});
  const slug = computed(() => $router.currentRoute.value.params.id);
  const spinnerInfos = useState<SpinnerInfoType[]>('list', () => [
    {
      spinnerList: [],
      finished: false,
      converted: false,
    },
  ]);
  // 重置页面数据
  const reset = () => {
    spinnerInfos.value = [
      {
        spinnerList: [],
        finished: false,
        converted: false,
      },
    ];
    caseContain.value = [];
    caseLuck.value = [];
    caseIdMap.value = new Map();
    caseInfo.value = {};
  };
  // 创建spinner列表
  const createSpinnerLists = (num: number) => {
    const spinnerL = spinnerInfos.value.length;
    if (spinnerL > num) {
      spinnerInfos.value = spinnerInfos.value.slice(0, num - spinnerL);
    } else if (spinnerL < num) {
      for (let i = 0; i < num - spinnerL; i++) {
        const { spinnerList } = useSpinnerListGenrator({
          caseContain: caseContain.value,
        });
        spinnerInfos.value.push({
          spinnerList,
          finished: false,
          converted: false,
        });
      }
    }
  };
  // 获取宝箱信息
  const getCaseInfo = async () => {
    const { data: req } = await casesApi.getCasesInfo(
      {
        slug: slug.value as string,
        lucky_size: 6,
      },
      true,
    );
    const data = req.value?.data;
    if (data) {
      // case信息
      caseInfo.value = data.case_info || {};
      const cases = new Map();
      const users = new Map();
      (data.goods || []).forEach((el) => cases.set(el.goods_id, el));
      (data.lucky_user || []).forEach((el) => users.set(el.uid, el));
      caseIdMap.value = cases;
      // 包厢内物品
      caseContain.value = (data.list || []).map((el) => {
        return {
          ...el,
          ...cases.get(el.goods_id),
        };
      });
      // luck items
      caseLuck.value = (data.lucky_goods || []).map((el) => {
        return [
          {
            ...el,
            ...cases.get(el.goods_id),
            ...users.get(el.uid),
          },
        ];
      });
      if (
        !spinnerInfos.value[0].spinnerList.length &&
        caseContain.value.length
      ) {
        const { spinnerList } = useSpinnerListGenrator({
          caseContain: caseContain.value,
        });
        spinnerInfos.value[0].spinnerList = spinnerList;
      }
    }
  };
  // 获取开箱结果
  const getCaseRes = async (
    experiment: boolean = false,
    isLevelCase: boolean = false,
    isFast: boolean = false,
    isSkip: boolean = false,
    totalPrice?: string,
  ) => {
    // is_experiment 1是,2否
    const openApi = isLevelCase ? 'openLevelCases' : 'openCases';
    const { data: req } = await casesApi[openApi]({
      slug: slug.value as string,
      num: isLevelCase ? undefined : spinnerInfos.value.length,
      client_seed: settingStore.clientSeed,
      is_experiment: experiment ? 1 : 2,
      is_fast: isFast ? 1 : 2,
      is_skip: isSkip ? 1 : 2,
      total_price: totalPrice,
    });
    const data = req.value?.data;
    if (data && data.list?.length) {
      // 替换结果
      data.list.forEach((el, index) => {
        const finished = spinnerInfos.value[index].finished;
        // 如果有上次已结束开箱,重置箱子内容
        if (finished) {
          const { spinnerList } = useSpinnerListGenrator({
            caseContain: caseContain.value,
          });
          spinnerInfos.value[index] = {
            spinnerList,
            finished: false,
            converted: false,
          };
        }
        if (el.goods_id) {
          spinnerInfos.value[index].selectItem = caseIdMap.value.get(
            el.goods_id,
          );
          spinnerInfos.value[index].res = el;
          spinnerInfos.value[index].experiment = experiment;
        }
      });
      return data.list;
    }
    return [];
  };

  return {
    spinnerInfos,
    caseInfo,
    caseLuck,
    caseContain,
    createSpinnerLists,
    getCaseInfo,
    tagsParse,
    getCaseRes,
    reset,
  };
});
