import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useBattleStore = defineStore('battle', () => {
  const {
    setBattleInitialList,
    allBattlesData,
    dismissedBattles,
    hiddenBattles,
    findBattleIndex,
    cancelBattle,
    createBattle,
    startBattle,
    startNewRound,
    joinBattle,
    leaveBattle,
    finishBattle,
    dismissBattle,
    clearBattle,
  } = useBattleList();
  const { battleApi } = useApi();
  const loading = ref(false);
  const myBattleList = ref<any>({});
  const myBattleCurrentData = ref<any>({});
  const myBattleHistoryData = ref<any>({});
  const myBattleTotalCount = ref(0);
  const activeBattleList = ref<any>({});
  const activeTotalCount = ref<any>(0);
  const totalBattlesCount = computed(
    () => activeTotalCount.value + myBattleTotalCount.value,
  );

  // 获取当前对战列表
  const getMyBattles = async (page: string, pageSize: string) => {
    try {
      loading.value = true;
      const res = await battleApi.getMyBattleList({
        page,
        page_size: pageSize,
        type: '1',
        order: '2',
      });
      if (res.data.value.code === 0) {
        myBattleCurrentData.value = res.data.value.data;
      }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const getMyBattleHistory = async (page: string, pageSize: string) => {
    try {
      loading.value = true;
      const res = await battleApi.getMyBattleList({
        page,
        page_size: pageSize,
        type: '2',
        order: '2',
        order_by: 'created_at',
      });
      if (res.data.value.code === 0) {
        myBattleHistoryData.value = res.data.value.data;
      }
    } finally {
      loading.value = false;
    }
  };
  const setActiveBattlesCount = (count: number) => {
    activeTotalCount.value = count;
  };
  const setMyBattlesCount = (count: number) => {
    myBattleTotalCount.value = count;
  };

  return {
    allBattlesData,
    dismissedBattles,
    hiddenBattles,
    totalBattlesCount,
    myBattleList,
    myBattleCurrentData,
    myBattleHistoryData,
    activeBattleList,
    myBattleTotalCount,
    activeTotalCount,
    loading,
    findBattleIndex,
    cancelBattle,
    createBattle,
    startBattle,
    startNewRound,
    joinBattle,
    leaveBattle,
    finishBattle,
    dismissBattle,
    clearBattle,
    setBattleInitialList,
    getMyBattles,
    getMyBattleHistory,
    setActiveBattlesCount,
    setMyBattlesCount,
  };
});
