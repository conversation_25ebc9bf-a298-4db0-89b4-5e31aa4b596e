import { defineStore } from 'pinia';

// 工具函数
const sortByOrder = (items: any) =>
  items.sort(
    (a: any, b: any) =>
      (a.order !== undefined ? a.order : 999) -
      (b.order !== undefined ? b.order : 999),
  );

const { depositApi } = useApi();

export const useDepositStore = defineStore('deposit', () => {
  const paymentMethods = ref<ApiBffInterfaceV1PaymentMethodsReply[]>([]);
  const cryptoExchangeRateData = ref<any>(null);
  const cryptoDepositAddresses = ref<Record<string, string | null>>({});
  const cryptoDeposits = ref<Record<string, any>>({});
  const feesEnabled = ref(true);

  const fetchPaymentMethods = async () => {
    const { data } = await depositApi.getPaymentMethods();

    const response = data.value.data;

    if (!response) {
      throw new Error('Unable to load payment methods', response);
    }
    paymentMethods.value = sortByOrder(response.data);
  };

  const fetchCryptoExchangeRateData = async () => {
    const { data } = await depositApi.getCryptoExchangeRate();
    cryptoExchangeRateData.value = data.value.data.items.reduce(
      (acc: any, item: any) => {
        acc[item.type] = item;
        return acc;
      },
      {},
    );
  };

  const fetchCryptoDepositData = async (currency: string) => {
    const { data } = await depositApi.getCryptoDepositAddress(currency);
    cryptoDepositAddresses.value[currency] = data.value.data.address || null;
    cryptoDeposits.value[currency] = data.value.data.deposits || [];
  };

  const updateCryptoDepositData = ({
    cryptoCurrency,
    depositData,
  }: {
    cryptoCurrency: string;
    depositData: any;
  }) => {
    const deposits = cryptoDeposits.value[cryptoCurrency] || [];
    const index = deposits.findIndex((d: any) => d.id === depositData.id);

    if (index > -1) {
      deposits.splice(index, 1, depositData);
    } else {
      deposits.unshift(depositData);
    }

    cryptoDeposits.value[cryptoCurrency] = deposits;
  };

  const getAllConfigs = computed(() => (country = null) => {
    const configs: any = {};
    paymentMethods.value.forEach((method: any) => {
      method.configurations?.forEach((config: any) => {
        if (country && !config.countries.includes(country)) return;

        const methodData = { ...method };
        delete methodData.configurations;
        configs[config.id] = {
          ...config,
          method: methodData,
        };
      });
    });
    return configs;
  });

  const findConfig = computed(
    () =>
      ({ configs, type, method, pid = null, configId = null }: any) => {
        let config = configId
          ? configs.find((c: any) => `${c.id}` === `${configId}`)
          : null;

        if (!config) {
          config = pid
            ? configs.find(
                (c: any) => `${c.pid}` === `${pid}` && c.provider.key === type,
              )
            : configs.find(
                (c: any) => c.provider.key === type && c.method.key === method,
              );
        }
        return config;
      },
  );

  const cryptoMethods = computed(() =>
    paymentMethods.value.filter((method: any) => method.type === 3),
  );

  const giftCardMethods = computed(() =>
    paymentMethods.value.filter((method: any) => method.type === 1),
  );

  const getPaymentMethodsForCountry = computed(() => {
    return paymentMethods.value.filter(
      (method: any) => method.configurations.length > 0 && method.type === 2,
    );
  });

  const getCryptoDepositAddress = computed(
    () => (currency: any) => cryptoDepositAddresses.value?.[currency],
  );

  // 费用相关
  const getConfigsWithFee = computed(() => (country = null) => {
    if (!feesEnabled.value) return [];

    const configs = Object.values(getAllConfigs.value(country));
    const configsWithFee = configs.filter(
      (config: any) =>
        config.fee_base_amount || config.fee_minimum || config.fee_percentage,
    );

    return configsWithFee;
  });

  const getFee = computed(
    () =>
      ({ type, method, pid = null, configId = null }: any) => {
        if (!feesEnabled.value) {
          return null;
        }

        const configsWithFee = getConfigsWithFee.value();
        return findConfig.value({
          configs: configsWithFee,
          type,
          method,
          pid,
          configId,
        });
      },
  );

  return {
    paymentMethods,
    cryptoExchangeRateData,
    cryptoDepositAddresses,
    cryptoDeposits,
    feesEnabled,
    giftCardMethods,
    cryptoMethods,
    fetchPaymentMethods,
    fetchCryptoExchangeRateData,
    fetchCryptoDepositData,
    updateCryptoDepositData,
    getAllConfigs,
    findConfig,
    getPaymentMethodsForCountry,
    getConfigsWithFee,
    getFee,
    getCryptoDepositAddress,
  };
});
