import { defineStore } from 'pinia';
export const useBackpackStore = defineStore('backpack', () => {
  const { backpackApi } = useApi();
  const backpackStatistics = ref<any>();

  const getBackpackStatistics = async () => {
    const { data: req } = await backpackApi.getBackpackStatistics();
    backpackStatistics.value = req?.value?.data;
  };
  return {
    backpackStatistics,
    getBackpackStatistics,
  };
});
