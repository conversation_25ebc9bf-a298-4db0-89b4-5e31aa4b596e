<template>
  <NuxtLayout name="default">
    <div class="flex gap-6">
      <div
        class="hidden xl:block w-[20%] h-max pt-[32px] px-4 pb-2 flex-col justify-center bg-[#151A29] rounded-lg"
      >
        <ProfileUser :user-info="userInfo" class="w-full" />
        <div class="h-[1px] bg-[#25304F] my-6"></div>
        <ProfileMenu @logout="handleLogout" />
      </div>
      <div class="w-full">
        <!-- 移动端显示 -->
        <div class="xl:hidden mb-lg">
          <ProfileUser :user-info="userInfo" class="w-full px-md mb-lg" />
          <ProfileMenu @logout="handleLogout" />
        </div>
        <slot />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useSettingStore } from '~/stores/modules/setting';

const settingStore = useSettingStore();
const userInfo = computed<ApiV1UserInfoGet200ResponseData>(
  () => settingStore.userInfo ?? ({} as ApiV1UserInfoGet200ResponseData),
);
const handleLogout = () => {
  settingStore.signout();
};
</script>
