<template>
  <NuxtLayout name="profile">
    <div class="bg-[#151A29] px-lg max-sm:px-3 py-[26px] rounded-lg">
      <p class="font-bold text-[#7D90CA]">{{ t('my_coin_quantity') }}</p>
      <div class="flex items-center gap-[6px] mt-[16px]">
        <BaseIcon name="gold" class="w-[24px] h-[24px]" filled></BaseIcon>
        <span class="text-[#fff]">
          {{ userInfo?.balance.balance }}
        </span>
      </div>
    </div>
    <div class="mt-lg pt-md relative rounded-lg bg-[#151A29] px-lg max-sm:p-3">
      <ul class="flex gap-xl sm:gap-sm">
        <li
          v-for="tab in tabs"
          :key="tab.value"
          class="tab-item"
          :class="getTabClass(tab)"
          @click="handleSelectTab(tab)"
        >
          {{ t(tab.title) }}
        </li>
      </ul>
      <slot />
    </div>
    <div id="coin-purse-footer"></div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import type { Tab } from '@/composables/useTabRouteConfig';

const settingStore = useSettingStore();
const { t } = useI18n();
const router = useRouter();

const tabConfig = useTabRouteConfig([
  {
    title: 'deposit',
    value: 'deposit',
    route: 'profile/deposit',
    parent: 'deposit',
  },
  {
    title: 'coin_record',
    value: 'coin-record',
    route: 'profile/coin-record',
    parent: 'deposit',
  },
]);

const { tabs, selectedTab } = tabConfig;

const userInfo = computed(() => settingStore.userInfo);

const getTabClass = (tab: Tab) => ({
  'border-b-2 border-[#F8B838] text-white': selectedTab.value === tab.value,
  'text-[#7D90CA]': selectedTab.value !== tab.value,
});

const handleSelectTab = (tab: Tab) => {
  selectedTab.value = tab.value;
  router.push(`/${tab.route}`);
};

watch(
  () => router.currentRoute.value,
  () => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.tab-item {
  @apply px-[4px] py-[8px]  font-bold cursor-pointer;
}
</style>
