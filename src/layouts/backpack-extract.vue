<template>
  <NuxtLayout name="backpack">
    <div class="mt-lg relative">
      <ul class="flex gap-xl sm:gap-sm md:w-[50%] w-[30%]">
        <ClientOnly>
          <n-badge
            v-for="tab in tabs"
            :key="tab.value"
            :value="badge[tab?.badge as keyof typeof badge] || 0"
            :offset="[20, 2]"
            :max="99"
          >
            <li
              :key="tab.value"
              class="tab-item whitespace-nowrap"
              :class="getTabClass(tab)"
              @click="handleSelectTab(tab)"
            >
              <span>{{ t(tab.title) }}</span>
              <span v-if="showTotal(tab)">
                ({{
                  statistics && tab.total_key ? statistics[tab.total_key] : 0
                }})
              </span>
            </li>
          </n-badge>
        </ClientOnly>
      </ul>
      <slot />
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useBackpackStore } from '~/stores/modules/backpack';
import { useAppStore } from '~/stores/modules/app';
import type { Tab } from '@/composables/useTabRouteConfig';

const { t } = useI18n();
const router = useRouter();
const backpackStore = useBackpackStore();
const appStore = useAppStore();

const tabConfig = useTabRouteConfig([
  {
    title: 'extracting_progress',
    value: 'progress',
    total_key: 'extracting_total',
    route: 'extract/progress',
    badge: 'extracting',
  },
  {
    title: 'extracting_records',
    value: 'record',
    route: 'extract/record',
    badge: 'extract',
  },
]);

const { tabs, selectedTab } = tabConfig;

const statistics = computed(() => backpackStore.backpackStatistics);

const badge = computed(() => {
  return {
    extracting: appStore.extractingTotal,
    extract: appStore.extractTotal,
  };
});

const getTabClass = (tab: Tab) => ({
  'border-b-2 border-[#F8B838] text-white': selectedTab.value === tab.value,
  'text-[#7D90CA]': selectedTab.value !== tab.value,
});

const showTotal = (tab: Tab) =>
  tab?.total_key !== undefined && statistics.value?.[tab.total_key];

const handleSelectTab = (tab: Tab) => {
  selectedTab.value = tab.value;
  router.push(`/backpack/${tab.route}`);
  if (tab.badge && badge.value[tab?.badge as keyof typeof badge.value] > 0) {
    appStore.getMetadata();
  }
};

watch(
  () => router.currentRoute.value,
  () => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.tab-item {
  @apply text-center  py-[8px]  font-bold cursor-pointer;
}
</style>
