<template>
  <NuxtLayout name="default">
    <div class="h-screen flex flex-col mx-auto gap-[20px] bg-[#f7f8fa]">
      <div class="bg-white">
        <div
          class="h-[60px] flex justify-between px-[50px] items-center max-w-[1440px] mx-auto"
        >
          <a class="mr-8 shrink-0">
            <img src="/imgs/home_logo.png" alt="logo" class="w-[178.5px]" />
          </a>
          <LiveMngUser />
        </div>
      </div>

      <div
        class="flex-1 flex overflow-hidden gap-[20px] max-w-[1440px] relative left-1/2 -translate-x-1/2"
      >
        <LiveMngMenu />
        <div class="flex-1 overflow-auto min-h-0 hide-scrollbar text-[#1D2129]">
          <slot />
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import { useLiveMngStore } from '~/stores/modules/liveMng';
const settingStore = useSettingStore();
const liveMngStore = useLiveMngStore();
settingStore.getUserInfo(true);
liveMngStore.getStreamerPermission(true);
</script>
<style lang="scss">
body {
  background-color: #f7f8fa;
}
.custom-dialog .n-dialog__close {
  color: rgba(102, 102, 102, 1);
  margin: 24px;
}
</style>
