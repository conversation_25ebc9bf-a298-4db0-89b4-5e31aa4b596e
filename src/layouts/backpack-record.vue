<template>
  <NuxtLayout name="backpack">
    <div class="mt-lg relative">
      <ul class="flex gap-xl sm:gap-sm md:w-[50%] w-[30%]">
        <li
          v-for="tab in tabs"
          :key="tab.value"
          class="tab-item whitespace-nowrap"
          :class="getTabClass(tab)"
          @click="handleSelectTab(tab)"
        >
          {{ t(tab.title) }}
        </li>
      </ul>
      <slot />
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import type { Tab } from '@/composables/useTabRouteConfig';

const { t } = useI18n();
const router = useRouter();

const tabConfig = useTabRouteConfig([
  {
    title: 'obtain_records',
    value: 'obtain',
    route: 'record/obtain',
  },
  {
    title: 'lost_records',
    value: 'lost',
    route: 'record/lost',
  },
]);

const { tabs, selectedTab } = tabConfig;

const getTabClass = (tab: Tab) => ({
  'border-b-2 border-[#F8B838] text-white': selectedTab.value === tab.value,
  'text-[#7D90CA]': selectedTab.value !== tab.value,
});

const handleSelectTab = (tab: Tab) => {
  selectedTab.value = tab.value;
  router.push(`/backpack/${tab.route}`);
};

watch(
  () => router.currentRoute.value,
  () => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.tab-item {
  @apply text-center  py-[8px]  font-bold cursor-pointer;
}
</style>
