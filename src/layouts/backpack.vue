<template>
  <NuxtLayout name="default">
    <div class="flex-layout">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
        <div class="flex gap-md flex-wrap">
          <ClientOnly>
            <n-badge
              v-for="tab in tabs"
              :key="tab.value"
              :value="badge[tab?.badge as keyof typeof badge] || 0"
              :offset="[16, -3]"
              :max="99"
            >
              <n-button
                :key="tab.value"
                class="px-[12px] h-[40px] font-bold"
                v-bind="getButtonProps(tab)"
                @click="handleSelectTab(tab)"
              >
                <div class="sm:flex items-center block">
                  <span>{{ t(tab.title) }}</span>
                  <span v-if="showTotal(tab)">
                    ({{
                      statistics[
                        tab.total_key as keyof typeof statistics.value
                      ]
                    }})
                  </span>
                </div>
              </n-button>
            </n-badge>
          </ClientOnly>
        </div>
        <div class="right w-full sm:w-auto mt-lg sm:mt-0 flex gap-[40px]">
          <div class="flex items-center gap-2" @click="handlePrecautions">
            <svgo-precautions
              filled
              class="w-[18px] h-[18px] mb-0 cursor-pointer"
            ></svgo-precautions>
            <n-button text type="primary" color="#7D90CA" class="font-bold">
              {{ t('precautions') }}
            </n-button>
          </div>

          <div
            class="flex items-center gap-2"
            @click="handleSteamBindSettingDialog"
          >
            <svgo-set filled class="w-[18px] h-[18px] mb-0"></svgo-set>
            <n-button
              text
              type="primary"
              color="#7D90CA"
              class="font-bold uppercase"
            >
              {{ t('steam_settings') }}
            </n-button>
          </div>
        </div>
      </div>
      <div class="flex-layout">
        <slot />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { type ButtonProps } from 'naive-ui';
import { useBackpackStore } from '~/stores/modules/backpack';
import { useAppStore } from '~/stores/modules/app';
import type { Tab } from '@/composables/useTabRouteConfig';
const { t } = useI18n();
const router = useRouter();
const backpackStore = useBackpackStore();
const appStore = useAppStore();
const { handleSteamBindSettingDialog } = useSteamSettingsDialogs();
const tabConfig = useTabRouteConfig([
  {
    title: 'my_skin',
    value: 'skin',
    route: 'skin',
    total_key: 'skin_total',
    badge: 'skin',
  },
  {
    title: 'extracting',
    value: 'progress',
    route: 'extract/progress',
    total_key: 'extracting_total',
    badge: 'extract',
  },
  {
    title: 'backpack_record',
    value: 'obtain',
    route: 'record/obtain',
  },
]);

const { tabs, selectedTab } = tabConfig;

const badge = computed(() => {
  return {
    skin: appStore.skinTotal,
    extract: appStore.backpackTotal,
  };
});

const statistics = computed(() => backpackStore.backpackStatistics);

const getButtonProps = (tab: Tab): ButtonProps => {
  if (selectedTab.value === tab.value) {
    return {
      color: '#F8B838',
      ghost: true,
    };
  }
  return {
    color: '#151A29',
    textColor: '#7D90CA',
  };
};

const showTotal = (tab: Tab) =>
  tab?.total_key && statistics.value?.[tab.total_key];

const handleSelectTab = (tab: Tab) => {
  selectedTab.value = tab.value;
  navigateTo(`/backpack/${tab.route}`);
};

// 背包注意事项
const handlePrecautions = () => {
  const { openConfirm } = useDialogPromptsConfirm('precautions');
  openConfirm({
    title: t('precautions'),
    content: t('precautions_description'),
    contentClass: '!mb-0',
    enablePrompts: true,
    hideConfirmBtn: true,
    cancelText: t('i_understand'),
  });
};

watch(
  () => router.currentRoute.value,
  () => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>
