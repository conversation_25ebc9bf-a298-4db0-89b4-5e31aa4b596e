import type { AutoCompleteProps, SelectProps } from 'naive-ui';
import LiveHomeSvgo from '~/assets/icons/live-home.svg';
import LiveHomeASvgo from '~/assets/icons/live-home-a.svg';
import EarningsSvgo from '~/assets/icons/earnings.svg';
import EarningsASvgo from '~/assets/icons/earnings-a.svg';
import WithdrawalsSvgo from '~/assets/icons/withdrawals.svg';
import WithdrawalsASvgo from '~/assets/icons/withdrawals-a.svg';
import PromotionSvgo from '~/assets/icons/promotion.svg';
import PromotionASvgo from '~/assets/icons/promotion-a.svg';
import { Permission, LiveMngPage } from '~/types/liveMng';
type SearchThemeOverridesType = NonNullable<
  AutoCompleteProps['themeOverrides']
>;
type SelectThemeOverridesType = NonNullable<SelectProps['themeOverrides']>;

// 成员邀请表单配置
export const inviteFormConfig = {
  fields: [
    {
      key: 'uid',
      label: 'UID',
      type: 'input' as const,
      placeholder: "Please enter the invitee's UID",
      required: true,
    },
    {
      key: 'remarks',
      label: 'Remarks',
      type: 'textarea' as const,
      placeholder: 'Please enter remarks (optional)',
      props: {
        size: 'small',
        autosize: { minRows: 3, maxRows: 5 },
      },
    },
  ],
};
// 成员编辑配置
export const editFormConfig = {
  fields: [
    {
      key: 'remarks',
      label: 'Remarks',
      type: 'textarea' as const,
      placeholder: 'Please enter remarks (optional)',
      props: {
        size: 'small',
        autosize: { minRows: 3, maxRows: 5 },
      },
    },
  ],
};

export const searchThemeOverrides: SearchThemeOverridesType = {
  peers: {
    Input: {
      suffixTextColor: '#4E5969',
      border: 'none',
      color: '#F7F8FA',
      placeholderColor: '#86909C',
    },
  },
};
export const selectThemeOverrides: SelectThemeOverridesType = {
  peers: {
    InternalSelection: {
      border: 'none',
      color: '#F7F8FA',
      placeholderColor: '#86909C',
    },
  },
};
export const DEFAULT_FILTER_PARAMS = {
  search: '',
  rebate_status: null,
} as const;
export const searchSchema = {
  key: 'search',
  type: 'string',
  placeholder: 'Search ID',
  themeOverrides: searchThemeOverrides,
  component: 'n-input-search',
  suffix: true,
  auth: true,
  value: DEFAULT_FILTER_PARAMS.search,
  listeners: {
    'complete:select': true,
  },
};
export const selectSchema = {
  key: 'rebate_status',
  type: 'select',
  component: 'n-select',
  themeOverrides: selectThemeOverrides,
  value: DEFAULT_FILTER_PARAMS.rebate_status,
  placeholder: 'All States',
  consistentMenuWidth: false,
};

export const permissionOptions = [
  { label: 'No permission to view', value: Permission.NONE },
  { label: 'View only own', value: Permission.OWN },
  { label: 'View all', value: Permission.ALL },
];

export const pageMap: Record<string, LiveMngPage> = {
  home: LiveMngPage.HOME,
  member: LiveMngPage.MEMBER,
  promotion: LiveMngPage.PROMOTION,
  earnings: LiveMngPage.EARNING,
  withdrawals: LiveMngPage.WITHDRAWALS,
};
interface PageListItem {
  key: LiveMngPage;
  label: string;
  disabled?: boolean;
}

export const pageList: PageListItem[] = [
  { key: LiveMngPage.HOME, label: 'Home', disabled: true },
  { key: LiveMngPage.MEMBER, label: 'Member List', disabled: true },
  { key: LiveMngPage.PROMOTION, label: 'Promotion Records' },
  { key: LiveMngPage.EARNING, label: 'Rebate Details' },
  { key: LiveMngPage.WITHDRAWALS, label: 'Withdrawals', disabled: true },
];

export const menuList: Tab[] = [
  {
    title: 'Home',
    value: 'home',
    route: '',
    icon: LiveHomeSvgo,
    action_icon: LiveHomeASvgo,
  },
  {
    title: 'Member List',
    value: 'member',
    route: 'member',
    icon: EarningsSvgo,
    action_icon: EarningsASvgo,
  },
  {
    title: 'Promotion Records',
    value: 'promotion',
    route: 'promotion',
    icon: PromotionSvgo,
    action_icon: PromotionASvgo,
  },
  {
    title: 'Rebate Details',
    value: 'earnings',
    route: 'earnings',
    icon: EarningsSvgo,
    action_icon: EarningsASvgo,
  },
  {
    title: 'Withdrawals',
    value: 'withdrawals',
    route: 'withdrawals',
    icon: WithdrawalsSvgo,
    action_icon: WithdrawalsASvgo,
  },
];

export const LEAVE_GUILD_CONTENT =
  'After leaving the guild, you will no longer contribute rebates to the guild. Additionally, users you promoted will be converted to regular referrals based on their invitation time. Please confirm with the administrator before proceeding. Are you sure you want to leave the guild now?';

export const DISMISS_CONTENT =
  "This member's rebates will no longer be available to the guild after removal. Confirm kicking this member?";

export const INVITE_CONTENT =
  "The invitation requires the invitee's acceptance to complete membership. It is valid for only 3 days; please resend if expired.";
