/**
 * 流水类型映射
 */
export const TRADE_TYPE_MAP = {
  1: 'case',
  2: 'case_battles',
  3: 'coinflip',
  4: 'exchange',
  5: 'free_case',
  6: 'bonus_cases',
  7: 'referrals',
  8: 'convert',
  9: 'crypto_deposit',
  10: 'gift_card_deposit',
  11: 'bank_deposit',
  12: 'crypto_withdraw',
  13: 'other',
};

export type TradeTypeMapKey = keyof typeof TRADE_TYPE_MAP;
export type TradeTypeMapValue = (typeof TRADE_TYPE_MAP)[TradeTypeMapKey];

/**
 * 流水类型提示描述
 */
export const FLOW_TYPE_DESCRIPTION = {
  2: 'case_battles',
  3: 'case_battles',
  4: 'case_battles',
  5: 'case_battles',
  6: 'coinflip',
  7: 'coinflip',
  8: 'coinflip',
  9: 'coinflip',
  10: 'exchange_skin',
  12: 'free_case',
  13: 'referrals',
};

export type FlowTypeDescriptionKey = keyof typeof FLOW_TYPE_DESCRIPTION;
export type FlowTypeDescriptionValue =
  (typeof FLOW_TYPE_DESCRIPTION)[FlowTypeDescriptionKey];
