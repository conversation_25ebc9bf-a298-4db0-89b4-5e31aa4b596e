/**
 * 交易记录类型常量
 * 开启宝箱 = 1; 创建宝箱对战 = 2; 加入宝箱对战 = 3; 取消宝箱对战 = 4; 退出宝箱对战 = 5;
 * 创建猜硬币 = 6; 加入猜硬币 = 7; 赢了猜硬币 = 8; 取消猜硬币 = 9; 兑换皮肤 = 10;
 * 开启免费宝箱 = 11; 开启等级宝箱 = 12; 推荐 = 13; 转换皮肤 = 14; 加密货币充值 = 15;
 * 礼品卡充值 = 16; 银行卡充值 = 17; 取消银行卡充值 = 18; 加密货币提现 = 19; 加密货币提现失败 = 20;
 * 充值异常 = 21; 充值异常 = 22; 普通硬币发放 = 23; 充值硬币发放 = 24; cdk兑换 = 25; cdk兑换失败回滚 = 26;
 */
export const RECORD_TYPES = {
  OPEN_CASE: { id: 1, name: 'OPEN_CASE' },
  CREATE_CASE_BATTLE: { id: 2, name: 'CREATE_CASE_BATTLE' },
  JOIN_CASE_BATTLE: { id: 3, name: 'JOIN_CASE_BATTLE' },
  CANCEL_CASE_BATTLE: { id: 4, name: 'CANCEL_CASE_BATTLE' },
  QUIT_CASE_BATTLE: { id: 5, name: 'QUIT_CASE_BATTLE' },
  CREATE_COINFLIP: { id: 6, name: 'CREATE_COINFLIP' },
  JOIN_COINFLIP: { id: 7, name: 'JOIN_COINFLIP' },
  WIN_COINFLIP: { id: 8, name: 'WIN_COINFLIP' },
  CANCEL_COINFLIP: { id: 9, name: 'CANCEL_COINFLIP' },
  EXCHANGE_SKIN: { id: 10, name: 'EXCHANGE_SKIN' },
  OPEN_FREE_CASE: { id: 11, name: 'OPEN_FREE_CASE' },
  OPEN_LEVEL_CASE: { id: 12, name: 'OPEN_LEVEL_CASE' },
  CLAIM_REFERRAL: { id: 13, name: 'CLAIM_REFERRAL' },
  CONVERT_SKIN: { id: 14, name: 'CONVERT_SKIN' },
  CRYPTO_DEPOSIT: { id: 15, name: 'CRYPTO_DEPOSIT' },
  GIFTCARD_DEPOSIT: { id: 16, name: 'GIFTCARD_DEPOSIT' },
  BANK_DEPOSIT: { id: 17, name: 'BANK_DEPOSIT' },
  CANCEL_BANK_DEPOSIT: { id: 18, name: 'CANCEL_BANK_DEPOSIT' },
  CRYPTO_WITHDRAW: { id: 19, name: 'CRYPTO_WITHDRAW' },
  CRYPTO_WITHDRAW_FAILED: { id: 20, name: 'CRYPTO_WITHDRAW_FAILED' },
  RECHARGE_ISSUE: { id: 21, name: 'RECHARGE_ISSUE' },
  RECHARGE_ISSUE_FAILED: { id: 22, name: 'RECHARGE_ISSUE_FAILED' },
  MngGrantNormalCoin: { id: 23, name: 'OTHER' },
  MngGrantRechargeCoin: { id: 24, name: 'OTHER' },
  CDK_REDEEM_CODE: { id: 25, name: 'CDK_REDEEM_CODE' },
  CDK_ROLLBACK: { id: 26, name: 'CDK_ROLLBACK' },
};

export type RecordTypeKey = keyof typeof RECORD_TYPES;
export type RecordTypeValue = (typeof RECORD_TYPES)[RecordTypeKey]['name'];
export type RecordTypeId = (typeof RECORD_TYPES)[RecordTypeKey]['id'];
export type RecordType = RecordTypeValue;

/**
 * 记录类型名称到ID的映射
 */
export const recordNameToId: Record<RecordTypeValue, RecordTypeId> =
  Object.entries(RECORD_TYPES).reduce(
    (acc, [_, value]) => ({
      ...acc,
      [value.name]: value.id,
    }),
    {} as Record<RecordTypeValue, RecordTypeId>,
  );

/**
 * 记录类型ID到名称的映射
 */
export const recordIdToName: Record<RecordTypeId, RecordTypeValue> =
  Object.entries(RECORD_TYPES).reduce(
    (acc, [_, value]) => ({
      ...acc,
      [value.id]: value.name,
    }),
    {} as Record<RecordTypeId, RecordTypeValue>,
  );

/**
 * 流水描述
 */
export const RECORD_LABEL_MAP = (): Record<RecordType, string> => {
  const { $td } = useNuxtApp();
  return {
    OPEN_CASE: $td('cases_record', 'Cases Record') + 'ID：',
    CREATE_CASE_BATTLE: $td('case_battles', 'Case Battles') + 'ID：',
    JOIN_CASE_BATTLE: $td('case_battles', 'Case Battles') + 'ID：',
    CANCEL_CASE_BATTLE: $td('case_battles', 'Case Battles') + 'ID：',
    QUIT_CASE_BATTLE: $td('case_battles', 'Case Battles') + 'ID：',
    CREATE_COINFLIP: $td('coinflip', 'Coinflip') + 'ID：',
    JOIN_COINFLIP: $td('coinflip', 'Coinflip') + 'ID：',
    WIN_COINFLIP: $td('coinflip', 'Coinflip') + 'ID：',
    CANCEL_COINFLIP: $td('coinflip', 'Coinflip') + 'ID：',
    EXCHANGE_SKIN: $td('skin', 'Skin') + 'ID：',
    OPEN_FREE_CASE: $td('cases_record', 'Cases Record') + 'ID：',
    OPEN_LEVEL_CASE: $td('cases_record', 'Cases Record') + 'ID：',
    CLAIM_REFERRAL: $td('claim_referral_rewards', 'Claim Referral Rewards'),
    CONVERT_SKIN: $td('skin', 'Skin') + 'ID：',
    CRYPTO_DEPOSIT: $td('deposit_record', 'Deposit Record') + 'ID：',
    GIFTCARD_DEPOSIT: $td('deposit_record', 'Deposit Record') + 'ID：',
    BANK_DEPOSIT: $td('deposit_record', 'Deposit Record') + 'ID：',
    CANCEL_BANK_DEPOSIT: $td('deposit_record', 'Deposit Record') + 'ID：',
    CRYPTO_WITHDRAW: $td('withdraw_record', 'Withdraw Record') + 'ID：',
    CRYPTO_WITHDRAW_FAILED: $td('withdraw_record', 'Withdraw Record') + 'ID：',
    RECHARGE_ISSUE: $td('deposit_record', 'Deposit Record') + 'ID：',
    RECHARGE_ISSUE_FAILED: $td('deposit_record', 'Deposit Record') + 'ID：',
    CDK_REDEEM_CODE: $td('redeem_code', 'Redeem Code') + '：',
    CDK_ROLLBACK: $td('redeem_code', 'Redeem Code') + '：',
  };
};
