// Cookie 和 Storage 键
export const STORAGE_KEYS = {
  COOKIES: {
    TOKEN: 'token',
    ERROR_CODE: 'err_code',
    ERROR_MSG: 'err_msg',
    I18N_REDIRECTED: 'i18n_redirected',
  },
  LOCAL_STORAGE: {
    BIND_STATUS: 'bind_status',
  },
  TURNSTILE: {
    VERIFIED: 'turnstile_verified',
    EXPIRY: 'turnstile_expiry',
  },
} as const;

export const DOMAIN_CONFIG = {
  DOMAIN: 'skin-bucks.com',
  IS_CHAT_MOCKED: false,
  PREVIEW_STORAGE_URL: '',
  STEAM_CDN_URL: '',
  CLOUDFLARE_CDN_URL: '',
  // 交易链接获取地址
  TRADE_URL: (steamId: string) => {
    if (!steamId) {
      return 'https://steamcommunity.com';
    }
    return `https://steamcommunity.com/profiles/${steamId}/tradeoffers/privacy#trade_offer_access_url`;
  },
  // steam 用户主页
  STEAM_USER_PROFILE: (steamId: string) => {
    if (!steamId) {
      return '';
    }
    return `https://steamcommunity.com/profiles/${steamId}`;
  },
  AUCTION_BASE_DURATION: 3,
  RECOMMENDED_PRICE_FACTOR: 0.06,
  FAILED_AUCTION_FEE_PERCENT: 0.01,
  FAILED_AUCTION_MIN_FEE: 50,
  KYC_COIN_WITHDRAW_LIMIT: 4500,
  SERVICE: 'csgoempire',
  COINFLIP_HOUSE_EDGE: 0.005,
  CASES_HOUSE_EDGE: 0.07,
};
