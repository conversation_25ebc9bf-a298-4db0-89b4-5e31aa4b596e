// header
// 左上
import PromoCode from '~/components/dialog/PromoCode.vue';
export const Cases = (t: any) => {
  return [
    {
      name: 'Bonus cases',
      label: t('bonus_cases'),
      path: '/profile/bonus-cases',
      needLogin: true,
      textColor: '#7D90CA',
    },
    {
      name: 'Referrals',
      label: t('referrals'),
      path: '/profile/referrals',
      needLogin: true,
      textColor: '#7D90CA',
    },
    {
      name: 'Promo Code',
      label: t('redeem_code'),
      path: 'dialog',
      component: PromoCode,
      needLogin: true,
      textColor: '#7D90CA',
    },
  ];
};
// 右上
export const Abouts = (t: any) => {
  return [
    {
      name: 'Fairness',
      label: t('fairness'),
      path: '/fairness',
      textColor: '#7D90CA',
    },
    {
      name: 'About',
      label: t('about'),
      path: '/document/about',
      textColor: '#7D90CA',
    },
    {
      name: 'Language',
      label: t('language'),
      path: 'language',
      textColor: '#7D90CA',
    },
  ];
};
// 左下
export const Games = (t: any) => {
  return [
    {
      name: 'Home',
      label: t('home'),
      icon: 'home',
      path: '/',
    },
    // {
    //   name: 'ROULETTE',
    //   label: t('轮盘'),
    //   path: '/roulette',
    // },
    {
      name: 'CASES',
      label: t('cases'),
      icon: 'cases',
      path: '/cases',
      restrictIP: true,
    },
    {
      name: 'CASE BATTLES',
      label: t('case_battles'),
      icon: 'case-battles',
      path: '/case-battles',
      restrictIP: true,
    },
    // {
    //   name: 'COINFLIP',
    //   label: t('coinflip'),
    //   icon: 'coinflip',
    //   path: '/coinflip',
    //   restrictIP: true,
    // },
    {
      name: 'ROLL',
      label: t('roll'),
      icon: 'rollroom',
      path: '/rollroom',
    },
    {
      name: 'Exchange',
      label: t('exchange'),
      icon: 'exchange',
      path: '/exchange',
    },
  ];
};
// 右下
export const Users = (t: any) => {
  return [
    {
      badge: 'notification',
      name: 'Notifications',
      label: t('notifications'),
      path: '/notifications',
      icon: 'notification',
      needLogin: true,
      primary: true,
      color: '#262D49',
      textColor: '#7D90CA',
      tooltip: t('message'),
    },
    {
      badge: 'backpack',
      name: 'Backpack',
      label: t('backpack'),
      path: '/backpack',
      icon: 'backpack',
      needLogin: true,
      primary: true,
      color: '#262D49',
      textColor: '#7D90CA',
      tooltip: t('backpack'),
    },
    {
      name: 'Coins',
      label: t('coins'),
      path: '/profile/deposit',
      icon: 'gold',
      needLogin: true,
      primary: true,
      color: '#262D49',
    },
    {
      name: 'User',
      label: t('profile'),
      path: '/profile',
      needLogin: true,
    },
    {
      name: 'SIGN IN 丨 SIGN UP',
      label: t('sign_in', 'SIGN IN') + '丨' + t('sign_up', 'SIGN UP'),
      path: 'login',
      needLogin: false,
      primary: true,
      textColor: '#0A0D14',
    },
  ];
};

// 划上头像 btn
export const UsersPop = (t: any) => {
  return [
    {
      name: 'Profile',
      label: t('profile'),
      path: '/profile',
      textColor: '#7D90CA',
    },
    {
      name: 'Coin Purse',
      label: t('coin_purse'),
      path: '/profile/deposit',
      textColor: '#7D90CA',
    },
    {
      name: 'Bonus Casesfile',
      label: t('bonus_cases'),
      path: '/profile/bonus-cases',
      textColor: '#7D90CA',
    },
    {
      name: 'Referrals',
      label: t('referrals'),
      path: '/profile/referrals',
      textColor: '#7D90CA',
    },
    {
      name: 'Log out',
      label: t('log_out'),
      path: 'logout',
      textColor: '#7D90CA',
    },
  ];
};

// footer
export const FooterInfo = (t: any) => {
  return [
    {
      title: 'INFO',
      label: t('info'),
      lists: [
        {
          title: 'ABOUT',
          label: t('about_us'),
          link: '/document/about',
        },
        {
          title: 'PROVABLY FAIR',
          label: t('provably_fair'),
          link: '/fairness',
        },
        // {
        //   title: 'SUPPORT',
        //   label: t('support'),
        //   link: '/support',
        // },
      ],
    },
    {
      title: 'AGREEMENT TERMS',
      label: t('agreement_terms'),
      lists: [
        {
          title: 'TERMS OF SERVICE',
          label: t('terms_of_service'),
          link: '/document/agreement',
        },
        // {
        //   title: 'REFUND POLICY',
        //   label: t('refund_policy'),
        //   link: '/',
        // },
        {
          title: 'PRIVACY POLOCY',
          label: t('privacy_policy'),
          link: '/document/privacy',
        },
      ],
    },
  ];
};
