/**
 * 背包记录类型
 * 1-金币兑换入库; 2-开箱入库; 3-对战入库; 4-roll房入库;
 * 5-开箱福利入库; 6-等级宝箱入库; 7-系统发放(其他类型); 8-cdk兑换;
 * 11-提货出库; 12-回收出库;
 */
export const BACKPACK_RECORD_TYPES = {
  EXCHANGE: 1,
  CASES: 2,
  CASES_BATTLES: 3,
  ROLL: 4,
  GIFT: 5,
  LEVEL_BOX: 6,
  SYSTEM: 7,
  CDK: 8,
  DELIVERY: 11,
  RECYCLE: 12,
} as const;

export type BackpackRecordType =
  (typeof BACKPACK_RECORD_TYPES)[keyof typeof BACKPACK_RECORD_TYPES];

/**
 * 背包记录类型标签映射
 */
export const BACKPACK_RECORD_LABEL_MAP = {
  [BACKPACK_RECORD_TYPES.EXCHANGE]: 'consuming_coins',
  [BACKPACK_RECORD_TYPES.CASES]: 'cases_record_id',
  [BACKPACK_RECORD_TYPES.CASES_BATTLES]: 'cases_battles_id',
  [BACKPACK_RECORD_TYPES.ROLL]: 'roll_id',
  [BACKPACK_RECORD_TYPES.GIFT]: 'cases_record_id',
  [BACKPACK_RECORD_TYPES.LEVEL_BOX]: 'cases_record_id',
  [BACKPACK_RECORD_TYPES.SYSTEM]: 'other',
  [BACKPACK_RECORD_TYPES.CDK]: 'redeem_code',
  [BACKPACK_RECORD_TYPES.DELIVERY]: 'orders_id',
  [BACKPACK_RECORD_TYPES.RECYCLE]: 'obtain_coins',
} as const;

/**
 * 获取背包记录类型标签
 */
export const getBackpackRecordTypeLabel = (type?: number): string => {
  const { t } = useI18n();
  switch (type) {
    case BACKPACK_RECORD_TYPES.EXCHANGE:
      return t('exchange');
    case BACKPACK_RECORD_TYPES.CASES:
      return t('cases');
    case BACKPACK_RECORD_TYPES.LEVEL_BOX:
      return t('bonus_cases');
    case BACKPACK_RECORD_TYPES.GIFT:
      return t('free_case');
    case BACKPACK_RECORD_TYPES.CASES_BATTLES:
      return t('case_battles');
    case BACKPACK_RECORD_TYPES.ROLL:
      return t('roll');
    case BACKPACK_RECORD_TYPES.SYSTEM:
      return t('other');
    case BACKPACK_RECORD_TYPES.RECYCLE:
      return t('convert');
    case BACKPACK_RECORD_TYPES.DELIVERY:
      return t('extracting');
    case BACKPACK_RECORD_TYPES.CDK:
      return t('redeem_code', 'Redeem Code');
    default:
      return t('other');
  }
};
