/**
 * <PERSON><PERSON> 配置
 */
import { STORAGE_KEYS } from './index';

export const COOKIE_MAX_AGE = {
  AUTH: 60 * 60 * 24 * 31,
  PREFERENCES: 60 * 60 * 24 * 365,
  TEMPORARY: 60 * 60 * 24,
  SESSION: undefined,
} as const;

export const COOKIE_CONFIGS = {
  AUTH: {
    maxAge: COOKIE_MAX_AGE.AUTH,
    httpOnly: false,
    secure: true,
    sameSite: 'strict',
  },
} as const;

// Cookie 类型
export const COOKIE_TYPE_MAP = {
  [STORAGE_KEYS.COOKIES.TOKEN]: 'AUTH',
  [STORAGE_KEYS.COOKIES.I18N_REDIRECTED]: 'PREFERENCES',
  [STORAGE_KEYS.COOKIES.ERROR_CODE]: 'TEMPORARY',
  [STORAGE_KEYS.COOKIES.ERROR_MSG]: 'TEMPORARY',
} as const;
