interface GemMapping {
  fullName: string;
  shortName?: string;
  color: string;
  icon?: true;
}

export const WEAR_NAMES: Record<string, string> = {
  'Factory New': 'FN',
  'Minimal Wear': 'MW',
  'Field-Tested': 'FT',
  'Well-Worn': 'WW',
  'Battle-Scarred': 'BS',
};

export const RARITY_RANGES = [
  {
    min: 0,
    max: 99,
    color: '#85CCFF',
  },
  {
    min: 100,
    max: 499,
    color: '#136BEF',
  },
  {
    min: 500,
    max: 2499,
    color: '#7E41FF',
  },
  {
    min: 2500,
    max: 9999,
    color: '#DF2FF4',
  },
  {
    min: 10000,
    max: 99999,
    color: '#EB4C4B',
  },
  {
    min: 100000,
    max: null,
    color: '#E1A826',
  },
];

// 玄学映射
export const GEM_PHASE_MAPPING: Record<string, GemMapping> = {
  Ruby: {
    fullName: '红宝石',
    color: 'FD4058',
    icon: true,
  },
  Sapphire: {
    fullName: '蓝宝石',
    color: '4269E3',
    icon: true,
  },
  Emerald: {
    fullName: '绿宝石',
    color: '37E06B',
    icon: true,
  },
  'Black Pearl': {
    fullName: '黑珍珠',
    color: '494963',
    icon: true,
  },
  P1: {
    fullName: 'Phase 1',
    shortName: 'P1',
    color: 'fff',
  },
  P2: {
    fullName: 'Phase 2',
    shortName: 'P2',
    color: 'fff',
  },
  P3: {
    fullName: 'Phase 3',
    shortName: 'P3',
    color: 'fff',
  },
  P4: {
    fullName: 'Phase 4',
    shortName: 'P4',
    color: 'fff',
  },
};
