<template>
  <div
    class="min-h-[24px] flex items-center flex-shrink-0 gap-x-1.5 px-1 py-0.5 rounded"
    :style="{ background: bgColor }"
  >
    <img
      v-if="levelInfo?.level_url"
      :src="levelInfo?.level_url"
      alt=""
      class="w-[20px] h-[20px]"
    />
    <div
      :class="{ 'font-bold': bold }"
      :style="{
        color: levelInfo?.level_color,
      }"
    >
      {{ levelInfo?.level_id }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '~/stores/modules/app';
const props = withDefaults(
  defineProps<{
    level?: number;
    bold?: boolean;
  }>(),
  {
    level: undefined,
    bold: false,
  },
);
const bgColor = computed(() => {
  if (!levelInfo.value?.level_color) return '';
  return hexToRgba(levelInfo.value.level_color, 0.1);
});
const appStore = useAppStore();
const levelInfo = computed(() => {
  if (props.level !== undefined) {
    return appStore.levelMap.get(props.level);
  }
  return undefined;
});
</script>
<style lang="scss" scoped></style>
