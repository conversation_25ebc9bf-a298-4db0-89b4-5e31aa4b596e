<template>
  <component
    :is="`svgo-${name}`"
    class="text-xl my-0 inline"
    v-bind="$attrs"
    :filled="filled"
    :font-controlled="fontControlled"
  ></component>
</template>
<script lang="ts" setup>
const { name } = defineProps({
  name: {
    type: String,
    default: '',
  },
  filled: {
    type: Boolean,
    default: true,
  },
  fontControlled: {
    type: Boolean,
    default: true,
  },
});
</script>
