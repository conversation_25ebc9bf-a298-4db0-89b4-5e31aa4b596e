<template>
  <div class="flex-layout w-full flex justify-center items-center">
    <slot>
      <div
        class="flex-layout items-center justify-center flex-wrap text-[#68779F] text-lg"
        :class="{ 'flex-col gap-y-0': column }"
      >
        <BaseIcon
          :name="icon"
          filled
          class="w-[214px] h-[114px]"
          :font-controlled="false"
        ></BaseIcon>
        <span>{{ msg }}</span>
      </div>
    </slot>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  msg: {
    type: String,
    default: 'empty',
  },
  column: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: 'empty',
  },
});
</script>
<style lang="scss" scoped></style>
