<template>
  <div class="grow flex flex-col justify-between">
    <DataTable
      v-bind="attrs"
      :bordered="false"
      :bottom-bordered="false"
      :columns="columns"
      :data="data"
      :loading="loading"
      :pagination="false"
      class="text-[#B0AAA7]"
      :scroll-x="scrollX"
    >
      <template #empty>
        <ClientOnly>
          <Empty
            :msg="$t(emptytext) || $t('no_record_found')"
            column
            class="bg-transparent"
          />
          <div v-show="loading" class="py-18 mt-[30px] mb-3 h-[177.5px]"></div>
        </ClientOnly>
      </template>
    </DataTable>
    <div class="wallet-list px-[20px] py-3.5">
      <ListPaginator
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :page="pagination.page"
        @update:page="updatePage"
      ></ListPaginator>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  columns: {
    type: Array as any,
    default: () => [],
  },
  data: {
    type: Array as any,
    default: () => [],
  },
  emptytext: {
    type: String,
    default: 'no_data',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object as any,
    default: () => {
      return {
        total: 0,
        page: 1,
        pageSize: 10,
      };
    },
  },
  scrollX: {
    type: Number,
    default: 1000,
  },
});

type Pagination = {
  total?: number;
  page?: number;
  pageSize?: number;
};
const attrs = useAttrs();
const emit = defineEmits(['update:page']);
const updatePage = (updateData: Pagination) => {
  emit('update:page', updateData);
};
</script>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-th) {
  border-bottom: none !important;
}
</style>
