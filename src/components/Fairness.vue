<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div
    class="fairness-container"
    :class="[containerClasses, { 'flex-row-reverse': placement === 'right' }]"
    @click="popupFair"
  >
    <svgo-fair
      v-if="showIcon"
      class="mb-0"
      :class="iconSizes[iconSize]"
      :fontControlled="false"
    ></svgo-fair>
    <span :class="['w-fit', textSize[size]]">{{ text }}</span>
  </div>
</template>

<script lang="ts" setup>
import FairDialog from '~/components/dialog/Fair.vue';
import type { FairInfoType } from '~/types/cases';

// 类型定义
type Size = 'xs' | 'tiny' | 'small' | 'large' | 'xlarge' | '2xl' | '3xl';
type Placement = 'left' | 'right';

interface FairnessProps {
  size?: Size;
  iconSize?: Size;
  placement?: Placement;
  fairInfo?: FairInfoType;
  upperCase?: boolean;
  showIcon?: boolean;
}

// 样式常量
const CONTAINER_BASE_CLASSES = [
  'h-[42px]',
  'flex',
  'items-center',
  'gap-sm',
  'hover:text-theme-color',
  'cursor-pointer',
  'text-purple-1',
] as const;

const TEXT_SIZES: Readonly<Record<Size, string>> = {
  xs: 'text-[10px]',
  tiny: 'text-[12px]',
  small: 'text-[14px]',
  large: 'text-[16px]',
  xlarge: 'text-[18px]',
  '2xl': 'text-[20px]',
  '3xl': 'text-[22px]',
} as const;

const ICON_SIZES: Readonly<Record<Size, string>> = {
  xs: 'size-[10px]',
  tiny: 'size-[14px]',
  small: 'size-[16px]',
  large: 'size-[18px]',
  xlarge: 'size-[20px]',
  '2xl': 'size-[22px]',
  '3xl': 'size-[24px]',
} as const;

// Props 定义
const props = withDefaults(defineProps<FairnessProps>(), {
  placement: 'left',
  size: 'small',
  iconSize: '3xl',
  fairInfo: undefined,
  upperCase: false,
  showIcon: true,
});

const dialog = useAppDialog();
const { t } = useI18n();
const $router = useRouter();

// 计算属性
const containerClasses = computed(() => CONTAINER_BASE_CLASSES.join(' '));
const textSize = TEXT_SIZES;
const iconSizes = ICON_SIZES;
const text = computed(() => {
  const baseText = t('provably_fair');
  return props.upperCase ? baseText.toUpperCase() : baseText;
});

// 方法
const popupFair = () => {
  if (props.fairInfo) {
    dialog.open(FairDialog, {
      style: {
        width: '880px',
      },
      contentProps: {
        fairInfo: props.fairInfo,
      },
    });
    return;
  }

  const path = $router.currentRoute.value.path.split('/')[1];
  openNewPage(`/fairness?tab=${path}`);
};
</script>

<style lang="scss" scoped>
.fairness-container {
  transition: color 0.2s ease;
}
</style>
