<template>
  <Tooltip
    :trigger="tooltip || tooltipText ? 'hover' : 'manual'"
    placement="top"
  >
    <template #trigger>
      <span
        :class="[
          sizeClass,
          alignClass,
          lineHeightClass,
          $attrs.class,
          {
            'font-thin': bold === 'thin',
            'font-light': bold === 'light',
            'font-normal': bold === 'normal',
            'font-medium': bold === 'medium',
            'font-semibold': bold === 'semibold',
            'font-bold': bold === 'bold' || bold === true || bold === '',
            'font-italic': italic,
            underline: underline,
            '!block truncate': ellipsis && (lines === 1 || !lines),
          },
          ellipsis && lines > 1
            ? ['line-clamp-' + lines, 'break-words', 'overflow-hidden']
            : '',
        ]"
        :style="{
          ...(maxWidth ? { maxWidth } : {}),
          ...(customStyle || {}),
          ...(ellipsis && lines > 1
            ? {
                display: '-webkit-box',
                '-webkit-box-orient': 'vertical',
                '-webkit-line-clamp': lines,
              }
            : {}),
        }"
        @click="onClick"
      >
        <slot></slot>
      </span>
    </template>
    {{ tooltipText }}
  </Tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue';

type TextSize = 'xl' | 'large' | 'medium' | 'small' | 'xs';
type TextAlign = 'left' | 'center' | 'right';
type LineHeight = 'none' | 'tight' | 'normal' | 'relaxed' | 'loose';
type TextBold =
  | 'bold'
  | 'semibold'
  | 'medium'
  | 'normal'
  | 'light'
  | 'thin'
  | ''
  | boolean;

interface Props {
  size?: TextSize;
  bold?: TextBold;
  italic?: boolean;
  underline?: boolean;
  ellipsis?: boolean;
  lines?: number;
  maxWidth?: string;
  align?: TextAlign;
  lineHeight?: LineHeight;
  customStyle?: Record<string, string>;
  onClick?: (e: MouseEvent) => void;
  tooltip?: boolean;
  tooltipText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  bold: 'normal',
  italic: false,
  underline: false,
  ellipsis: false,
  lines: 1,
  align: 'left',
  lineHeight: 'normal',
  maxWidth: '',
  customStyle: () => ({}),
  onClick: () => {},
  tooltip: false,
  tooltipText: '',
});
const sizeClass = computed(() => {
  const sizes: Record<TextSize, string> = {
    xl: 'text-xl', // 18px
    large: 'text-lg', // 16px
    medium: 'text-base', // 14px
    small: 'text-sm', // 12px
    xs: 'text-xs', // 10px
  };
  return sizes[props.size];
});

const alignClass = computed(() => {
  const aligns: Record<TextAlign, string> = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };
  return aligns[props.align];
});

const lineHeightClass = computed(() => {
  const lineHeights: Record<LineHeight, string> = {
    none: 'leading-none',
    tight: 'leading-tight',
    normal: 'leading-normal',
    relaxed: 'leading-relaxed',
    loose: 'leading-loose',
  };
  return lineHeights[props.lineHeight];
});

defineOptions({
  inheritAttrs: false,
});
</script>
