<template>
  <span>{{ formatValue(currentValue) }}</span>
</template>

<script setup lang="ts">
interface Props {
  value: number | string;
  digits?: number;
  isFloat?: boolean;
  duration?: number;
  steps?: number;
}

const props = withDefaults(defineProps<Props>(), {
  digits: 2,
  isFloat: true,
  duration: 500,
  steps: 60,
});

const startTime = ref(0);
const endTime = ref(0);
const difference = ref(0);
const interval = ref(0);
const currentValue = ref<number | string>(props.value);

const digits = computed(() => props.digits);

const { resume, pause } = useIntervalFn(updateValue, interval, {
  immediate: false,
});

function formatValue(val: number | string) {
  const numVal = Number(val);
  if (Number.isNaN(numVal)) return '0';

  if (props.isFloat === false) {
    return new Intl.NumberFormat().format(Math.round(numVal));
  }

  const formatted = new Intl.NumberFormat(undefined, {
    minimumFractionDigits: digits.value,
    maximumFractionDigits: digits.value,
  }).format(numVal);

  // 处理负零的特殊情况
  if (formatted.match(/^-0[.,]0+$/)) {
    return formatted.substring(1);
  }

  return formatted;
}

function updateValue() {
  const now = new Date().getTime();
  const remaining = Math.max((endTime.value - now) / props.duration, 0);

  currentValue.value = Number(props.value) - remaining * difference.value;

  if (currentValue.value === props.value) {
    pause();
  }
}

watch(
  () => props.value,
  (newVal, oldVal) => {
    if (oldVal === newVal) return;

    difference.value = Number(newVal) - Number(oldVal);
    startTime.value = new Date().getTime();
    endTime.value = startTime.value + props.duration;

    const steps = Math.max(props.steps, 10);
    interval.value = Math.abs(Math.floor(props.duration / steps));

    resume();
  },
);
</script>
