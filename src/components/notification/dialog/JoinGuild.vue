<template>
  <div>
    <h2 class="text-center">{{ $t('confirm_join_guild') }}</h2>
    <p class="my-5">
      {{ $t('guild_invites_you', { a: guild }) }}
    </p>
    <n-input
      v-model:value="uid"
      type="text"
      :placeholder="$t('enter_UID_guild_inviter')"
    />
    <p class="my-5 text-theme-color">
      {{ $t('before_join_guild_notify') }}
    </p>
    <MainButton class="w-full mt-10" :loading="loading" @click="confirmJoin">
      {{ $t('confirm_join_guild') }}
    </MainButton>
  </div>
</template>
<script lang="ts" setup>
const dialogRef = inject('dialogRef') as UseDialogOptions;
const toast = useAppToast();
const { notificationApi } = useApi();
const props = defineProps({
  guild: {
    type: String,
    default: '',
  },
});
const { t } = useI18n();
const uid = ref<string>('');
const loading = ref(false);
const confirmJoin = async () => {
  if (!uid.value) {
    toast.error({ content: t('enter_UID_guild_inviter') });
    return;
  }
  const guildIdMatch = props.guild.match(/\((.*?)\)/);
  if (guildIdMatch) {
    loading.value = true;
    const guildId = guildIdMatch[1];
    const { data: req } = await notificationApi.joinGuild({
      guild_leader: uid.value,
      guild_id: Number(guildId),
    });
    const data = req.value?.data;
    if (data) {
      dialogRef.onClose?.();
      dialogRef.destroy();
      toast.success({ content: t('join_guild_success') });
    }
    loading.value = false;
  }
};
</script>
<style lang="scss" scoped></style>
