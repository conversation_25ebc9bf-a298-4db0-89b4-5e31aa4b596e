<template>
  <div>
    <n-spin :show="loading">
      <template v-if="data.length">
        <NotificationMsg
          v-for="item in data"
          :key="item.id"
          :info="item"
          :highlight="item.status === 1"
          @click="handleMsg(item.id)"
        />
      </template>
      <template v-else-if="!loading">
        <Empty column class="bg-[#151A29]">
          <div
            class="flex flex-col items-center justify-center h-[188px] text-[#68779F]"
          >
            <BaseIcon name="empty-record" class="!text-[84px] mb-4"></BaseIcon>
            <div>{{ emptyMsg || $t('no_data') }}</div>
          </div>
        </Empty>
      </template>
      <div v-else class="min-h-[188px]"></div>
    </n-spin>
    <ListPaginator
      :total="paginator.total"
      :page="paginator.page"
      :page-size="paginator.pageSize || 15"
      @update:page="updatePage"
    ></ListPaginator>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    paginator: {
      total: number;
      page: number;
      pageSize?: number;
    };
    data: ApiV1NotifyGet200ResponseDataListInner[];
    emptyMsg: string;
    loading: boolean;
  }>(),
  {
    paginator: () => ({
      total: 0,
      page: 1,
      pageSize: 15,
    }),
  },
);
const $router = useRouter();
const $emit = defineEmits(['updatePage']);
// 分页
const updatePage = (page: { page: number }) => {
  $emit('updatePage', page.page);
};
const handleMsg = (id?: number) => {
  if (!id) return;
  $router.push(`/notifications/${id}`);
};
</script>
<style lang="scss" scoped></style>
