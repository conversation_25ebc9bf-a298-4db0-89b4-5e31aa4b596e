<template>
  <div
    :class="highlight ? 'text-[#fff] ' : ' text-white/50'"
    class="flex justify-between item-center gap-y-2 py-4 border-b-1 border-bg-black cursor-pointer hover-item flex-wrap"
  >
    <div class="box">
      <BaseIcon
        :name="highlight ? 'unread' : 'read'"
        class="mr-1.5 mb-0.5"
        filled
      ></BaseIcon>
      {{ info.title }}
    </div>
    <span :class="{ 'text-white/50': !highlight }">{{ info.send_time }}</span>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    info: ApiV1NotifyGet200ResponseDataListInner;
    highlight?: boolean;
  }>(),
  {
    highlight: false,
  },
);
</script>
<style lang="scss" scoped>
.hover-item:hover {
  color: #f8b838 !important;
  span {
    color: #f8b838 !important;
  }
}
</style>
