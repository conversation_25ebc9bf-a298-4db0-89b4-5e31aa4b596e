<template>
  <div :class="['tags-box', $attrs.class]" :style="tagBoxStyle">
    <template v-for="(tag, index) in shortTags" :key="`tag-${index}`">
      <Tooltip trigger="hover">
        <template #trigger>
          <div>
            <Gem
              v-if="isSpecialTag(tag) && tag.title"
              :title="tag.title"
              :size="size"
            />
            <span v-else :style="{ color: `#${tag.color}` }">
              {{ tag.text }}
            </span>
          </div>
        </template>
        <div class="tags-box">
          <span
            :style="{
              color: isSpecialTag(tag)
                ? `#${gemInfo?.color}`
                : `#${normalTags[index]?.color}`,
            }"
          >
            {{ isSpecialTag(tag) ? tag.title : normalTags[index]?.text }}
          </span>
          <b v-if="index < normalTags.length - 1"></b>
        </div>
      </Tooltip>
      <b v-if="index < shortTags.length - 1"></b>
    </template>
  </div>
</template>

<script setup lang="ts">
import Gem from './Gem.vue';
import { GEM_PHASE_MAPPING } from '~/constants/skin';
import type { CaseSkinItemType } from '~/types/cases';

const SPECIAL_TAGS = [
  'Ruby',
  'Sapphire',
  'Emerald',
  'Black Pearl',
  'P1',
  'P2',
  'P3',
  'P4',
];

// 外观多语言
const WEAR_CATEGORY_MAPPING: Record<string, string> = {
  'Factory New': 'wearcategory0',
  'Minimal Wear': 'wearcategory1',
  'Field-Tested': 'wearcategory2',
  'Well-Worn': 'wearcategory3',
  'Battle-Scarred': 'wearcategory4',
  'Not Painted': 'wearcategory5',
};

interface Props {
  info: CaseSkinItemType;
  size?: number;
  gap?: number;
  fontSize?: number;
}

interface TagItem {
  color: string;
  text: string;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 17,
  gap: 4,
  fontSize: 16,
});

const isSpecialTag = (tag: TagItem) => {
  if (!tag.title) return false;
  return SPECIAL_TAGS.includes(tag.title);
};

const createRegularTag = (tag: any): { short: TagItem; normal: TagItem } => {
  const { $td } = useNuxtApp();
  return {
    short: {
      color: tag.color,
      text: tag.ultra_short_title,
      title: tag.title,
    },
    normal: {
      color: tag.color,
      text: $td(WEAR_CATEGORY_MAPPING[tag.title] || tag.title, tag.title),
    },
  };
};

const gemInfo = computed(
  () => GEM_PHASE_MAPPING[props.info.style_name ?? ''] || null,
);

const shortTags = computed(() => {
  const result: TagItem[] = [];
  const tagList = props.info.tags || {};

  Object.values(tagList).forEach((tag) => {
    if (!tag.is_show || !tag.ultra_short_title) return;
    result.push(createRegularTag(tag).short);
  });

  const styleName = props.info.style_name;
  if (styleName) {
    result.push({
      color: 'fff',
      text: styleName,
      title: styleName,
    });
  }

  return result;
});

const normalTags = computed(() => {
  const result: TagItem[] = [];
  const tagList = props.info.tags || {};

  Object.values(tagList).forEach((tag) => {
    if (!tag.is_show || !tag.ultra_short_title) return;
    result.push(createRegularTag(tag).normal);
  });

  return result;
});

const tagBoxStyle = computed(() => ({
  '--tag-gap': `${props.gap}px`,
  '--tag-font-size': `${props.fontSize}px`,
}));
</script>

<style lang="scss" scoped>
.tags-box {
  @apply flex items-center leading-none;
  gap: var(--tag-gap, 4px);
  font-size: var(--tag-font-size, 16px);

  :deep(b) {
    height: 10px;
    width: 1px;
    background: #2d3243;
  }
}
</style>
