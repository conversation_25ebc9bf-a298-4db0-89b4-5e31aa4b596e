<template>
  <div>
    <Table
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :pagination="pagination"
      :emptytext="emptyTableText"
      :loading="tableLoading"
      :theme-overrides-cover="{
        thColor: '#20263B',
        thTextColor: '#fff',
      }"
      @update:page="updatePage"
    />
  </div>
</template>

<script setup lang="ts">
import { createCommissionDetailsColumns } from '~/models/referralsModel';

const { settingApi } = useApi();

const columns = ref<any[]>([]);

const tableData = ref<ApiV1UserCommissionDetailsGet200ResponseDataItemsInner[]>(
  [],
);

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(true);

const emptyTableText = ref('no_record_found');

const updatePage = ({ page }: { page: number }) => {
  pagination.value.page = page;
  getCommissionDetails();
};

// 获取佣金明细
const getCommissionDetails = async () => {
  tableLoading.value = true;
  try {
    const res = await settingApi.getCommissionDetails({
      page: pagination.value.page,
    });
    if (res.data.value.code === 0) {
      tableData.value = res.data.value.data.items;
      pagination.value.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

onMounted(() => {
  columns.value = createCommissionDetailsColumns();
  getCommissionDetails();
});
</script>
