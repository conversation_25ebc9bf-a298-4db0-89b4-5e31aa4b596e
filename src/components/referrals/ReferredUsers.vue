<template>
  <div>
    <Table
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :pagination="pagination"
      :emptytext="emptyTableText"
      :loading="tableLoading"
      :theme-overrides-cover="{
        thColor: '#20263B',
        thTextColor: '#fff',
      }"
      @update:page="updatePage"
    />
  </div>
</template>

<script setup lang="ts">
import { createReferredUsersColumns } from '~/models/referralsModel';

const columns = ref<any[]>([]);

const tableData = ref<
  ApiV1UserCommissionReferredGet200ResponseDataItemsInner[]
>([]);

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(true);

const emptyTableText = ref('no_record_found');

const { settingApi } = useApi();

const updatePage = ({ page }: { page: number }) => {
  pagination.value.page = page;
  getReferredUsers();
};

// 获取推荐用户列表
const getReferredUsers = async () => {
  tableLoading.value = true;
  try {
    const res = await settingApi.getReferredUsers({
      page: pagination.value.page,
    });
    if (res.data.value.code === 0) {
      tableData.value = res.data.value.data.items;
      pagination.value.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

onMounted(() => {
  columns.value = createReferredUsersColumns();
  getReferredUsers();
});
</script>
