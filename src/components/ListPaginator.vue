<template>
  <div v-show="total && pageCount > 1" class="py-3">
    <n-space justify="end">
      <n-pagination
        :theme-overrides="themeOverrides"
        :page="page"
        :page-size="pageSize"
        :page-sizes="[10]"
        :page-count="pageCount"
        @update:page="updatePage"
        @update:page-count="updatePageSize"
      >
        <template #prev>
          <div class="px-2">FIRST</div>
        </template>
        <template #next>
          <div class="px-2">LAST</div>
        </template>

        <!-- <template #goto>
          <span class="text-surface-0">{{ $t('跳至') }}</span>
        </template>
        <template #suffix>
          <span class="text-surface-0">{{ $t('页') }}</span>
        </template> -->
      </n-pagination>
    </n-space>
  </div>
</template>
<script setup lang="ts">
import type { PaginationProps, InputProps } from 'naive-ui';
type PaginationThemeOverrides = NonNullable<PaginationProps['themeOverrides']>;
type InputThemeOverrides = NonNullable<InputProps['themeOverrides']>;
const primaryColor = '#F8B838';
const border: string = '1px solid #F8B838';
const themeOverrides: PaginationThemeOverrides & InputThemeOverrides = {
  buttonColor: 'rgba(255, 255, 255, 0)',
  buttonBorder: '',
  buttonIconColor: '#fff',
  buttonIconColorHover: '#fff',
  buttonBorderHover: '',
  itemColor: 'rgba(125, 144, 202, 0.1)',
  itemSizeMedium: '36px',
  // itemColorHover: '',
  // itemColorActive: '',
  itemColorActiveHover: '',
  itemColorDisabled: '#151A29',
  itemTextColor: '#7D90CA',
  // itemTextColorPressed: '',
  itemTextColorHover: '#fff',
  itemTextColorActive: primaryColor,
  // itemBorder: '',
  // itemBorderDisabled: '',
  itemBorderActive: border,
  inputWidthMedium: '41px',
  itemTextColorDisabled: 'rgba(255, 255, 255, 0.38)',

  peers: {
    Input: {
      color: 'rgba(255, 255, 255, 0.1)',
      textColor: '#fff',
      caretColor: primaryColor,
      borderFocus: border,
      borderHover: border,
      colorFocus: border,
      border,
      boxShadowFocus: border,
    },
  },
};

defineOptions({
  inheritAttrs: false,
});

const props = defineProps({
  page: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  total: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['update:page', 'update:pageSize']);

const pageCount = computed(() => {
  return Math.ceil(props.total / props.pageSize);
});

const updatePage = (page: number) => {
  emit('update:page', { page });
};

const updatePageSize = (pageSize: number) => {
  emit('update:pageSize', { pageSize });
};

defineExpose({
  updatePage,
  updatePageSize,
});
</script>
