<template>
  <div class="space-y-[12px] pt-[4px]">
    <!-- 充值金额 -->
    <div class="text-lg flex justify-between items-center">
      <span>{{ t('coin_recharge_amount', 'Coin recharge amount') }}</span>
      <p class="flex items-center gap-1 text-base">${{ baseAmount }}</p>
    </div>

    <!-- 手续费 -->
    <p class="text-lg flex justify-between items-center">
      <span>{{ t('processing_fee', 'Processing fee') }}</span>
      <span class="text-base">${{ feeAmount }}</span>
    </p>

    <!-- 总金额 -->
    <p class="text-lg flex justify-between items-center">
      <span>{{ t('amount_to_be_paid', 'Amount to be paid') }}</span>
      <span class="text-base text-theme-color">${{ totalAmount }}</span>
    </p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  baseAmount: number | string;
  feeAmount: string;
  totalAmount: string;
}

withDefaults(defineProps<Props>(), {});
const { t } = useI18n();
</script>
