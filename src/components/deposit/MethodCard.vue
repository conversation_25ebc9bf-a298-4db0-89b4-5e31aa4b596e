<template>
  <div>
    <a href="#" @click="selectMethod">
      <div
        class="px-md h-[56px] flex items-center bg-[#20263B] hover:bg-[#1F2744] transition-colors gap-x-1.5 duration-300 rounded cursor-pointer"
        :class="{ 'opacity-30 pointer-events-none': isUnderMaintenance }"
      >
        <div class="w-[40px] h-[40px] flex items-center">
          <img
            v-if="getImageUrl()"
            class="h-full object-contain"
            :src="getImageUrl()"
            :alt="name"
          />
        </div>

        <div>
          <h3>{{ name }}</h3>
        </div>
      </div>
    </a>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  method: {
    type: Object,
    required: false,
    default: null,
  },
  configuration: {
    type: Object,
    required: false,
    default: null,
  },
  options: {
    type: Object,
    required: false,
    default: null,
  },
  showFlowType: {
    type: Boolean,
    required: false,
    default: false,
  },
  country: {
    type: String,
    required: false,
    default: '',
  },
});

const emit = defineEmits(['select']);

const isUnderMaintenance = computed(() => {
  return props.configuration
    ? props.configuration.is_under_maintenance
    : props.method && props.method.configurations.length > 0
      ? props.method.configurations.every(
          (c: any) => c?.is_under_maintenance === true,
        )
      : false;
});

const name = computed(() => {
  return props.method?.name ?? '';
});

const selectMethod = (e: Event) => {
  e.preventDefault();
  const { method } = props;
  if (!isUnderMaintenance.value) {
    emit('select', method);
  }
};

const getImageUrl = () => {
  const { method, configuration } = props;

  if (configuration?.provider_logo_url_override) {
    return configuration.provider_logo_url_override;
  }

  if (method.logo_url?.startsWith('http')) {
    return method.logo_url;
  }

  // const key = configuration?.flow_type === 0
  //   ? configuration.provider.key
  //   : method.key

  // 图片映射
  return null;
};
</script>
