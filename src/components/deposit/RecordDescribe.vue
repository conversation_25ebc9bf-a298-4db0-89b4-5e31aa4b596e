<template>
  <client-only>
    <div
      class="break-normal whitespace-nowrap flex items-center"
      :class="className"
    >
      <n-ellipsis>
        <span>{{ labelMap[recordIdToName[type]] ?? '' }}</span>
        <span>{{ recordDetail }}</span>
      </n-ellipsis>
    </div>
  </client-only>
</template>

<script setup lang="ts">
import { recordIdToName, RECORD_LABEL_MAP } from '~/constants/transaction';

const labelMap = RECORD_LABEL_MAP();

interface Props {
  type: keyof typeof recordIdToName;
  recordDetail: string;
  className?: string;
}

withDefaults(defineProps<Props>(), {
  type: 1,
  recordDetail: '',
  className: '',
});
</script>
