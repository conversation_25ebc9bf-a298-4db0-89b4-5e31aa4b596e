<template>
  <div>
    <!-- 金额输入框 -->
    <n-form-item
      ref="depositAmountRef"
      :rule="rules.depositAmount"
      :show-feedback="false"
      :show-label="false"
      class="mb-lg"
    >
      <n-input
        size="large"
        class="h-[42px] rounded-md flex item-center font-medium"
        :theme-overrides="inputTheme"
        :value="depositAmount"
        :allow-input="onlyAllowNumber"
        :placeholder="placeholder"
        @update:value="handleAmountInput"
      >
        <template #prefix>
          <base-icon name="gold" class="size-6" filled />
        </template>
      </n-input>
    </n-form-item>

    <!-- 快捷金额选择 -->
    <div
      class="flex flex-wrap gap-md mb-[24px] max-sm:grid max-sm:grid-cols-3 max-sm:gap-2"
    >
      <div
        v-for="(option, index) in depositQuickOptions"
        :key="index"
        class="bg-dark-2 h-[36px] flex-1 flex items-center justify-center rounded-[4px] text-purple-1 box-border border-[1px] border-dark-2 hover:border-theme-color hover:text-theme-color font-medium cursor-pointer"
        @click="handleQuickOptionClick(option)"
      >
        <span>{{ option }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  depositAmount: string;
  depositQuickOptions: string[];
  rules: any;
  onlyAllowNumber: (value: string) => boolean;
  placeholder?: string;
  inputTheme?: any;
}

interface Emits {
  (e: 'amount-input', value: string): void;
  (e: 'quick-option-click', option: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter the amount of coins to top up',
  inputTheme: () => ({
    placeholderColor: '#4B5679',
    border: 'none',
    color: '#20263B',
  }),
});

const emit = defineEmits<Emits>();

const depositAmountRef = ref<any>(null);

watch(
  () => props.depositAmount,
  (newValue, oldValue) => {
    if (newValue !== oldValue && depositAmountRef.value) {
      nextTick(() => {
        depositAmountRef.value?.validate();
      });
    }
  },
);

const handleAmountInput = (value: string) => {
  emit('amount-input', value);
};

const handleQuickOptionClick = (option: string) => {
  emit('quick-option-click', option);
};

const validate = async () => {
  return await depositAmountRef.value?.validate();
};

defineExpose({
  validate,
  depositAmountRef,
});
</script>
