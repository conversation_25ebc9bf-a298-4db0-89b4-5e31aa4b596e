<template>
  <div
    class="cursor-pointer flex items-center gap-sm text-[#7D90CA]"
    @click="soundStore.switchSound"
  >
    <BaseIcon
      :name="soundStore.soundEnabled ? 'voice' : 'voice-off'"
      class="size-[24px]"
      :font-controlled="false"
      :filled="false"
    ></BaseIcon>
    <span>
      {{ soundStore.soundEnabled ? $t('sound_on') : $t('sound_off') }}
    </span>
  </div>
</template>
<script lang="ts" setup>
import { useSoundStore } from '~/stores/modules/sound';
const soundStore = useSoundStore();
</script>
<style lang="scss" scoped></style>
