<template>
  <img
    v-if="!isVideo"
    v-lazyload="lazy"
    :alt="alt"
    :class="className"
    v-bind="{ ...$attrs, ...lazyImage }"
  />
  <video
    v-else
    ref="videoRef"
    v-lazyload="lazy"
    :alt="alt"
    :class="className"
    autoplay
    loop
    muted
    playsinline
    preload="metadata"
    disablePictureInPicture
    controlslist="nodownload"
    v-bind="{ ...$attrs, ...lazyImage }"
  />
</template>
<script lang="ts" setup>
const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  alt: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  lazy: {
    type: Boolean,
    default: true,
  },
});

const videoRef = ref<HTMLVideoElement | null>(null);
const isVideo = computed(() => {
  const videoFormats = ['.mp4', '.webm', '.ogg', '.mov'];
  return props.src
    ? videoFormats.some((format) => props.src?.toLowerCase().endsWith(format))
    : false;
});

let wasPlayingOnHide = false;
let currentPlayPromise: Promise<void> | null = null;

const lazyImage = computed(() => {
  if (props.lazy) {
    return {
      'data-src': props.src,
    };
  } else {
    return {
      src: props.src,
    };
  }
});

const safePlayVideo = (video: HTMLVideoElement) => {
  if (!video) return;
  if (!document.body.contains(video)) return;

  if (!video.paused) return;

  video.currentTime = 0;
  if (currentPlayPromise) return;

  try {
    const playPromise = video.play();
    if (playPromise !== undefined) {
      currentPlayPromise = playPromise;
      playPromise
        .then(() => {
          currentPlayPromise = null;
        })
        .catch((error: Error) => {
          currentPlayPromise = null;
          if (error.name !== 'AbortError') {
            console.error('视频播放失败:', error);
          }
        });
    }
  } catch (error) {
    currentPlayPromise = null;
    console.error('视频播放异常:', error);
  }
};

const safePauseVideo = (video: HTMLVideoElement) => {
  if (!video) return;
  if (!document.body.contains(video)) {
    currentPlayPromise = null;
    return;
  }

  try {
    if (!video.paused) {
      video.pause();
    }
  } catch (error) {}

  if (currentPlayPromise) {
    currentPlayPromise
      .then(() => {
        currentPlayPromise = null;
      })
      .catch(() => {
        currentPlayPromise = null;
      });
  }
};

const handleVisibilityChange = () => {
  if (!videoRef.value || !isVideo.value) return;
  if (document.hidden) {
    if (!videoRef.value.paused) {
      wasPlayingOnHide = true;
      safePauseVideo(videoRef.value);
    } else {
      wasPlayingOnHide = false;
    }
  } else if (wasPlayingOnHide && videoRef.value.paused) {
    setTimeout(() => {
      if (document.hidden) return;
      safePlayVideo(videoRef.value as HTMLVideoElement);
    }, 100);
  }
};

const { stop: stopObserver } = useIntersectionObserver(
  videoRef,
  ([{ isIntersecting }]) => {
    if (!videoRef.value || !isVideo.value) return;

    if (isIntersecting) {
      if (videoRef.value.paused) {
        safePlayVideo(videoRef.value);
      }
    } else {
      safePauseVideo(videoRef.value);
    }
  },
  { threshold: 0.1 },
);

onMounted(() => {
  if (!isVideo.value) return;
  if (videoRef.value) {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }
});

onBeforeUnmount(() => {
  if (!isVideo.value) return;
  document.removeEventListener('visibilitychange', handleVisibilityChange);

  if (videoRef.value) {
    currentPlayPromise = null;
    try {
      if (!videoRef.value.paused) {
        videoRef.value.pause();
      }
    } catch (error) {}
  }
  stopObserver();
});
</script>
