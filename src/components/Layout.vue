<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-config-provider
    :theme="theme"
    :theme-overrides="themeOverrides"
    inline-theme-disabled
    :locale="enUS"
    :date-locale="dateEnUS"
  >
    <div class="text-primary-text relative bg-[#0A0D14] layout-bg">
      <div v-if="isOpenCasePage" class="cases-bg"></div>
      <Header :key="globalRefreshKey"></Header>
      <div class="min-h-screen pt-[50px] xl:pt-[96px] flex flex-col">
        <div class="flex-layout mt-5 max-w-[1376px] w-full mx-auto max-xl:px-2">
          <slot :key="globalRefreshKey"></slot>
        </div>
        <Footer></Footer>
      </div>
      <AppSpin v-if="appStore.spinShow" />
      <div class="z-[6666] fixed right-[20px] top-[50%] translate-y-[-50%]">
        <div
          class="w-[57px] h-[57px] bg-[#0A0D14] border-[#323A51] rounded-full cursor-pointer flex items-center justify-center"
          :style="{ boxShadow: '0 0 25px 0 rgba(248,184,56,0.5)' }"
          @click="toggle('open')"
        >
          <BaseIcon name="service" class="w-[27px] h-[25px]" />
        </div>
      </div>
    </div>
  </n-config-provider>
  <client-only>
    <TurnstileVerificationModal
      ref="verificationModalRef"
      :show="showVerification"
      :site-key="config.public.TURNSTILE_SITE_KEY"
      :error-message="verificationState.error"
      @verified="handleVerificationSuccess"
      @error="handleVerificationError"
    />
  </client-only>
</template>
<script lang="ts" setup>
import { enUS, dateEnUS } from 'naive-ui';
import { useAppStore } from '~/stores/modules/app';
import { useSettingStore } from '~/stores/modules/setting';
import { ClientSeed } from '~/utils';
const { theme, themeOverrides } = useThemeOverrides();
const settingStore = useSettingStore();
const appStore = useAppStore();
const $router = useRouter();

if (import.meta.server) {
  appStore.openSpin();
}
await settingStore.getUserInfo(true);

const sharedMetadata = useState<any>('app-metadata', () => null);

if (!sharedMetadata.value) {
  const result = await appStore.getMetadata(false, true);
  sharedMetadata.value = result;
} else {
  appStore.setMetaData(sharedMetadata.value);
}

const config = useRuntimeConfig();
// 是否打开人机验证
const enableTurnstile =
  config.public.ENABLE_TURNSTILE === 'true' && import.meta.client;

const { toggle, setUser } = useChatWoot();
const userInfo = computed(() => settingStore.userInfo);

const setChatwootUser = () => {
  if (!userInfo.value?.uid) return;
  setUser(userInfo.value?.uid, {
    avatar_url: userInfo.value?.user_avatar,
    name: userInfo.value?.user_name,
  });
};

if (import.meta.client) {
  window.addEventListener('chatwoot:ready', () => {
    if (userInfo.value) {
      setChatwootUser();
    }
  });
} else {
  settingStore.getUserInfo(true);
}

// 验证人机状态
const {
  showVerification,
  debouncedCheckVerification,
  verificationState,
  handleVerificationSuccess: processVerification,
} = useTurnstileVerification();

// 人机验证窗口
const verificationModalRef = ref<HTMLElement>();

const globalRefreshKey = computed(() => appStore.globalRefreshKey);

// 处理验证成功
const handleVerificationSuccess = async (data: any) => {
  if (!data || !data.token) {
    console.error('验证令牌无效');
    return;
  }

  const success = await processVerification(data.token);
  if (!success && verificationModalRef.value) {
    verificationModalRef.value?.remove();
  }
};

// 处理验证错误
const handleVerificationError = (error: any) => {
  console.error('验证错误:', error);
};

// 检查人机验证时效
const checkAndTriggerVerification = async () => {
  // 已经在显示验证，不重复检查
  if (showVerification.value) return;

  // 检查验证是否已过期
  const stillValid = await debouncedCheckVerification();

  // 如果验证已过期或需要重新验证，则显示验证窗口
  if (!stillValid) {
    showVerification.value = true;
  }
};
// 在客户端获取客户端种子和判断路由IP权限
if (import.meta.client) {
  settingStore.clientSeed = ClientSeed.get();

  // 校验当前页面是否在限制国家
  const restrictIPRoute = ['cases', 'coinflip', 'case-battles'];
  const path = $router.currentRoute.value.path;
  await appStore.getMetadata(false);
  const ipPermission = appStore.ipPermission;
  const toRootPath = path.split('/')[1];
  const pathIndex = restrictIPRoute.findIndex((el) => el === toRootPath);
  if (!ipPermission && pathIndex > -1) {
    // 如果当前IP在限制国家和地区,且路由地址在限制国家中,重定向到首页
    Promise.resolve(navigateTo('/')).then(() => {
      appStore.closeSpin();
    });
  } else {
    setTimeout(() => {
      appStore.closeSpin();
    });
  }
}

const isOpenCasePage = computed(() => {
  const route = $router.currentRoute.value;
  return route.name === 'cases-id';
});

// 监听登录成功或者已登录
watch(
  () => settingStore.userInfo,
  (newV, oldV) => {
    if (!oldV && newV && !appStore.notifyPollingRunning) {
      appStore.startPollingNotify();
    }
  },
);

const { stopListener } = useRouteObserver((to, from, _type, isFirstCall) => {
  if (from?.path !== to?.path) {
    if (!isFirstCall && to.meta?.getMeta) {
      appStore.getMetadata();
    }
    if (enableTurnstile) {
      checkAndTriggerVerification();
    }
  }
}, true);

appStore.getLevelConfig();

// 初始化
onMounted(async () => {
  if (settingStore.userInfo) {
    appStore.startPollingNotify(undefined, false);
  }
  if (enableTurnstile) {
    // 初始检查人机验证状态
    const isValidVerification = await debouncedCheckVerification();
    // 如果验证无效，显示验证窗口
    if (!isValidVerification) {
      showVerification.value = true;
    }
  }
});

onBeforeUnmount(() => {
  stopListener();
});
</script>
<style lang="scss" scoped>
.layout-bg {
  background-image: url('/imgs/layout_bg.png');
  background-position: 0 0;
}
.cases-bg {
  @apply w-[1008px] h-[330px] absolute left-1/2 -translate-x-[600px] sm:w-[2016px] sm:h-[660px] sm:-translate-x-[1020px] sm:max-xl:top-[-40px] max-sm:top-[35px];
  background: url('/imgs/case-bg.png') no-repeat right 0 top -62px;
  background-size: contain;
}
</style>
