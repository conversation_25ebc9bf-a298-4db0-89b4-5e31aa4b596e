<template>
  <div class="bind-email-form">
    <BaseEmailForm
      mode="bind"
      :default-email="email"
      :email-readonly="!!email"
      :show-email-promotion="true"
      @success="handleSuccess"
      @cancel="emit('cancel')"
    />
  </div>
</template>

<script setup lang="ts">
import BaseEmailForm from './BaseEmailForm.vue';
interface Props {
  email?: string;
}

interface Emits {
  (e: 'success'): void;
  (e: 'cancel'): void;
}

withDefaults(defineProps<Props>(), {
  email: '',
});
const emit = defineEmits<Emits>();

const handleSuccess = () => {
  emit('success');
};
</script>
