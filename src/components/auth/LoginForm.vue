<template>
  <div class="login-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :show-feedback="false"
      :show-require-mark="false"
      class="space-y-[12px]"
    >
      <!-- 电子邮箱 -->
      <n-form-item path="email">
        <template #label>
          <div class="flex items-center gap-[6px]">
            <BaseIcon name="login-email" class="size-[14px]" />
            <span>
              {{ $t('email', 'Email') }}
            </span>
          </div>
        </template>
        <n-input
          v-model:value="formData.email"
          :placeholder="$t('enter_email', 'Enter your email address')"
          :theme-overrides="inputTheme"
          @focus="handleFieldFocus"
        />
      </n-form-item>

      <!-- 密码 -->
      <n-form-item path="password">
        <template #label>
          <div class="flex items-center gap-[6px]">
            <BaseIcon name="login-lock" class="size-[14px]" />
            <span>
              {{ $t('password', 'Password') }}
            </span>
          </div>
        </template>
        <n-input
          v-model:value="formData.password"
          type="password"
          :maxlength="16"
          show-password-on="click"
          :placeholder="$t('enter_password', 'Enter password')"
          :theme-overrides="inputTheme"
          :input-props="{ autocomplete: 'off' }"
          @keyup.enter="handleLogin"
          @focus="handleFieldFocus"
        />
      </n-form-item>

      <!-- 忘记密码 -->
      <div class="text-right !mt-[10px]">
        <n-button
          text
          class="text-purple-1 hover:text-blue-300"
          @click="handleForgotPassword"
        >
          {{ $t('forgot_password', 'Forgot Password?') }}
        </n-button>
      </div>

      <!-- Cloudflare验证 - 隐形模式 -->
      <CloudflareTurnstile
        ref="turnstileRef"
        size="flexible"
        :auto-execute="true"
        :site-key="config.public.TURNSTILE_SITE_KEY"
        @verified="handleTurnstileVerified"
        @error="handleTurnstileError"
      />

      <!-- 登录按钮 -->
      <n-button
        type="primary"
        size="large"
        block
        :loading="loading"
        class="h-[42px] btn-gradient-primary uppercase"
        @click="handleLogin"
      >
        {{ $t('confirm_login', 'Confirm Login') }}
      </n-button>

      <!-- 分割线 -->
      <div class="flex items-center my-6">
        <div class="flex-1 h-px bg-[#5D688A]"></div>
        <span class="px-4 text-[#5D688A] text-sm font-medium uppercase">
          {{ $t('or_select', 'or select') }}
        </span>
        <div class="flex-1 h-px bg-[#5D688A]"></div>
      </div>

      <!-- Steam登录 -->
      <div class="flex justify-center">
        <n-button
          class="mx-auto h-12 min-w-[194px] btn-gradient-gray"
          @click="handleSteamLogin"
        >
          <template #icon>
            <BaseIcon name="login" class="w-5 h-5" />
          </template>
          {{ $t('sign_in_with_steam') }}
        </n-button>
      </div>
    </n-form>

    <!-- 服务条款 -->
    <div
      v-if="props.showTermsAndPrivacy"
      class="mt-[16px] text-[12px] text-[#5D688A]"
    >
      <i18n-t keypath="login_agreement" scope="global">
        <template #a>
          <n-button
            text
            size="tiny"
            class="text-[#5D688A] text-[12px] underline"
            @click="openTerms"
          >
            {{ $t('terms_of_service', 'Terms of Service') }}
          </n-button>
        </template>
      </i18n-t>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormRules } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';
import { isValidEmail } from '~/utils/validation';

interface LoginFormData {
  email: string;
  password: string;
}

interface Props {
  showTermsAndPrivacy?: boolean;
}

interface Emits {
  (e: 'success'): void;
  (e: 'switch-to-register'): void;
}

const props = withDefaults(defineProps<Props>(), {
  showTermsAndPrivacy: true,
});

const dialogRef = inject('dialogRef') as UseDialogOptions;
const emit = defineEmits<Emits>();

const formRef = ref<FormInst>();
const turnstileRef = ref();
const loading = ref(false);
const turnstileToken = ref('');

const config = useRuntimeConfig();
const settingStore = useSettingStore();
const { loginApi } = useApi();
const toast = useAppToast();
const { t } = useI18n();

const formData = reactive<LoginFormData>({
  email: '',
  password: '',
});

const inputTheme = {
  color: 'transparent',
  colorFocus: 'transparent',
  heightMedium: '38px',
};

const rules: FormRules = {
  email: [
    {
      required: true,
      message: t(
        'please_enter_login_credentials',
        'Please enter your login email and password.',
      ),
      trigger: ['blur'],
    },
    {
      type: 'email',
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
    {
      validator: (_rule, value) => {
        return isValidEmail(value);
      },
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
  ],
  password: [
    {
      required: true,
      message: t('password_required', 'Enter password'),
      trigger: ['blur'],
    },
  ],
};

const handleTurnstileVerified = (token: string) => {
  turnstileToken.value = token;
  if (loading.value) {
    nextTick(() => {
      handleLogin();
    });
  }
};

const handleTurnstileError = (error: any) => {
  console.error('Turnstile verification failed:', error);
  turnstileToken.value = '';
};

const handleLogin = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    if (!turnstileToken.value) {
      toast.error({
        content: t(
          'human_verification_required',
          'Please complete the human verification',
        ),
      });
      return;
    }

    loading.value = true;
    const res = await loginApi.emailLogin({
      email: formData.email,
      password: formData.password,
      cf_token: turnstileToken.value,
    });

    if (res.data.value.code === 0) {
      await settingStore.setTokenSafely(res.data.value.data.token);
      emit('success');
    } else {
      turnstileRef.value?.reset();
      turnstileToken.value = '';
    }
  } catch (error: any) {
    if (Array.isArray(error)) {
      const flatErrors = error.flat();
      if (error.length === 1 && flatErrors[0]?.message) {
        toast.error({
          content: t(flatErrors[0].message),
        });
      } else {
        toast.error({
          content: t(
            'please_enter_login_credentials',
            'Please enter your login email and password.',
          ),
        });
      }
    }
  } finally {
    loading.value = false;
  }
};

const handleSteamLogin = () => {
  settingStore.signin({
    openDialog: false,
    event: 'login',
  });
};
// 处理忘记密码
const handleForgotPassword = () => {
  dialogRef?.destroy();
  settingStore.openResetPasswordModal();
};

const handleFieldFocus = () => {
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
};

const openTerms = () => {
  // 打开服务条款页面
  window.open('/document/agreement', '_blank');
};
</script>

<style scoped lang="scss">
.login-form {
  iframe {
    width: 100%;
  }
}
</style>
