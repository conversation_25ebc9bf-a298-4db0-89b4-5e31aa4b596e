<template>
  <div class="reset-password-form">
    <BaseEmailForm
      mode="reset_password"
      :default-email="email"
      :email-readonly="!!email"
      @success="handleSuccess"
      @cancel="emit('cancel')"
    />
  </div>
</template>

<script setup lang="ts">
import BaseEmailForm from './BaseEmailForm.vue';

interface Props {
  email?: string;
}

interface Emits {
  (e: 'success'): void;
  (e: 'cancel'): void;
}

withDefaults(defineProps<Props>(), {
  email: '',
});
const emit = defineEmits<Emits>();

const handleSuccess = () => {
  emit('success');
};
</script>
