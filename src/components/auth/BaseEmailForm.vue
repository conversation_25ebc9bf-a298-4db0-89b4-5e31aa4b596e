<template>
  <div class="base-email-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :show-feedback="false"
      :show-require-mark="false"
      class="space-y-4"
    >
      <!-- 电子邮箱 -->
      <n-form-item ref="emailFormItemRef" path="email">
        <template #label>
          <div class="flex items-center gap-[6px]">
            <BaseIcon name="login-email" class="size-[14px]" />
            <span>
              {{ $t('email', 'Email') }}
            </span>
          </div>
        </template>
        <div class="w-full flex gap-2">
          <n-input
            :value="props.emailReadonly ? maskedEmail : formData.email"
            class="flex-1"
            :placeholder="$t('enter_email', 'Enter your email address')"
            :readonly="props.emailReadonly"
            :input-props="{ autocomplete: 'off' }"
            size="large"
            :theme-overrides="
              props.emailReadonly ? readonlyInputTheme : inputTheme
            "
            @update:value="
              (value) => !props.emailReadonly && (formData.email = value)
            "
            @focus="() => handleFieldFocus('email')"
          />
          <n-button
            :loading="sendCodeLoading"
            ghost
            class="w-[146px] h-[38px] rounded-[4px]"
            @click="sendVerificationCode"
          >
            {{
              countdown > 0
                ? `${countdown}s`
                : $t('send_verification_code', 'Get code')
            }}
          </n-button>
        </div>
      </n-form-item>

      <!-- 邮箱验证码 -->
      <n-form-item
        ref="codeFormItemRef"
        :show-label="false"
        path="verificationCode"
      >
        <div class="w-full flex gap-2">
          <n-input
            v-model:value="formData.verificationCode"
            :placeholder="
              $t(
                'enter_verification_code',
                'Enter the verification code sent to your email',
              )
            "
            :input-props="{ autocomplete: 'off' }"
            :theme-overrides="inputTheme"
            @focus="() => handleFieldFocus('verificationCode')"
          />
        </div>
      </n-form-item>

      <!-- 密码 -->
      <n-form-item ref="passwordFormItemRef" path="password">
        <template #label>
          <div class="flex items-center gap-[6px]">
            <BaseIcon name="login-lock" class="size-[14px]" />
            <span>
              {{ passwordLabel }}
            </span>
          </div>
        </template>
        <n-input
          v-model:value="formData.password"
          type="password"
          show-password-on="click"
          :placeholder="passwordPlaceholder"
          size="large"
          :input-props="{ autocomplete: 'off' }"
          :theme-overrides="inputTheme"
          @focus="() => handleFieldFocus('password')"
          @input="handlePasswordInput"
        />
      </n-form-item>

      <!-- 确认密码 -->
      <n-form-item
        ref="confirmPasswordFormItemRef"
        :show-label="false"
        path="confirmPassword"
      >
        <n-input
          v-model:value="formData.confirmPassword"
          type="password"
          show-password-on="click"
          :placeholder="confirmPasswordPlaceholder"
          size="large"
          :input-props="{ autocomplete: 'off' }"
          :theme-overrides="inputTheme"
          @focus="() => handleFieldFocus('confirmPassword')"
        />
      </n-form-item>

      <!-- 密码提示 -->
      <p class="text-[#5D688A] text-sm">
        {{
          $t(
            'password_complexity',
            'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
          )
        }}
      </p>

      <!-- 邮件推广和服务条款同意 -->
      <div v-if="props.showEmailPromotion || props.showTermsAndPrivacy">
        <n-form-item
          v-if="props.showEmailPromotion"
          path="agreeEmailPromotion"
          :show-label="false"
        >
          <n-checkbox
            v-model:checked="formData.agreeEmailPromotion"
            :theme-overrides="checkboxTheme"
          >
            <span
              :class="[
                'text-sm ',
                { 'text-white': formData.agreeEmailPromotion },
              ]"
            >
              {{
                $t(
                  'email_receive_promotion',
                  'I would like to receive promotions, events, and updates via email',
                )
              }}
            </span>
          </n-checkbox>
        </n-form-item>

        <n-form-item
          v-if="props.showTermsAndPrivacy"
          ref="agreeTermsFormItemRef"
          path="agreeTermsAndPrivacy"
          :show-label="false"
        >
          <n-checkbox
            v-model:checked="formData.agreeTermsAndPrivacy"
            :theme-overrides="agreeTermsCheckboxTheme"
            @update:checked="() => handleFieldFocus('agreeTermsAndPrivacy')"
          >
            <span
              :class="[
                'text-sm ',
                { 'text-white': formData.agreeTermsAndPrivacy },
              ]"
            >
              <i18n-t keypath="agree_privacy" scope="global">
                <template #a>
                  <span
                    text
                    :class="[
                      'text-sm  text-purple-1 p-0 h-auto underline',
                      { '!text-white': formData.agreeTermsAndPrivacy },
                    ]"
                    @click.stop="openTerms"
                  >
                    {{ $t('terms_of_service', 'Terms of Service') }}
                  </span>
                </template>
                <template #b>
                  <span
                    text
                    :class="[
                      'text-sm  text-purple-1 p-0 h-auto underline',
                      { '!text-white': formData.agreeTermsAndPrivacy },
                    ]"
                    @click.stop="openPrivacy"
                  >
                    {{ $t('privacy_policy', 'Privacy Policy') }}
                  </span>
                </template>
              </i18n-t>
            </span>
          </n-checkbox>
        </n-form-item>
      </div>

      <!-- Cloudflare验证  -->
      <CloudflareTurnstile
        ref="turnstileRef"
        size="flexible"
        retry="auto"
        :site-key="config.public.TURNSTILE_SITE_KEY"
        @verified="handleTurnstileVerified"
        @error="handleTurnstileError"
      />

      <!-- 操作按钮 -->
      <n-button
        type="primary"
        size="large"
        block
        :loading="loading"
        class="h-12 btn-gradient-primary font-medium uppercase"
        @click="handleSubmit"
      >
        {{ submitButtonText }}
      </n-button>

      <!-- 分割线和Steam登录 (仅注册模式显示) -->
      <template v-if="mode === 'register'">
        <!-- 分割线 -->
        <div class="flex items-center my-6">
          <div class="flex-1 h-px bg-[#5D688A]"></div>
          <span class="px-4 text-[#5D688A] text-sm font-medium uppercase">
            {{ $t('or_select', 'or select') }}
          </span>
          <div class="flex-1 h-px bg-[#5D688A]"></div>
        </div>

        <!-- Steam登录 -->
        <div class="flex justify-center">
          <n-button
            class="h-12 min-w-[194px] btn-gradient-gray"
            @click="handleSteamLogin"
          >
            <template #icon>
              <BaseIcon name="login" class="w-5 h-5" />
            </template>
            {{ $t('sign_in_with_steam', 'SIGN IN WITH STEAM') }}
          </n-button>
        </div>
      </template>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormRules } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';
import { isValidEmail } from '~/utils/validation';

type FormMode = 'register' | 'bind' | 'reset_password';

interface BaseEmailFormData {
  email: string;
  verificationCode: string;
  password: string;
  confirmPassword: string;
  agreeEmailPromotion?: boolean;
  agreeTermsAndPrivacy?: boolean;
}

interface Props {
  mode: FormMode;
  defaultEmail?: string;
  emailReadonly?: boolean;
  showEmailPromotion?: boolean;
  showTermsAndPrivacy?: boolean;
}

interface Emits {
  (e: 'success'): void;
  (e: 'switch-to-login'): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  defaultEmail: '',
  emailReadonly: false,
  showEmailPromotion: false,
  showTermsAndPrivacy: false,
});
const emit = defineEmits<Emits>();

const formRef = ref<FormInst>();
const emailFormItemRef = ref();
const codeFormItemRef = ref();
const passwordFormItemRef = ref();
const confirmPasswordFormItemRef = ref();
const turnstileRef = ref();
const loading = ref(false);
const sendCodeLoading = ref(false);
const countdown = ref(0);
const turnstileToken = ref('');
const agreeTermsFormItemRef = ref();
const config = useRuntimeConfig();
const settingStore = useSettingStore();
const { settingApi, loginApi } = useApi();
const toast = useAppToast();
const { t } = useI18n();

const formData = reactive<BaseEmailFormData>({
  email: props.defaultEmail || '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
  agreeEmailPromotion: false,
  agreeTermsAndPrivacy: false,
});

const inputTheme = {
  color: 'transparent',
  colorFocus: 'transparent',
  heightMedium: '38px',
  border: '1px solid #49536F',
  borderFocus: '1px solid #49536F',
  borderHover: '1px solid #49536F',
  boxShadowFocus: 'transparent',
};

const readonlyInputTheme = {
  color: '#2D334A',
  colorFocus: '#2D334A',
  border: '1px solid #2D334A',
  heightMedium: '38px',
  borderFocus: '1px solid #2D334A',
};

const agreeTermsCheckboxTheme = computed(() => {
  const hasError =
    agreeTermsFormItemRef.value?.mergedValidationStatus === 'error';

  return {
    ...(hasError && {
      border: '1px solid #e74c3c',
      borderFocus: '1px solid #e74c3c',
      borderHover: '1px solid #e74c3c',
      boxShadowFocus: '0 0 0 2px rgba(231, 76, 60, 0.2)',
    }),
  };
});

// 验证码倒计时存储
const COUNTDOWN_STORAGE_KEYS = {
  register: 'email_verification_countdown_register',
  bind: 'email_verification_countdown_bind',
  reset_password: 'email_verification_countdown_reset_password',
};

// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null;

const passwordLabel = computed(() => {
  switch (props.mode) {
    case 'register':
      return t('password', 'Password');
    case 'bind':
      return t('set_password', 'Set Password');
    case 'reset_password':
      return t('new_password', 'New Password');
    default:
      return t('password', 'Password');
  }
});

const passwordPlaceholder = computed(() => {
  switch (props.mode) {
    case 'register':
      return t('enter_password', 'Please enter password');
    case 'bind':
      return t('enter_password', 'Please enter password');
    case 'reset_password':
      return t('enter_new_password', 'Please enter new password');
    default:
      return t('enter_password', 'Please enter password');
  }
});

const confirmPasswordPlaceholder = computed(() => {
  switch (props.mode) {
    case 'reset_password':
      return t(
        'confirm_new_password_placeholder',
        'Please re-enter new password',
      );
    default:
      return t('confirm_password_placeholder', 'Please re-enter your password');
  }
});

const submitButtonText = computed(() => {
  switch (props.mode) {
    case 'register':
      return t('confirm_register', 'Confirm Registration');
    case 'bind':
      return t('confirm_bind_email', 'Confirm binding');
    case 'reset_password':
      return t('confirm_reset_password', 'Confirm password reset');
    default:
      return t('confirm', 'Confirm');
  }
});

const checkboxTheme = computed(() => {
  return {};
});

// 格式化邮箱显示
const maskedEmail = computed(() => {
  return props.emailReadonly ? formatEmail(formData.email) : formData.email;
});

// 获取倒计时存储
const getCountdownStorageKey = () => {
  return COUNTDOWN_STORAGE_KEYS[props.mode];
};

const saveCountdownState = (remainingTime: number) => {
  const storageKey = getCountdownStorageKey();
  const expireTime = Date.now() + remainingTime * 1000;
  localStorage.setItem(storageKey, expireTime.toString());
};

// 恢复倒计时状态
const restoreCountdownState = () => {
  const storageKey = getCountdownStorageKey();
  const expireTimeStr = localStorage.getItem(storageKey);

  if (!expireTimeStr) return 0;

  const expireTime = parseInt(expireTimeStr);
  const now = Date.now();
  const remainingTime = Math.max(0, Math.ceil((expireTime - now) / 1000));

  if (remainingTime <= 0) {
    localStorage.removeItem(storageKey);
    return 0;
  }

  return remainingTime;
};

// 清除倒计时状态
const clearCountdownState = () => {
  const storageKey = getCountdownStorageKey();
  localStorage.removeItem(storageKey);
};

// 开始倒计时
const startCountdown = (initialTime: number = 60) => {
  countdown.value = initialTime;
  saveCountdownState(initialTime);

  if (countdownTimer) {
    clearInterval(countdownTimer);
  }

  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value > 0) {
      saveCountdownState(countdown.value);
    } else {
      clearInterval(countdownTimer!);
      countdownTimer = null;
      clearCountdownState();
    }
  }, 1000);
};

const validatePasswordComplexity = (_rule: any, value: string) => {
  if (!value) return true;

  const hasUpperCase = /[A-Z]/.test(value);
  const hasLowerCase = /[a-z]/.test(value);
  const hasNumbers = /\d/.test(value);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

  const validTypes = [
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar,
  ].filter(Boolean).length;

  return validTypes >= 2;
};

const validateConfirmPassword = (_rule: any, value: string) => {
  if (!value) return true;
  return value === formData.password;
};

const rules: FormRules = {
  email: [
    {
      required: true,
      message: t('enter_email', 'Enter your email address'),
      trigger: ['blur'],
    },
    {
      type: 'email',
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
    {
      validator: (_rule, value) => {
        return isValidEmail(value);
      },
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
  ],
  verificationCode: [
    {
      min: 1,
      max: 6,
      required: true,
      message: t(
        'enter_verification_code',
        'Enter the verification code sent to your email',
      ),
      trigger: ['blur'],
    },
  ],
  password: [
    {
      required: true,
      message: t('password_required', 'Enter password'),
      trigger: ['blur'],
    },
    {
      min: 8,
      max: 16,
      message: t(
        'password_complexity',
        'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
      ),
      trigger: ['blur'],
    },
    {
      validator: validatePasswordComplexity,
      message: t(
        'password_complexity',
        'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
      ),
      trigger: ['blur'],
    },
  ],
  confirmPassword: [
    {
      required: true,
      message: t('confirm_password_required', 'Please re-enter your password'),
      trigger: ['blur'],
    },
    {
      validator: validateConfirmPassword,
      message: t(
        'email_or_password_incorrect',
        'Please fill in the correct information',
      ),
      trigger: ['blur'],
    },
    {
      validator: validatePasswordComplexity,
      message: t(
        'password_complexity',
        'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
      ),
      trigger: ['blur'],
    },
  ],
  ...(props.mode === 'register'
    ? {
        ...(props.showTermsAndPrivacy
          ? {
              agreeTermsAndPrivacy: [
                {
                  validator: (_rule, value) => {
                    return value === true;
                  },
                  message: t(
                    'agree_terms_and_privacy',
                    'Please agree to the Terms of Service and Privacy Agreement.',
                  ),
                  trigger: ['change'],
                },
              ],
            }
          : {}),
      }
    : {}),
};

const handleTurnstileVerified = (token: string) => {
  turnstileToken.value = token;
};

const handleTurnstileError = (error: any) => {
  console.error('Turnstile error:', error);
  toast.error({
    content: t(
      'turnstile_verification_failed',
      'Your current network or system environment is abnormal. Please retry the human verification.',
    ),
  });
  turnstileToken.value = '';
};

const sendVerificationCode = async () => {
  if (!formData.email || !isValidEmail(formData.email)) {
    toast.error({
      content: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
    });
    return;
  }

  try {
    sendCodeLoading.value = true;
    const res = await settingApi.emailApply({
      email: formData.email,
      event: props.mode,
    });

    if (res.data.value.code === 0) {
      toast.success({
        content: t(
          'verification_code_sent',
          'Sent successfully. Please check your email.',
        ),
      });
      startCountdown(60);
    }
  } catch (error: any) {
  } finally {
    sendCodeLoading.value = false;
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (!turnstileToken.value) {
      toast.error({
        content: t(
          'human_verification_required',
          'Please complete the human verification',
        ),
      });
      return;
    }

    loading.value = true;
    let res;

    try {
      switch (props.mode) {
        case 'register': {
          // 注册
          res = await loginApi.emailRegister({
            email: formData.email,
            code: formData.verificationCode,
            password: formData.password,
            confirm_password: formData.confirmPassword,
            cf_token: turnstileToken.value,
            invite_code: settingStore.inviteCode,
            platform: settingStore.platform ? 1 : 0,
            is_subscribe_ads: formData.agreeEmailPromotion ? 1 : 2,
          });
          const data = res.data.value;
          if (data.code === 0) {
            await settingStore.setTokenSafely(data.data?.token);
            emit('success');
          }
          break;
        }
        case 'bind':
          // 绑定邮箱
          res = await settingApi.emailVerify({
            email: formData.email,
            code: formData.verificationCode,
            password: formData.password,
            confirm_password: formData.confirmPassword,
            cf_token: turnstileToken.value,
            is_subscribe_ads: formData.agreeEmailPromotion ? 1 : 2,
          });
          break;
        case 'reset_password':
          // 重置密码
          res = await loginApi.emailResetPassword({
            email: formData.email,
            code: formData.verificationCode,
            new_password: formData.password,
            confirm_new_password: formData.confirmPassword,
            cf_token: turnstileToken.value,
          });
          break;
      }
    } catch (error) {
      // 重置验证码
      turnstileRef.value?.reset();
      turnstileToken.value = '';
    }

    const successMessages = {
      register: undefined,
      bind: t('bind_success', 'Binding successful'),
      reset_password: t(
        'reset_password_success',
        'You can now log in with your new password',
      ),
    };
    if (res?.data.value.code === 0) {
      if (successMessages[props.mode] !== undefined) {
        toast.success({
          content: successMessages[props.mode],
        });
      }
      emit('success');
    } else {
      // 重置验证码
      turnstileRef.value?.reset();
      turnstileToken.value = '';
    }
  } catch (error: any) {
    if (Array.isArray(error)) {
      const flatErrors = error.flat();
      if (error.length === 1 && flatErrors[0]?.message) {
        toast.error({
          content: t(flatErrors[0].message),
        });
      } else {
        toast.error({
          content: t(
            'email_or_password_incorrect',
            'Please fill in the correct information',
          ),
        });
      }
    }
  } finally {
    loading.value = false;
  }
};

const handleSteamLogin = () => {
  settingStore.signin({
    openDialog: false,
    event: 'login',
  });
};

const openTerms = () => {
  window.open('/document/agreement', '_blank');
};

const openPrivacy = () => {
  window.open('/document/privacy', '_blank');
};

const handleFieldFocus = (fieldName?: string) => {
  if (!fieldName) {
    return;
  }
  const formItemRefMap = {
    email: emailFormItemRef,
    verificationCode: codeFormItemRef,
    password: passwordFormItemRef,
    confirmPassword: confirmPasswordFormItemRef,
    agreeTermsAndPrivacy: agreeTermsFormItemRef,
  };

  const formItemRef = formItemRefMap[fieldName as keyof typeof formItemRefMap];
  if (formItemRef?.value) {
    formItemRef.value.restoreValidation();
  }
};

const handlePasswordInput = () => {
  if (formData.confirmPassword && confirmPasswordFormItemRef.value) {
    nextTick(() => {
      confirmPasswordFormItemRef.value?.validate();
    });
  }
};

onMounted(() => {
  const remainingTime = restoreCountdownState();
  if (remainingTime > 0) {
    startCountdown(remainingTime);
  }
});

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
});
</script>

<style scoped lang="scss"></style>
