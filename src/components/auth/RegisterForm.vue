<template>
  <div class="register-form">
    <BaseEmailForm
      mode="register"
      :show-email-promotion="true"
      :show-terms-and-privacy="true"
      @success="handleSuccess"
      @switch-to-login="emit('switch-to-login')"
    />
  </div>
</template>

<script setup lang="ts">
import BaseEmailForm from './BaseEmailForm.vue';

interface Emits {
  (e: 'success'): void;
  (e: 'switch-to-login'): void;
}

const emit = defineEmits<Emits>();

const handleSuccess = () => {
  emit('success');
};
</script>
