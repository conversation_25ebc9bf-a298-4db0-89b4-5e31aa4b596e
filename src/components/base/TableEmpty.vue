<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="py-28 w-full flex flex-col items-center">
    <img src="/imgs/empty.png" width="180" height="16" />
    <div class="mt-[30px] mb-6 text-lg/5 font-bold text-white">
      <slot>
        {{ $t(emptytext) }}
      </slot>
    </div>
    <slot name="action"></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  emptytext: {
    type: String,
    default: 'no_data',
  },
});
</script>
