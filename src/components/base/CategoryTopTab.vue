<!-- eslint-disable vue/no-v-html -->
<template>
  <div
    :class="[
      'scroll-wrap relative flex items-center w-full',
      {
        'scroll-wrap-left': canScrollLeft,
        'scroll-wrap-right': canScrollRight,
      },
    ]"
  >
    <!-- 左滑动按钮 -->
    <div
      v-show="canScrollLeft"
      :class="[
        'absolute left-0 z-10 flex items-center justify-center   size-[30px] cursor-pointer  rounded-full opacity-85 transition-all duration-300  border-[1px] border-[#404967] shadow-[0_0_8px_#323A51] hover:bg-[#28314A]',
      ]"
      @click="scrollToLeft"
    >
      <BaseIcon
        name="back"
        class="w-[6px] h-[8px] text-white"
        :filled="false"
      />
    </div>
    <!-- 右滑动按钮 -->
    <div
      v-show="canScrollRight"
      :class="[
        'absolute right-0 z-10 flex items-center justify-center size-[30px] cursor-pointer    rounded-full  opacity-85 transition-all duration-300 border-[1px] border-[#404967] shadow-[0_0_8px_#323A51] hover:bg-[#28314A]',
      ]"
      @click="scrollToRight"
    >
      <BaseIcon
        name="back"
        class="w-[6px] h-[8px] text-white rotate-180"
        :filled="false"
      />
    </div>
    <!-- 滚动容器 -->
    <div
      v-if="!isPageLoading"
      ref="scrollContainer"
      class="flex gap-md overflow-x-auto scroll-smooth no-scrollbar w-full"
      @scroll="handleScroll"
    >
      <div
        v-for="(item, index) in categoryList"
        :key="`${item?.value || ''}-${index}`"
        ref="categoryItemRefs"
        :class="[
          'flex-shrink-0 min-w-[130px]  cursor-pointer group',
          cardClass,
        ]"
        @click="handleTypeparent(item)"
      >
        <Tooltip
          trigger="hover"
          :raw="true"
          placement="bottom"
          :show-arrow="false"
          :arrow-wrapper-style="{ padding: '0 !important' }"
          @update:show="(show) => onHide(show, index)"
        >
          <template #trigger>
            <div
              class="bg-dark-3 w-full h-[56px] px-[12px] radius-md flex items-center justify-center gap-md text-purple-1 uppercase hover:bg-[#28314A] hover:text-white"
              :class="[
                activeCategory[0]?.title == item?.title
                  ? '!bg-[#28314A] !text-white'
                  : 'text-[#B0AAA7]',
              ]"
            >
              <slot name="header">
                <BaseIcon
                  :name="'category-' + item.value"
                  :class="[
                    'size-[30px]',
                    'group-hover:text-primary-400',
                    activeCategory[0]?.title == item?.title
                      ? 'text-primary-400'
                      : '',
                  ]"
                  :filled="false"
                  :font-controlled="false"
                />
              </slot>
              <slot name="title">
                <span class="font-semibold whitespace-nowrap">
                  {{ $td(item?.value || '', item?.title) }}
                </span>
              </slot>
            </div>
          </template>
          <div
            v-if="item.list?.length"
            :class="[
              'mt-1 p-[10px] bg-[#151A29] border border-[#323A51]  grid grid-cols-2 gap-2 radius-md ',
              item.list?.length > 30 ? 'grid-cols-4' : 'grid-cols-2',
            ]"
          >
            <div
              v-for="(subclass, idx) in item.list"
              :key="idx"
              :class="[
                'w-full px-[10px] py-[11px] flex items-center justify-start base-color font-semibold  rounded cursor-pointer hover:bg-[#28314A] hover:text-white',
                activeCategory[0] == item && activeCategory.includes(subclass)
                  ? 'bg-[#28314A] !text-white'
                  : '!text-purple-1',
              ]"
              @click="handleTypeson(item, subclass)"
            >
              <span>{{ $td(subclass.title || '', subclass.title) }}</span>
            </div>
          </div>
        </Tooltip>
      </div>
    </div>
    <div
      v-else
      class="flex gap-md xl:grid xl:grid-cols-[repeat(auto-fit,minmax(130px,1fr))] h-[56px] overflow-x-auto scroll-smooth no-scrollbar w-full"
    >
      <n-skeleton
        :repeat="10"
        class="w-full h-[56px] flex-shrink-0"
      ></n-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface CategoryItem {
  value: string;
  title: string;
  attribute: string;
  list?: CategoryItem[];
  isPageLoading?: boolean;
}

const props = defineProps({
  categoryList: {
    required: true,
    type: Array as PropType<V1GetGoodsFilterReply['categories']>,
    default: () => [],
  },
  cardClass: {
    type: String,
    default: '',
  },
  active: {
    type: Array as () => any[],
    default: () => [],
  },
  isPageLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:activeCategory']);
// hover的索引
const currentInxNumber = ref(-1);

const activeCategory = ref<any[]>([]);

let observer: MutationObserver | null = null;
const categoryItemRefs = ref<HTMLElement[]>([]);

watch(
  () => props.active,
  (newval, olaval) => {
    if (newval !== olaval && newval.length > 0) {
      activeCategory.value = newval;
    } else {
      activeCategory.value = [props.categoryList?.[0]];
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => categoryItemRefs.value,
  (newval) => {
    if (newval?.length > 0) {
      nextTick(() => {
        observer = new MutationObserver(() => {
          if (scrollContainer.value) {
            checkScrollButtons();
          }
        });

        if (scrollContainer.value) {
          observer.observe(scrollContainer.value, {
            childList: true,
            subtree: true,
            attributes: false,
          });
          checkScrollButtons();
        }
        window.addEventListener('resize', checkScrollButtons);
      });
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

const updateMultiSelection = (
  currentSelection: CategoryItem[],
  subclass: CategoryItem,
): CategoryItem[] => {
  const idx = currentSelection.findIndex(
    (item) => item.value === subclass.value,
  );
  const newSelection =
    idx > -1
      ? currentSelection.filter((item) => item.value !== subclass.value)
      : [...currentSelection, subclass];

  return newSelection.filter(
    (item) => item.title?.toLocaleLowerCase() !== 'all',
  );
};

const emitUpdatedSelection = (
  parentItem: CategoryItem,
  selection: CategoryItem[],
) => {
  const params = {
    [parentItem?.attribute]: [parentItem?.value],
    [selection[1]?.attribute]: selection.slice(1).map((item) => item.value),
  };
  emit('update:activeCategory', params);
};

const onHide = (show: boolean, index?: any) => {
  nextTick(() => {
    currentInxNumber.value = !show ? -1 : index;
  });
};
const handleTypeparent = (item: any) => {
  if (item.value?.toLocaleLowerCase() === 'all') {
    activeCategory.value = [item];
    emit('update:activeCategory', {});
    return;
  }

  activeCategory.value = [item];
  emit('update:activeCategory', {
    [item.attribute]: [item.value],
  });
};
const handleTypeson = (item: any, subclass: any) => {
  const isNewCategory = activeCategory.value[0]?.title !== item.title;
  const isUnlimited = subclass.title?.toLocaleLowerCase() === 'all';

  if (isUnlimited || isNewCategory || activeCategory.value.length < 2) {
    activeCategory.value = [item, subclass];
  } else {
    activeCategory.value = updateMultiSelection(activeCategory.value, subclass);
  }
  emitUpdatedSelection(item, activeCategory.value);
};

const resetStateMarketFilter = () => {
  activeCategory.value = [props.categoryList?.[0]];
};

const scrollContainer = ref<HTMLElement | null>(null);
const canScrollLeft = ref(false);
const canScrollRight = ref(false);

const checkScrollButtons = () => {
  if (!scrollContainer.value) return;

  const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.value;
  // 如果内容宽度大于容器宽度，则显示右按钮
  canScrollRight.value = Math.ceil(scrollLeft + clientWidth) < scrollWidth;
  // 如果已经滚动，则显示左按钮
  canScrollLeft.value = scrollLeft > 0;
};

const handleScroll = () => {
  checkScrollButtons();
};

const scrollToLeft = () => {
  if (!scrollContainer.value) return;
  scrollContainer.value.scrollBy({
    left: -240,
    behavior: 'smooth',
  });
};

const scrollToRight = () => {
  if (!scrollContainer.value) return;
  scrollContainer.value.scrollBy({
    left: 240,
    behavior: 'smooth',
  });
};

onUnmounted(() => {
  observer?.disconnect();
  window.removeEventListener('resize', checkScrollButtons);
});
defineExpose({
  resetStateMarketFilter,
});
</script>

<style lang="scss" scoped>
:global(.v-popper--theme-category-menu .v-popper__arrow-container) {
  display: none !important;
}
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
.scroll-wrap-right {
  @apply relative;
  &::after {
    @apply content-[''] absolute right-0 top-0 w-[80px] h-full pointer-events-none;
    background: linear-gradient(270deg, #151a29 40%, hsla(0, 0%, 100%, 0));
    &:hover {
      background: linear-gradient(270deg, #28314a 40%, hsla(0, 0%, 100%, 0));
    }
  }
}
.scroll-wrap-left {
  @apply relative;
  &::before {
    @apply content-[''] absolute left-0 top-0 w-[80px] h-full pointer-events-none;
    background: linear-gradient(90deg, #151a29 40%, hsla(0, 0%, 100%, 0));
    &:hover {
      background: linear-gradient(90deg, #28314a 40%, hsla(0, 0%, 100%, 0));
    }
  }
}
</style>
