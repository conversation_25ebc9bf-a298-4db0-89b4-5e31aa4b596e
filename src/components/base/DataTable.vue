<template>
  <div>
    <slot name="title"></slot>
    <n-data-table v-bind="$attrs" class="base-color">
      <template #empty>
        <Empty :msg="emptyMsg || $t('no_data')" column class="bg-transparent" />
      </template>
      <template v-for="(_item, key, i) in $slots" :key="i" #[key]>
        <slot :name="key"></slot>
      </template>
    </n-data-table>
    <!-- paginator -->
    <div
      v-if="paginator"
      v-show="paginatorShow"
      class="wallet-list px-[20px] py-3.5"
    >
      <ListPaginator
        :total="paginator.total"
        :page="paginator.page"
        @update:page="updatePage"
      ></ListPaginator>
    </div>
  </div>
</template>

<script setup lang="ts">
type Pagination = {
  total: number;
  page: number;
  page_size: number;
};
type TableProps = {
  paginator?: Pagination;
  emptyMsg?: string;
};
// const slots = useSlots();
// const attrs = useAttrs();
const props = defineProps<TableProps>();
const paginatorShow = computed(() => {
  if (props.paginator) {
    return props.paginator?.total > props.paginator?.page_size;
  }
  return false;
});

const emit = defineEmits(['update:page']);

// 切换页码
const updatePage = (updateData: Pagination) => {
  emit('update:page', updateData);
};
</script>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-th) {
  border-bottom: none !important;
}
</style>
