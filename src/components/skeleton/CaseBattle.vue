<template>
  <div class="bg-[#151A29] h-[230px] rounded-[8px]">
    <div
      class="flex justify-between items-center pt-[18px] pb-[14px] px-[16px]"
    >
      <n-skeleton height="33px" width="270px" />
      <div class="flex items-center gap-x-[7px]">
        <n-skeleton height="34px" width="33px" />
        <BaseIcon name="battle" class="!text-[20px]"></BaseIcon>
        <n-skeleton height="34px" width="33px" />
      </div>
    </div>
    <div
      class="bg-[#20263B] h-[98px] w-full flex items-center gap-x-[4px] mb-[12px] pl-[16px] overflow-hidden"
    >
      <base-icon
        v-for="index in 7"
        :key="index"
        name="case-skeleton"
        class="shrink-0 size-[90px]"
        :font-controlled="false"
      />
    </div>
    <div class="flex justify-between items-center px-[16px]">
      <n-skeleton height="22px" width="150px" />
      <n-skeleton height="42px" width="128px" class="rounded-[8px]" />
    </div>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped></style>
