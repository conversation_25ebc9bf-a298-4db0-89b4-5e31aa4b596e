<template>
  <Tooltip
    :trigger="tooltip ? 'hover' : 'manual'"
    :placement="placement"
    @update:show="tooltipUpdate"
  >
    <template #trigger>
      <span
        class="flex items-center gap-sm"
        v-bind="$attrs"
        :class="{
          'hover:text-theme-color': !disabled,
          'cursor-pointer': !disabled,
        }"
        @click="handleCopy"
      >
        <slot v-if="showIcon" name="icon">
          <svgo-battles-copy
            :class="{ 'opacity-50': disabled, 'size-[24px] mb-0': !disabled }"
          />
        </slot>
        <slot name="default">
          <span>{{ $td(text) }}</span>
        </slot>
      </span>
    </template>
    <span class="text-sm text-white">{{ currentTooltipText }}</span>
  </Tooltip>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useTimeoutFn } from '@vueuse/core';
import { useI18n } from 'vue-i18n';

interface Props {
  text?: string;
  textToCopy?: string;
  tooltipSuccess?: string;
  tooltipDefault?: string;
  tooltipError?: string;
  disabled?: boolean;
  tooltip?: boolean;
  showIcon?: boolean;
  copyable?: boolean;
  tooltipDuration?: number;
  placement?:
    | 'top-start'
    | 'top'
    | 'top-end'
    | 'right-start'
    | 'right'
    | 'right-end'
    | 'bottom-start'
    | 'bottom'
    | 'bottom-end'
    | 'left-start'
    | 'left'
    | 'left-end';
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

const props = withDefaults(defineProps<Props>(), {
  tooltipDuration: 1500,
  disabled: false,
  tooltip: true,
  showIcon: false,
  tooltipSuccess: 'replicating_success',
  tooltipDefault: 'click_to_copy_to_clipboard',
  tooltipError: '复制失败',
  text: '',
  textToCopy: '',
  placement: 'top',
  onSuccess: () => {},
  onError: (_error: Error) => {},
});

const { t, te } = useI18n();
const { copy } = useCopy({});

const copyStatus = ref<'default' | 'success' | 'error'>('default');
const showTooltip = ref(false);

const currentTooltipText = computed(() => {
  switch (copyStatus.value) {
    case 'success':
      return te(props.tooltipSuccess)
        ? t(props.tooltipSuccess)
        : props.tooltipSuccess;
    case 'error':
      return te(props.tooltipError)
        ? t(props.tooltipError)
        : props.tooltipError;
    default:
      return te(props.tooltipDefault)
        ? t(props.tooltipDefault)
        : props.tooltipDefault;
  }
});

const resetStatus = () => {
  copyStatus.value = 'default';
  showTooltip.value = false;
};

const { start: hideTooltip } = useTimeoutFn(resetStatus, props.tooltipDuration);

const tooltipUpdate = (value: boolean) => {
  if (!value) {
    setTimeout(() => {
      resetStatus();
    }, 500);
  }
};

const handleCopy = async () => {
  try {
    if (props.disabled) return;
    await copy(props.textToCopy ?? props.text);
    copyStatus.value = 'success';
    showTooltip.value = true;
    hideTooltip();
    props.onSuccess?.();
  } catch (error) {
    copyStatus.value = 'error';
    showTooltip.value = true;
    hideTooltip();
    props.onError?.(error as Error);
  }
};
</script>
