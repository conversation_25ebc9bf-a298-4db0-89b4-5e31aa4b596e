<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <!-- 投注金额输入 -->
  <div
    class="flex items-center flex-wrap w-full gap-[8px] lg:w-max"
    :class="{ 'sm:my-0': fastBtns.length < 5 }"
  >
    <div class="w-full lg:w-auto">
      <n-input-number
        :value="betAmount"
        :precision="2"
        :show-button="false"
        size="large"
        :min="min"
        :max="max"
        :placeholder="`${$t('amount')}...`"
        :theme-overrides="themeOverrides"
        @update:value="(e) => updateAmount(e)"
      >
        <template #prefix>
          <div class="mr-[2px]">
            <svgo-gold filled class="w-[22px] h-[22px]"></svgo-gold>
          </div>
        </template>
      </n-input-number>
    </div>
    <div
      class="flex items-center gap-[4px] border border-[#323A51] px-[8px] h-[42px] rounded max-lg:w-full max-lg:grid max-md:grid-cols-5 max-lg:grid-cols-7"
    >
      <BetAmountButton
        class="hidden md:inline-flex"
        @click="updateAmount(null)"
      >
        {{ $t('clear') }}
      </BetAmountButton>
      <BetAmountButton
        v-for="(item, index) in fastBtns"
        :key="index"
        @click="updateAmount(add(betAmount || 0, item))"
      >
        +{{ item }}
      </BetAmountButton>
      <BetAmountButton @click="updateAmount(divide(betAmount || 0, 2))">
        1/2
      </BetAmountButton>
      <BetAmountButton
        @click="updateAmount(Number(multiply(betAmount || 0, 2)))"
      >
        x2
      </BetAmountButton>
      <BetAmountButton
        class="hidden md:inline-flex"
        @click="updateAmount(userAmount)"
      >
        {{ 'MAX' }}
      </BetAmountButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { InputNumberProps } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';
import { BigNumberCalc } from '~/utils/index';
type ThemeOverrides = NonNullable<InputNumberProps['themeOverrides']>;
const themeOverrides: ThemeOverrides = {
  peers: {
    Input: {
      paddingLarge: '8px',
      borderRadius: '4px ',
      textColor: '#fff',
      fontSizeMedium: '14px',
      border: 'solid 1px #323A51',
      color: 'transparent',
      placeholderColor: '#4E5B7E',
    },
  },
};
const { add, multiply, divide } = BigNumberCalc;
withDefaults(
  defineProps<{
    betAmount: number | null;
    fastBtns?: number[];
    min?: number;
    max?: number;
  }>(),
  {
    fastBtns: () => [0.01, 0.1, 1, 10, 100],
    min: 0.01,
    max: 99999.99,
  },
);
const $emit = defineEmits(['update:betAmount']);
const settingStore = useSettingStore();
const userAmount = computed(
  () => settingStore.userInfo?.balance?.balance || null,
);
const updateAmount = (amount: number | string | null) => {
  if (typeof amount === 'string') {
    amount = Number(amount);
  }
  if (amount === 0) {
    amount = null;
  }
  if (amount && amount > 99999.99) {
    amount = 99999.99;
  }
  $emit('update:betAmount', amount);
};
</script>
<style lang="scss" scoped></style>
