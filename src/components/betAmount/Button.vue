<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-button
    :theme-overrides="themeOverrides"
    class="px-[12px] h-[30px] text-[14px] leading-tight min-w-fit"
  >
    <slot></slot>
  </n-button>
</template>
<script setup lang="ts">
import type { ButtonProps } from 'naive-ui';
type ButtonOverrides = NonNullable<ButtonProps['themeOverrides']>;
const themeOverrides: ButtonOverrides = {
  textColor: '#7D90CA',
  textColorFocus: '#7D90CA',
  textColorHover: '#F8B838',
  color: '#151A29',
  colorFocus: '#151A29',
  colorHover: '#151A29',
  border: 'none',
  borderFocus: 'none',
  borderHover: 'solid 1px #F8B838',
};
</script>
