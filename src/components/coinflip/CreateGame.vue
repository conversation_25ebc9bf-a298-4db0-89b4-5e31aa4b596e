<template>
  <div class="flex justify-end items-center gap-[8px] max-sm:flex-wrap">
    <div
      class="flex items-center border-1 border-[#323A51] px-3 rounded-[4px] h-[42px] flex-shrink-0"
    >
      <span class="mr-[2px] text-[#7D90CA]">{{ $t('side') }}:</span>
      <img
        src="/imgs/coin_gray.webp"
        alt=""
        class="w-[34px] h-[34px] mx-2 cursor-pointer opacity-50 hover:opacity-100 rounded-full border-[2px] border-transparent"
        :class="{
          '!opacity-100 !border-[#F8B838]': side === 1,
        }"
        @click="side = 1"
      />
      <img
        src="/imgs/coin_orange.webp"
        alt=""
        class="w-[34px] h-[34px] cursor-pointer opacity-50 hover:opacity-100 rounded-full border-[2px] border-transparent"
        :class="{
          '!opacity-100 !border-[#F8B838]': side === 2,
        }"
        @click="side = 2"
      />
    </div>
    <BetAmount v-model:betAmount="betAmount" :fastBtns="[1, 10, 100]" />
    <n-select
      v-model:value="roomNum"
      :options="gamesCountOpts"
      placeholder="lang"
      size="large"
      class="w-[65px] rounded-lg text-[14px]"
      :render-tag="renderSelectTag"
      :consistent-menu-width="false"
    />

    <MainButton @click="createGame">
      {{ $t('create') }} {{ $t('game', roomNum) }}
    </MainButton>
  </div>
</template>
<script lang="ts" setup>
import type { SelectRenderTag } from 'naive-ui';
const toast = useAppToast();
const { t } = useI18n();
const { coinflipApi } = useApi();
const { openConfirm } = useDialogPromptsConfirm('coinflipPrompts');
const $emit = defineEmits(['createGame']);
const gamesCountOpts = computed(() => [
  {
    label: `${t('one_game', { n: 1 })}`,
    value: 1,
  },
  {
    label: `${t('one_game', { n: 2 })}`,
    value: 2,
  },
  {
    label: `${t('one_game', { n: 3 })}`,
    value: 3,
  },
  {
    label: `${t('one_game', { n: 5 })}`,
    value: 5,
  },
  {
    label: `${t('one_game', { n: 10 })}`,
    value: 10,
  },
]);
// 1CT 灰面 / 2T 橘色面
const side = ref<number | null>(null);
const betAmount = ref<number | null>(null);
const roomNum = ref<number>(1);
const disabled = ref(false);

// 创建游戏
const createGame = () => {
  if (!checkLogin()) return;
  if (side.value === null) {
    return toast.error({ content: t('select_bet_side') });
  } else if (!betAmount.value) {
    return toast.error({ content: t('enter_bet') });
  } else if (betAmount.value < 1) {
    return toast.error({ content: t('401013') });
  }
  const params = {
    side: side.value,
    bet_amount: betAmount.value,
    room_num: roomNum.value,
  };
  openConfirm({
    title: t('confirm'),
    content: t('spend_coinflip_confirmation', {
      a: BigNumberCalc.multiply(betAmount.value, roomNum.value),
    }),
    onConfirm: async () => {
      if (!checkLogin()) return;
      disabled.value = true;
      const { data: req } = await coinflipApi.createGame(params);
      const data = req.value?.data;
      disabled.value = false;
      if (data) {
        toast.success({
          content: t('created_successfully'),
        });
        $emit('createGame', data.rounds);
      }
    },
  });
};
// select 选中项render
const renderSelectTag: SelectRenderTag = ({ option }) => {
  return option.value + 'x';
};
</script>
<style lang="scss" scoped></style>
