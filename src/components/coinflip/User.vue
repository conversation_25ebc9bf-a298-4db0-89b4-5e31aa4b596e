<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div
    class="flex-1 h-full flex flex-col bg-[#121621] items-center justify-center relative overflow-hidden rounded-lg pb-5"
    :style="boxStyle"
  >
    <div
      class="w-[200%] bg-[#1D2334] pb-[200%] rounded-full absolute bottom-[100%] translate-y-[20px] z-1"
    ></div>
    <img
      :src="coinimg[chosenSide - 1]"
      alt=""
      class="absolute w-[32px] h-[32px] left-1/2 -translate-x-1/2 top-[6px]"
    />
    <template v-if="user.uid">
      <div class="relative mt-[56px]">
        <div
          class="w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] rounded overflow-hidden"
        >
          <img :src="user.avatar" alt="" />
        </div>
      </div>
      <n-ellipsis
        class="max-w-[126px] mt-1 mb-2 text-[14px] sm:text-xl text-white text-center"
      >
        {{ user.steam_name }}
      </n-ellipsis>
      <UserLevel :level="user.level" />
      <div class="h-[10px] sm:h-[18px]"></div>
      <div
        class="flex items-center rou rounded bg-[rgb(0,0,0,0.2)] px-3 py-2.5 text-[#fff]"
      >
        <svgo-gold filled class="w-[22px] h-[22px] mb-0"></svgo-gold>
        <div
          :class="{
            'text-[#FD4058]': isWin === false,
            'text-[#3EFF95]': isWin,
          }"
          class="ml-1"
        >
          <span>{{ isWin ? '+' : isWin === false ? '-' : '' }}</span>
          <span>{{ isWin ? roomInfo.win_amount : roomInfo.bet_amount }}</span>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="relative w-max max-w-full">
        <template v-if="roomInfo.creator_dismissed">canceled</template>
        <template v-else>
          <div v-if="isSelf" class="flex flex-col gap-3 max-w-full">
            <MainButton :class="btnClass" :disabled="loading" @click="joinBot">
              {{ $t('join_bot') }}
            </MainButton>
            <MainButton
              :class="btnClass"
              type="linear"
              :disabled="loading"
              @click="cancelGame"
            >
              {{ $t('cancel') }}
            </MainButton>
          </div>
          <MainButton
            v-else
            :class="btnClass"
            :disabled="loading"
            @click="joinGame"
          >
            {{ $t('join_game') }}
          </MainButton>
        </template>
      </div>
      <div
        v-if="!roomInfo.creator_dismissed"
        class="absolute bottom-5 flex items-center bg-[rgb(0,0,0,0.2)] rounded px-3 py-2.5 text-white"
      >
        <svgo-gold filled class="w-[22px] h-[22px] mb-0"></svgo-gold>
        <span class="ml-1">{{ roomInfo.bet_amount }}</span>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useSettingStore } from '~/stores/modules/setting';
import type { RoomInfoType } from '~/types/coinflip';

const coinimg = ['/imgs/coin_gray.webp', '/imgs/coin_orange.webp'];
const props = withDefaults(
  defineProps<{
    isChallenger?: boolean;
    moduleId: string;
    resShow?: boolean;
    roomInfo: RoomInfoType;
    disabled: boolean;
  }>(),
  {
    isChallenger: false,
    moduleId: '',
    resShow: undefined,
    disabled: false,
  },
);
const btnClass =
  'max-sm:!min-w-[110px] max-sm:!text-[12px] max-sm:!h-[38px] max-w-full';
const $emit = defineEmits(['join-bot', 'cancel-game', 'join-game']);
const { openConfirm } = useDialogPromptsConfirm('coinflipPrompts');
const { t } = useI18n();
const { coinflipApi } = useApi();
const settingStore = useSettingStore();
const toast = useAppToast();
const loading = ref(false);
const isSelf = computed(() => props.moduleId !== 'open');
const roomId = computed(() => props.roomInfo.id);

const boxStyle = computed(() => {
  const isSelf = user.value.uid === settingStore.userInfo?.uid;
  if (isSelf && isWin.value !== undefined) {
    return {
      border: 'solid 2px ',
      borderColor: isWin.value ? '#80fb9f' : '#e9515d',
      background: isWin.value
        ? 'rgba(62, 255, 149, 0.1)'
        : 'rgba(253, 64, 88, 0.1)',
    };
  }
  return {
    opacity: isWin.value === false ? '0.3' : '1',
  };
});
// 用户信息
const user = computed(() => {
  if (props.isChallenger) {
    return props.roomInfo.challenger_profile || {};
  }
  return props.roomInfo.creator_profile || {};
});
// 选择正反
const chosenSide = computed(() => {
  const createrSide = props.roomInfo.creator_chosen_side || 1;
  if (props.isChallenger) {
    const challengerSide = createrSide === 1 ? 2 : 1;
    return challengerSide;
  }
  return Number(createrSide);
});
// 是否赢
const isWin = computed(() => {
  const res = Number(props.roomInfo.result || 0);
  if (res === 0 || !props.resShow || props.roomInfo.status === 9)
    return undefined;
  return res === chosenSide.value;
});
// 加入游戏
const joinGame = () => {
  if (!checkLogin()) return;
  openConfirm({
    title: t('confirm'),
    content: t('spend_coinflip_confirmation', {
      a: props.roomInfo.bet_amount || 0,
    }),
    onConfirm: async () => {
      if (!checkLogin()) return;
      loading.value = true;
      const { data: req } = await coinflipApi.joinGame(roomId.value as number);
      const data = req.value?.data;
      loading.value = false;
      if (data) {
        // 开始倒计时
        $emit('join-game', data.round);
      }
    },
  });
};
// 取消游戏
const cancelGame = async () => {
  loading.value = true;
  const { data: req } = await coinflipApi.cancelGame(roomId.value as number);
  const data = req.value?.data;
  loading.value = false;
  if (data) {
    $emit('cancel-game', data.round);
    toast.success({ content: t('coinflip_cancel_success') });
  }
};
// 加入机器人
const joinBot = async () => {
  loading.value = true;
  const { data: req } = await coinflipApi.callBot(roomId.value as number);
  const data = req.value?.data;
  loading.value = false;
  if (data) {
    $emit('join-bot', data.round);
  }
};
</script>
<style lang="scss" scoped></style>
