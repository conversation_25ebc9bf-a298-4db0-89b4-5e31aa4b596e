<template>
  <div
    class="bg-[#151A29] rounded-lg p-[8px] sm:p-[16px] flex items-center relative justify-center gap-[4px] sm:gap-[12px]"
    :class="{ 'bubble-ani': resShow && moduleId !== 'history' }"
  >
    <template v-if="roomInfo">
      <!-- 公平 -->
      <Fairness
        class="absolute -bottom-[10px] left-1/2 -translate-x-1/2 z-10 !text-[rgb(125,144,202,0.7)]"
        placement="left"
        size="xs"
        upperCase
        :showIcon="false"
        :fairInfo="fairInfo"
      />
      <!-- user1 -->
      <CoinflipUser
        :roomInfo="roomInfo"
        :resShow="resShow"
        :moduleId="moduleId"
        :disabled="loading"
      />
      <!-- vs / countdown -->
      <div
        class="w-[88px] h-[88px] shrink-0 rounded-full flex justify-center items-center text-[32px] text-white font-bold"
      >
        <!-- 倒计时 -->
        <template v-if="!coinShow">
          <n-countdown
            v-if="startCountDown"
            :render="({ seconds }) => seconds"
            :duration="5 * 1000"
            @finish="countDownFinish"
          />
          <svgo-vs v-else class="w-[65px] h-[65px] mb-0" filled />
        </template>
      </div>
      <!-- user2 -->
      <CoinflipUser
        :roomInfo="roomInfo"
        :resShow="resShow"
        :moduleId="moduleId"
        :disabled="loading"
        isChallenger
        @join-game="joinGame"
        @join-bot="joinBot"
        @cancel-game="cancelGame"
      />
      <!-- coin -->
      <div v-if="coinShow" class="flip-mask flip-mask-ani">
        <div class="w-[170px] h-[170px] overflow-hidden" :style="rotateZDeg">
          <div
            :class="
              Number(roomInfo.result) === 1 ? 'coinflip-back' : 'coinflip-front'
            "
          ></div>
        </div>
      </div>
      <div
        v-if="moduleId === 'history' && roomInfo.status !== 9"
        class="flip-mask z-[1]"
      >
        <img
          :src="coinimg[Number(roomInfo.result) - 1]"
          alt=""
          class="w-[88px]"
        />
      </div>
    </template>
    <div v-else class="h-[255px]"></div>
  </div>
</template>
<script lang="ts" setup>
import type { RoomInfoType } from '~/types/coinflip';

const coinimg = ['/imgs/coin_gray.webp', '/imgs/coin_orange.webp'];
const props = withDefaults(
  defineProps<{
    roomInfo: RoomInfoType | null;
    moduleId: string;
  }>(),
  {},
);
const { t } = useI18n();
const $emit = defineEmits([
  'final-game',
  'join-bot',
  'cancel-game',
  'join-game',
]);
// 公平性弹窗内容
const fairInfo = computed(() => {
  const roomInfo = props.roomInfo || {};
  const seedIndex = roomInfo.seed_index ? `（${roomInfo.seed_index}）` : '';
  return {
    msg: t('coinflip_fairness'),
    seeds: [
      { title: t('round_id'), value: roomInfo.id || '--' },
      {
        title: t('public_seed_hash'),
        value: roomInfo.private_seed_hash || '-',
      },
      { title: t('private_seed'), value: roomInfo.private_seed || '--' },
      {
        title: `${t('public_eos_seed')}${seedIndex}`,
        value: roomInfo.seed || '--',
      },
    ],
  };
});

// 硬币朝向的偏移量,随机
const rotateZDeg = computed(() => {
  const randomNum = Math.floor(Math.random() * 45); // 0-44
  const randomNum1 = Math.floor(Math.random() * 2); // 0-1
  return `transform: rotateZ(${randomNum1 > 0 ? '+' : '-'}${randomNum}deg)`;
});
// 开始倒计时
const startCountDown = ref(false);
const isCountDownFinish = ref(false);
// 抛硬币动画展示
const coinShow = ref(false);
// 结果展示
const resShow = ref(props.moduleId === 'history');
// 接口请求中,按钮disabled;
const loading = ref(false);

// 取消游戏
const cancelGame = (round: RoomInfoType) => {
  $emit('cancel-game', round);
};
// 加入机器人
const joinBot = (round: RoomInfoType) => {
  $emit('join-bot', round);
};
// 加入游戏
const joinGame = (round: RoomInfoType) => {
  $emit('join-game', round);
};

// 倒计时结束
const countDownFinish = () => {
  isCountDownFinish.value = true;
  if (!props.roomInfo?.result) return;
  isCountDownFinish.value = false;
  // 开始动画效果
  coinShow.value = true;
  setTimeout(() => {
    resShow.value = true;
    // 动画结束5s后隐藏
    setTimeout(() => {
      $emit('final-game');
    }, 5000);
  }, 3500);
};
if (props.roomInfo?.status === 1 || props.roomInfo?.status === 2) {
  startCountDown.value = true;
}
if (props.roomInfo?.status === 6 && props.moduleId === 'open') {
  countDownFinish();
}
watch(
  () => props.roomInfo,
  (newV, oldV) => {
    if (newV) {
      const status = Number(newV.status);
      if (status === 1 || status === 2) {
        startCountDown.value = true;
      }
      if (!oldV?.result && newV.result && isCountDownFinish.value) {
        countDownFinish();
      }
    }
  },
);
</script>
<style lang="scss" scoped>
.flip-mask {
  @apply absolute bottom-0 top-0 left-0 right-0 bg-[#2b2c36]/10 z-20  flex justify-center items-center;
}
.flip-mask-ani {
  backdrop-filter: blur(0px);
  animation: coinflipBg 3500ms linear forwards;
}
.coinflip-front,
.coinflip-back {
  width: 170px;
  height: 8670px;
  transform: translate3d(0, 0, 0);
  animation: coinflip 3500ms steps(50) forwards;
}
.coinflip-front {
  background: url('/imgs/coin_orange_ani.png') 0 / cover no-repeat;
}
.coinflip-back {
  background: url('/imgs/coin_gray_ani.png') 0 / cover no-repeat;
}
@keyframes coinflipBg {
  0% {
    backdrop-filter: blur(0px);
  }
  50% {
    backdrop-filter: blur(8px);
  }
  100% {
    backdrop-filter: blur(0px);
  }
}
// 图片大小50*2550
@keyframes coinflip {
  to {
    transform: translate3d(0, -8500px, 0);
  }
}

.bubble-ani {
  animation: bubble 200ms forwards;
}
@keyframes bubble {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}
</style>
