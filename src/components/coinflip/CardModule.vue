<template>
  <n-spin :show="props.moduleInfo?.loading && showSpin">
    <template v-if="props.moduleInfo?.loading && !showSpin">
      <div class="flip-box">
        <SkeletonCoinflip v-for="index in skeletonLength" :key="index" />
      </div>
    </template>
    <template v-else-if="emptyList">
      <div class="flip-box">
        <CoinflipCard
          v-for="(item, index) in list"
          :key="item?.id || index"
          :roomInfo="item"
          :moduleId="moduleId"
          @final-game="finalGame(item, index)"
          @join-bot="(res: RoomInfoType) => joinBot(index, res)"
          @cancel-game="cancelGame(index)"
          @join-game="(res: RoomInfoType) => joinGame(index, res)"
        />
      </div>
      <ListPaginator
        v-if="moduleInfo.page_size"
        :total="moduleInfo.total"
        :page-size="moduleInfo.page_size"
        :page="moduleInfo.page"
        @update:page="handlePage"
      ></ListPaginator>
    </template>
    <Empty
      v-else
      class="h-[192px]"
      :msg="$t(moduleInfo?.emptyMsg)"
      icon="empty-icon"
    ></Empty>
  </n-spin>
</template>
<script lang="ts" setup>
import { useCoinflipStore } from '~/stores/modules/coinflip';
import type { RoomInfoType } from '~/types/coinflip';
const props = withDefaults(
  defineProps<{
    list: (RoomInfoType | null)[];
    moduleInfo?: {
      page?: number;
      page_size?: number;
      total: number;
      loading: boolean;
      emptyMsg: string;
    };
    moduleId: string;
    pageLoading: boolean;
  }>(),
  {
    moduleInfo: undefined,
  },
);
const $emit = defineEmits(['update:page', 'final-game']);
const coinflipStore = useCoinflipStore();
// 当前展示list长度
const emptyList = computed(() => {
  if (props.moduleId === 'open') {
    return coinflipStore.openListMap.size;
  } else {
    return props.list.length;
  }
});
// loading类型
const showSpin = computed(() => {
  if (props.moduleId === 'history') {
    return Boolean(emptyList.value);
  } else {
    return !props.pageLoading;
  }
});
// 骨架屏card展示个数
const skeletonLength = computed(() => {
  if (props.moduleId === 'open') return 10;
  return 3;
});
// 分页
const handlePage = (page: number) => {
  $emit('update:page', page);
};
// 游戏结束-从列表中删除
const finalGame = async (item: RoomInfoType | null, index: number) => {
  if (props.moduleId === 'open') {
    coinflipStore.modules.open.total--;
    coinflipStore.openList[index] = null;
    await delay(600);
    coinflipStore.createOpenGame();
  } else {
    coinflipStore.myListMap.delete(coinflipStore.myList[index].id);
    coinflipStore.myList.splice(index, 1);
  }
  $emit('final-game', item, index);
};
// 取消游戏
const cancelGame = (index: number) => {
  coinflipStore.myListMap.delete(coinflipStore.myList[index].id);
  coinflipStore.myList.splice(index, 1);
};
// 加入机器人
const joinBot = (index: number, res: RoomInfoType) => {
  coinflipStore.myList[index] = res;
};
// 加入游戏
const joinGame = (index: number, res: RoomInfoType) => {
  coinflipStore.openList[index] = res;
};
</script>
<style lang="scss" scoped>
.flip-box {
  @apply grid gap-4 grid-cols-[repeat(auto-fill,minmax(350px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(448px,1fr))];
}
</style>
