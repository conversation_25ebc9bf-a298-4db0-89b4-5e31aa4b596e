<template>
  <div
    class="flex items-center max-lg:w-full max-lg:order-last max-lg:mt-4"
    :class="{ 'max-lg:hidden': !filterShow }"
  >
    <FilterForm
      class="flex items-center gap-2 flex-wrap"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <FilterComponetsFormItem
        v-for="item in formSchema"
        :key="item.key"
        :field="item"
        :field-name="item.key"
        :model-value="formState[item.key]"
        @update:model-value="(value) => handleUpdate(item.key, value)"
      />
    </FilterForm>
  </div>
</template>
<script lang="ts" setup>
import { useCoinflipStore } from '~/stores/modules/coinflip';
const coinflipStore = useCoinflipStore();
const { formSchema, formState } = coinflipStore.formFilter;
defineProps({
  filterShow: {
    type: <PERSON>olean,
    defalut: false,
  },
});
const $emit = defineEmits(['updateFilter']);
const handleUpdate = (key: string, value: any) => {
  if (value !== formState[key]) {
    const preVal = formState[key];
    formState[key] = value;
    $emit('updateFilter', formState, key, preVal, value);
  }
};
</script>
<style lang="scss" scoped>
.coinflip-filter {
  :deep(.n-base-selection-input) {
    padding-left: 2px;
    padding-right: 16px;
  }
  :deep(.n-base-selection .n-base-suffix) {
    right: 0;
  }
}
</style>
