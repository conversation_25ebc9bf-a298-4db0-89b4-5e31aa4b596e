<template>
  <div
    v-show="!(!loading && !list.length)"
    class="overflow-hidden"
    :class="{ 'shrink-0': valuable }"
  >
    <div class="broadcast-tab" :class="{ 'text-theme-color': valuable }">
      <BaseIcon :name="icon" class="!text-[14px]"></BaseIcon>
      <span class="uppercase font-bold">{{ title }}</span>
    </div>

    <div
      v-if="loading"
      class="relative flex overflow-hidden gap-x-[4px] sm:gap-x-[8px]"
      :class="contentClass"
    >
      <SkeletonBroadcast v-for="item in size" :key="item" />
    </div>
    <template v-else>
      <div class="relative w-full">
        <AnimateAddNewCase
          class="relative flex overflow-hidden max-sm:overflow-auto gap-x-[4px] sm:gap-x-[8px] w-full border rounded-[4px]"
          :class="contentClass"
        >
          <div
            v-for="items in list"
            :key="items[0].record_id"
            class="flex shrink-0 gap-x-[4px] sm:gap-x-[8px]"
          >
            <CasesSkinCard
              v-for="(item, index) in items"
              :key="item.record_id"
              :info="item"
              mask="Broadcast"
              size="sm"
              class="h-[130px] sm:h-[140px] w-[98px] sm:w-[120px] shrink-0"
              :class="{ '-translate-y-full drop-ani': item.update }"
              :style="`animationDelay: ${(400 / items.length) * (items.length - index - 1)}ms`"
              @mouseover="hoverBroadcast(true)"
              @mouseleave="hoverBroadcast(false)"
            />
          </div>
        </AnimateAddNewCase>
        <div v-if="!valuable" class="broadcast-mask-end"></div>
        <div v-if="valuable" class="hot-animation">
          <div
            v-for="(star, index) in stars"
            :key="index"
            class="star-bg"
            :class="{ 'star-ani': isAniFinish < 7 }"
            :style="`--translateY: -${star.translateY}px; left: ${star.left}px; animation-delay: ${star.delay}ms`"
            @animationend="animationFinish"
          ></div>
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
const props = defineProps<{
  size: number;
  title: string;
  icon: string;
  valuable?: boolean;
  contentClass?: string;
}>();
const { homeApi } = useApi();
const loading = ref<boolean>(false);
const list = ref<CaseSkinItemType[][]>([]);
const stars = [
  {
    left: 20,
    delay: 600,
    translateY: 80,
  },
  {
    left: 100,
    delay: 180,
    translateY: 110,
  },
  {
    left: 210,
    delay: 1080,
    translateY: 80,
  },
  {
    left: 260,
    delay: 420,
    translateY: 110,
  },
  {
    left: 350,
    delay: 0,
    translateY: 80,
  },
  {
    left: 400,
    delay: 780,
    translateY: 110,
  },
  {
    left: 475,
    delay: 270,
    translateY: 80,
  },
];
// 战报更新数据
const {
  update,
  setItem,
  hover: broadcastHover,
} = useBroadcastUpdate({
  list,
  valuable: props.valuable,
});
// 战报hover状态
const hoverBroadcast = (hover: boolean) => {
  if (hover !== broadcastHover.value) {
    broadcastHover.value = hover;
    if (!hover) {
      setItem();
    }
  }
};
// 战报列表
const getBroadcast = async () => {
  loading.value = true;
  const type = props.valuable ? 1 : 2;
  const { data: req } = await homeApi.getBroadcast(type, props.size);
  const data = req.value?.data;
  if (data) {
    const cases = new Map();
    const goods = new Map();
    const users = new Map();
    (data.cases || []).forEach((el) => cases.set(el.slug, el));
    (data.goods || []).forEach((el) => goods.set(el.goods_id, el));
    (data.user || []).forEach((el) => users.set(el.uid, el));
    list.value = (data.list || []).map((el) => {
      return [
        {
          ...el,
          ...goods.get(el.goods_id),
          ...users.get(el.uid),
          case: cases.get(el.slug),
        },
      ];
    });
  }
  loading.value = false;
};
getBroadcast();
// socket战报监听
useSocketListener('cases', {
  onRecord: update,
});

const isAniFinish = ref(0);
const animationFinish = async () => {
  isAniFinish.value++;
  if (isAniFinish.value === 7) {
    await delay(100);
    isAniFinish.value = 0;
  }
};
</script>
<style lang="scss" scoped>
.broadcast-tab {
  @apply flex items-center gap-x-[8px] text-[12px] rounded mb-[12px] text-[#7D90CA];
}
.broadcast-mask-end {
  @apply absolute right-0 top-0 bottom-0 w-[32px] z-10;
  background: linear-gradient(270deg, #0a0d14 0%, rgba(10, 13, 20, 0) 100%);
}
.hot-animation {
  @apply absolute top-0 bottom-0 left-0 right-0 z-10 pointer-events-none;
  background-image: url('/imgs/broadcast_hot_bg.png');
  background-repeat: repeat-x;
  background-size: auto 140px;
  background-position: 0 bottom;
  animation: bg-scroll 3s linear infinite;
}
@keyframes bg-scroll {
  from {
    background-position: 0 bottom;
  }
  to {
    // 图片宽度840
    background-position: 996px bottom;
  }
}
.star-bg {
  @apply absolute w-[11px] h-[16px] bottom-0;
  background-image: url('/imgs/broadcast_hot_light.png');
  background-size: 100%;
  transform: translate3d(0, 20px, 0);
}
.star-ani {
  animation: starMoveUpAndFade 800ms linear forwards;
}
@keyframes starMoveUpAndFade {
  0% {
    opacity: 1;
    transform: translate3d(0, 20px, 0);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate3d(0, var(--translateY), 0);
  }
}
.drop-ani {
  animation: drop 300ms linear forwards;
}
@keyframes drop {
  0% {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
