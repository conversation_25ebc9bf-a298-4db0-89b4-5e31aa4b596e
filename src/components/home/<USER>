<template>
  <div>
    <div class="flex items-center justify-between">
      <h2
        class="text-[20px] sm:text-[24px] font-bold text-white my-10 uppercase"
      >
        {{ title }}
      </h2>
      <div
        v-if="path"
        class="flex items-center gap-x-1 cursor-pointer text-[#7D90CA] text-[14px] font-bold"
        @click="goPage(path)"
      >
        <span>{{ $t('more') }}</span>
        <BaseIcon name="arrow" class="-rotate-90 !text-[10px]"></BaseIcon>
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
const $router = useRouter();
defineProps({
  title: {
    type: String,
    default: '',
  },
  path: {
    type: String,
    default: '',
  },
});
const goPage = (path: string) => {
  $router.push(path);
};
</script>
<style lang="scss" scoped></style>
