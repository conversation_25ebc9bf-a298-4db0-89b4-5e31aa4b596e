<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="relative w-full h-[200px] sm:h-[400px]">
    <svgo-close-circle
      class="text-[27px] position absolute right-0 -top-[40px] cursor-pointer"
      @click="closeDialog"
    ></svgo-close-circle>
    <n-carousel
      ref="carousleRef"
      class="w-[286px] sm:w-[575px]"
      :show-dots="false"
      :interval="10000"
      autoplay
      @update:current-index="(index: number) => (currentIndex = index)"
    >
      <template v-for="(item, index) in carousel" :key="index">
        <img
          v-if="item.popup_style === 1"
          class="w-full h-full object-contain rounded-xl cursor-pointer"
          :src="content(item).image_show_url"
          @click="handleCarousel(item.redirect_url)"
        />
        <div
          v-else
          class="w-full h-full rounded-xl sm:p-4"
          @click="handleCarousel(item.redirect_url)"
        >
          <h3 class="font-bold text-[18px] text-center mb-[12px]">
            {{ content(item).title }}
          </h3>
          <n-scrollbar class="h-[145px] sm:h-[315px] text-white">
            <div
              class="whitespace-pre-line p-3"
              v-html="content(item).content"
            ></div>
          </n-scrollbar>
        </div>
      </template>
    </n-carousel>
    <div
      v-if="carousel.length > 1"
      class="absolute -bottom-[42px] w-full flex gap-x-[26px] justify-center items-center"
    >
      <div class="carousel-arrow" @click="prevPage">
        <BaseIcon name="back" class="!text-[10px]"></BaseIcon>
      </div>
      <div class="flex gap-x-2">
        <span
          v-for="(_item, index) in carousel"
          :key="index"
          class="w-[22px] h-[8px] bg-white/40 rounded-sm hover:bg-theme-color/70 transition-all cursor-pointer"
          :class="{ '!bg-theme-color': index === currentIndex }"
          @click="toPage(index)"
        ></span>
      </div>
      <div class="carousel-arrow" @click="nextPage">
        <BaseIcon name="back" class="!text-[10px] rotate-180"></BaseIcon>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { CarouselInst } from 'naive-ui';
const dialogRef = inject('dialogRef') as UseDialogOptions;
withDefaults(
  defineProps<{
    carousel: ApiV1NotifyPopupGet200ResponseDataListInner[];
  }>(),
  {
    carousel: () => [],
  },
);
const carousleRef = ref<CarouselInst | null>(null);
const content = computed(() => {
  return (item: ApiV1NotifyPopupGet200ResponseDataListInner) => {
    return JSON.parse(item.content);
  };
});
const currentIndex = ref(0);
const closeDialog = () => {
  dialogRef.onClose?.();
  dialogRef.destroy();
};
const prevPage = () => {
  carousleRef.value?.prev();
};
const nextPage = () => {
  carousleRef.value?.next();
};
const toPage = (index: number) => {
  carousleRef.value?.to(index);
};
const handleCarousel = (link: string) => {
  if (!link) return;
  openNewPage(link);
};
</script>
<style lang="scss" scoped>
.carousel-arrow {
  @apply w-[28px] h-[28px] rounded bg-white/20 hover:bg-theme-color hover:text-[#030303] flex justify-center items-center transition-all;
}
</style>
