<template>
  <div class="flex flex-shrink-0 items-center gap-md">
    <img class="rounded-full" :style="avatarStyle" :src="avatar" />
    <div class="text-white">{{ nickname }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  avatar: string;
  nickname: string;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 30,
});

const avatarStyle = computed(() => ({
  height: `${props.size}px`,
  width: `${props.size}px`,
}));
</script>
