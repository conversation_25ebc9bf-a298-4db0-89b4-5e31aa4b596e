<template>
  <button
    :style="buttonStyle"
    :disabled="disabled || loading"
    class="flex items-center leading-none justify-center gap-2 transition-all disabled:cursor-not-allowed relative"
    @mouseover="isHovered = true"
    @mouseleave="mouseLeave"
    @mousedown="isActive = true"
    @mouseup="isActive = false"
  >
    <svgo-loading
      v-if="loading"
      :style="{
        width: fontSize,
        height: fontSize,
        fill: fontColor,
      }"
      filled
    ></svgo-loading>
    <slot name="icon" />
    <span class="button-content">
      <span class="truncate">
        <slot />
      </span>
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { BtnTypeConfig } from '~/constants/btn';

type ButtonType = 'primary' | 'secondary' | 'tertiary' | 'linear' | 'info';
type ButtonState = 'normal' | 'hover' | 'active' | 'disabled' | 'selected';

interface ButtonProps {
  type?: ButtonType;
  minWidth?: string;
  height?: string;
  fontSize?: string;
  fontColor?: string;
  padding?: string;
  borderRadius?: string;
  customStyle?: Record<string, any>;
  disabled?: boolean;
  loading?: boolean;
  selected?: boolean;
  text?: boolean;
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  minWidth: '128px',
  height: '42px',
  fontSize: '14px',
  fontColor: '#0A0D14',
  padding: '12px 16px',
  borderRadius: '4px',
  customStyle: () => ({}),
  disabled: false,
  loading: false,
  selected: false,
});

const isHovered = ref(false);
const isActive = ref(false);

// 类型配置表
const typeConfig = computed(() => {
  const config = BtnTypeConfig;
  return config[props.type];
});

// 当前状态计算
const currentState = computed<ButtonState>(() => {
  if (props.selected) return 'selected';
  if (props.disabled) return 'disabled';
  if (isActive.value) return 'active';
  if (isHovered.value) return 'hover';
  return 'normal';
});

const buttonStyle = computed(() => {
  const state = currentState.value;
  const isGradient = props.type === 'primary' || props.type === 'secondary';

  // 获取边框渐变色
  const borderConfig = typeConfig.value.border[state];
  // 获取背景样式
  const bgConfig = typeConfig.value.background[state];
  const textColor = typeConfig.value.textColor[state];

  let style = {};

  if (!props.text) {
    if (isGradient) {
      const borderGradient = `linear-gradient(45deg, ${borderConfig[0]} , ${borderConfig[1]})`;
      const bgGradient = `radial-gradient(100% 100% at 50% 0%, ${bgConfig[0]} 0%, ${bgConfig[1]} 100%)`;
      style = {
        backgroundClip: 'padding-box, border-box',
        backgroundOrigin: 'padding-box, border-box',
        backgroundImage: bgGradient + ',' + borderGradient,
      };
    } else {
      const bgColor = typeof bgConfig === 'string' ? bgConfig : bgConfig[0];
      const borderColor =
        typeof borderConfig === 'string' ? borderConfig : borderConfig[0];
      style = {
        background: bgColor,
        borderColor,
      };
    }
  }

  return {
    minWidth: props.minWidth,
    height: props.height,
    fontSize: props.fontSize,
    color: textColor,
    padding: props.padding,
    fontWeight: 500,
    borderRadius: props.borderRadius,
    opacity: 1,
    transform: currentState.value === 'active' ? 'scale(0.98)' : 'none',
    transition: 'all 0.2s ease',
    border: '1px solid transparent',
    ...style,
    ...props.customStyle,
  };
});

const mouseLeave = () => {
  isHovered.value = false;
  isActive.value = false;
};
</script>

<style scoped lang="scss">
.button-content {
  position: relative;
  z-index: 1;
}
</style>
