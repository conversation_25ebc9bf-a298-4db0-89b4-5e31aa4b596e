<template>
  <div>
    <!-- PC端布局 -->
    <div class="hidden xl:flex flex-col px-md gap-md w-[230px]">
      <n-affix
        :top="123"
        :style="{
          width: '212px',
        }"
      >
        <div
          v-for="tab in tabs"
          :key="tab.value"
          :class="[
            'px-4 py-[10px]  text-[#7D90CA] cursor-pointer rounded font-semibold',
            getTabClass(tab),
          ]"
          @click="handleSelectTab(tab)"
        >
          {{ tab.title }}
        </div>
      </n-affix>
    </div>

    <!-- 移动端下拉菜单 -->
    <div
      class="xl:hidden relative overflow-clip rounded border bg-dark-3 transition-all duration-200 border-dark-2 w-full mb-6"
    >
      <div>
        <button
          type="button"
          class="relative flex w-full items-center justify-between gap-md p-lg max-sm:p-3"
          @click="isOpen = !isOpen"
        >
          <p class="size-medium font-bold text-white">
            {{ getCurrentTabTitle }}
          </p>
          <div
            class="flex flex-shrink-0 items-center justify-center size-[14px] text-light-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="15"
              fill="none"
              class="h-full w-full"
            >
              <path
                fill="currentColor"
                d="m7.411 14.662 3.495-5.18c.173-.308.155-.66-.467-.66H3.558c-.568 0-.658.378-.467.66l3.494 5.18a.613.613 0 0 0 .826 0M7.411.982l3.495 5.18c.173.308.155.66-.467.66H3.558c-.568 0-.658-.378-.467-.66L6.585.983a.613.613 0 0 1 .826 0"
              />
            </svg>
          </div>
        </button>

        <div
          v-if="isOpen"
          class="relative z-10 -mt-md bg-dark-3 p-lg max-sm:p-2 pt-0"
        >
          <button
            v-for="tab in tabs"
            :key="tab.value"
            type="button"
            class="btn-tertiary flex rounded font-bold text-white w-full"
            :class="getTabClass(tab)"
            @click="handleSelectTab(tab)"
          >
            <div class="flex items-center">
              {{ tab.title }}
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Tab } from '@/composables/useTabRouteConfig';

const router = useRouter();

const tabConfig = useTabRouteConfig([
  {
    title: 'About',
    value: 'about',
    route: 'about',
  },
  {
    title: 'Agreement',
    value: 'agreement',
    route: 'agreement',
  },
  {
    title: 'Privacy',
    value: 'privacy',
    route: 'privacy',
  },
]);

const { tabs, selectedTab } = tabConfig;
const isOpen = ref(false);

const getTabClass = (tab: Tab) => ({
  'text-[#fff] bg-[#28314A]': selectedTab.value === tab.value,
  'text-[#7D90CA]': selectedTab.value !== tab.value,
});

const { y } = useWindowScroll();
// 获取当前选中的标签标题
const getCurrentTabTitle = computed(() => {
  const currentTab = tabs.find((tab) => tab.value === selectedTab.value);
  return currentTab?.title || 'about';
});

const handleSelectTab = (tab: Tab) => {
  selectedTab.value = tab.value;
  isOpen.value = false;
  router.push(tab.route ? `/document/${tab.route}` : '/document');
  y.value = 0;
};

// 监听路由变化
watch(
  () => router.currentRoute.value,
  (_newRoute: any) => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>

<style scoped lang="scss">
.btn-tertiary {
  @apply p-2 hover:bg-dark-2 transition-colors duration-200;
}

.front {
  @apply flex w-full p-2;
}
</style>
