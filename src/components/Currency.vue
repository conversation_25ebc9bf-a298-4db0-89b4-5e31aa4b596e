<template>
  <component
    :is="tag"
    :class="[
      'inline-flex items-center leading-none gap-[6px] font-medium',
      `justify-${align}`,
    ]"
  >
    <template v-if="showIcon">
      <template v-if="isLoading">
        <div class="w-4 h-4 bg-gray-300 animate-pulse rounded-full"></div>
      </template>

      <template v-else>
        <div :class="`m-0  text-${iconColor}  ${iconMarginClass}`">
          <svgo-gold
            filled
            :style="{
              width: typeof size === 'string' ? size : size + 'px',
              height: typeof size === 'string' ? size : size + 'px',
            }"
            :fontControlled="false"
          />
        </div>
      </template>
    </template>

    <div v-if="showAmount" :class="textColor">
      <template v-if="valueLoading">
        <div class="h-4 w-16 bg-gray-300 animate-pulse rounded"></div>
      </template>

      <template v-else>
        <span>{{ symbol }}</span>
        <template v-if="isAnimated">
          <AnimationNumber
            :value="displayAmount"
            :duration="animationDuration"
          />
        </template>
        <span v-else>
          {{ displayAmount }}
        </span>
      </template>
    </div>
  </component>
</template>
<script setup lang="ts">
const props = defineProps({
  amount: { type: [Number, String], default: 0 },
  iconColor: { type: String, default: 'theme-color' },
  iconMargin: { type: String, default: 'sm' },
  forceCurrency: { type: String, default: null },
  size: { type: [Number, String], default: '20px' },
  isAnimated: { type: Boolean, default: false },
  animationDuration: { type: Number, default: 500 },
  showAmount: { type: Boolean, default: true },
  showIcon: { type: Boolean, default: true },
  symbol: { type: String, default: null },
  currencyDigits: { type: Number, default: 0 },
  valueLoading: { type: Boolean, default: false },
  textColor: { type: String, default: '' },
  roundingMode: { type: Number, default: 0 },
  tag: { type: String, default: 'span' },
  align: {
    type: String,
    default: 'left',
  },
});
const isAnimated = ref(props.isAnimated);
const isLoading = ref(false);

// 当前使用的货币
const currentCurrency = computed(() => {
  if (props.forceCurrency) return props.forceCurrency;
  return 'Coins';
});
const iconMarginClass = computed(() =>
  props.showAmount ? `mr-${props.iconMargin}` : '',
);

// 计算显示金额
const displayAmount = computed(() => {
  // return formatNumberWithCommas(Number(props.amount), props.currencyDigits);
  return props.amount;
});

// 监听货币变化重置动画
watch(currentCurrency, () => {
  isAnimated.value = false;
  nextTick(() => {
    isAnimated.value = props.isAnimated;
  });
});
</script>
