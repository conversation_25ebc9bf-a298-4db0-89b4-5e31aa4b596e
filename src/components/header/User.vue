<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <Tooltip
    :style="{
      maxHeight: '530px',
      border: '1px solid #323a51',
      padding: '0',
    }"
    placement="bottom"
    :show-arrow="false"
    @update:show="showPopover"
  >
    <template #trigger>
      <n-avatar
        :src="userInfo.user_avatar"
        :size="42"
        class="cursor-pointer"
        @click="handleItem(userPop[0])"
      />
    </template>
    <div class="text-[14px] min-w-[220px]">
      <div class="pt-7 pb-6 flex flex-col items-center gap-y-4">
        <n-avatar :src="userInfo.user_avatar" :size="72" />
        <n-ellipsis
          class="text-white font-bold text-[22px] leading-none w-[220px] text-center px-2"
        >
          {{ userInfo.user_name }}
        </n-ellipsis>
        <p class="text-[#7D90CA] -mt-1.5 cursor-pointer" @click="copyUid">
          UID: {{ userInfo.uid }}
        </p>
      </div>
      <div class="px-[16px] mb-6">
        <div class="w-full flex justify-between text-theme-color">
          <span>{{ levelInfo.percent }}%</span>
          <span>Lvl {{ levelInfo.next_level_id }}</span>
        </div>
        <Tooltip trigger="hover">
          <template #trigger>
            <div class="w-full h-3 bg-[#06080E] rounded-full mt-2">
              <div
                :style="{ width: `${levelInfo.percent}%` }"
                class="h-3 bg-theme-color rounded-full"
              ></div>
            </div>
          </template>
          <div class="bg-black py-1 px-2 rounded-sm">
            <span class="text-theme-color">{{ levelProgression.exp }}</span>
            <span>/{{ levelProgression.step }}</span>
          </div>
        </Tooltip>
      </div>
      <div class="flex flex-col p-[16px] border-t-1 border-[#323A51]">
        <HeaderButton
          v-for="item in userPop"
          :key="item.name"
          :item="item"
          popover-btn
          class="w-full"
          @click="handleItem(item)"
        />
      </div>
    </div>
  </Tooltip>
</template>
<script lang="ts" setup>
import { UsersPop } from '~/constants/layout';
import { useSettingStore } from '~/stores/modules/setting';
import type { HeaderItemType } from '~/types/base';
const { t } = useI18n();
const userPop = computed<HeaderItemType[]>(() => UsersPop(t));
const settingStore = useSettingStore();
const { copy } = useCopy();
const toast = useAppToast();
const userInfo = computed(
  () => settingStore.userInfo as ApiV1UserInfoGet200ResponseData,
);
const levelInfo = computed(() => userInfo.value.level_info);
const levelProgression = computed(() => settingStore.levelProgression);

const $emit = defineEmits(['handleItem']);
const handleItem = (item?: HeaderItemType) => {
  $emit('handleItem', item);
};

const showPopover = (show: boolean) => {
  if (show) {
    settingStore.getUserInfo();
  }
};
const copyUid = () => {
  copy(userInfo.value?.uid);
  toast.success({ content: t('replicating_success') });
};
</script>
<style lang="scss" scoped></style>
