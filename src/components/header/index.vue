<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div
    class="fixed top-0 left-0 w-full z-[6666] text-[12px] bg-[#0A0D14] header-bg"
  >
    <div class="h-[96px] max-xl:invisible max-xl:mt-[-96px]">
      <div
        v-for="(row, index) in items"
        :key="index"
        class="border-b border-[#262B3D]"
        :class="{
          'h-[32px]': index === 0,
          'h-[64px]': index === 1,
        }"
      >
        <div class="flex justify-between gap-x-8 max-w-[1376px] mx-auto h-full">
          <div
            v-for="(part, partIndex) in row"
            :key="partIndex"
            class="flex items-center h-full"
            :class="{
              'gap-x-8': index === 0,
            }"
          >
            <a
              v-if="index === 1 && partIndex === 0"
              href="/"
              class="mr-8 shrink-0"
            >
              <img src="/imgs/home_logo.png" alt="logo" class="w-[178.5px]" />
            </a>
            <template v-for="(item, itemIndex) in part" :key="item.name">
              <template v-if="itemShow(item, partIndex, index)">
                <HeaderUser
                  v-if="item.name === 'User'"
                  @handle-item="
                    (info: HeaderItemType) => handleItem(info || item)
                  "
                />
                <HeaderLanguage v-else-if="item.name === 'Language'" />
                <ClientOnly v-else-if="item.badge">
                  <n-badge
                    :value="badge[item.badge as keyof typeof badge] || 0"
                    color="#FF5555"
                    :max="99"
                    :class="{ 'mr-[5px]': itemIndex === 0 }"
                  >
                    <Tooltip :trigger="item?.tooltip ? 'hover' : 'manual'">
                      <template #trigger>
                        <HeaderButton
                          :item="item"
                          :index="[index, partIndex, itemIndex]"
                          :active="highlightTab(item.path)"
                          @click="handleItem(item)"
                        />
                      </template>
                      {{ item?.tooltip }}
                    </Tooltip>
                  </n-badge>
                </ClientOnly>
                <ClientOnly v-else-if="item.restrictIP">
                  <HeaderButton
                    v-if="appStore.ipPermission"
                    :class="{ 'font-bold': index === 1 && partIndex === 0 }"
                    :item="item"
                    :index="[index, partIndex, itemIndex]"
                    :active="highlightTab(item.path)"
                    @click="handleItem(item)"
                  />
                </ClientOnly>
                <HeaderButton
                  v-else
                  :class="{ 'font-bold': index === 1 && partIndex === 0 }"
                  :item="item"
                  :index="[index, partIndex, itemIndex]"
                  :active="highlightTab(item.path)"
                  @click="handleItem(item)"
                />
              </template>
            </template>
          </div>
        </div>
      </div>
      <!-- </div> -->
    </div>

    <!-- 移动端header -->
    <HeaderMobile
      :items="[games, users, cases, abouts]"
      @handle-item="handleItem"
    />
  </div>
</template>
<script lang="ts" setup>
// import FreeCase from '~/components/dialog/FreeCase.vue';
import { Abouts, Cases, Games, Users } from '~/constants/layout';
import { useAppStore } from '~/stores/modules/app';
import { useExchangeStore } from '~/stores/modules/exchange';
import { useSettingStore } from '~/stores/modules/setting';
import { useSocketStore } from '~/stores/modules/socket';
import type { HeaderItemType } from '~/types/base';
type HeaderRowType = HeaderItemType[][];
const $router = useRouter();
const $route = useRoute();

const settingStore = useSettingStore();
const socketStore = useSocketStore();
const appStore = useAppStore();
const exchangeStore = useExchangeStore();
const { t } = useI18n();
const isLogin = computed(() => !!settingStore.userInfo);
const itemShow = computed(() => {
  return (item: HeaderItemType, partIndex: number, index: number) => {
    const loginShow =
      item.needLogin === undefined ||
      !(item.needLogin !== isLogin.value && index === 1 && partIndex === 1);
    // 部分item限制ip访问
    return loginShow;
  };
});
// 高亮tab
const highlightTab = computed(() => {
  const curPath = $router.currentRoute.value.path;
  return (path: string) => {
    if (curPath !== '/' && path === '/') {
      return false;
    }
    return curPath.includes(path);
  };
});
// 左上
const cases = computed(() => Cases(t));
// 右上
const abouts = computed(() => Abouts(t));
// 左下
const games = computed(() => Games(t));
// 右下
const users = computed(() => Users(t));
// 循环的item
const items = computed<HeaderRowType[]>(() => [
  [cases.value, abouts.value],
  [games.value, users.value],
]);
// 数量提醒
const badge = computed(() => {
  return {
    notification: appStore.msgTotal,
    backpack: appStore.headerBackpackTotal,
  };
});
// 链接按钮点击
const handleItem = (item: HeaderItemType) => {
  const { path } = item;
  if (!path) return;
  if (item.needLogin && !isLogin.value) {
    settingStore.signin();
    return;
  }
  if (path === 'login') {
    // 登录
    settingStore.signin();
  } else if (path === 'logout') {
    // 退出登录
    settingStore.signout();
  } else if (path === 'dialog') {
    // 弹窗 -- 比如免费宝箱
    if (item.component) {
      exchangeStore.cdkRedeem(item.component);
    }
  } else {
    // 跳转页面
    if ($route.path === path) {
      return appStore.globalRefreshKey++;
    }
    $router.push(path);
  }
};
// 战报socket
useSocketListener('notifications', {
  onBalance: (data) => {
    if (settingStore.userInfo) {
      settingStore.userInfo.balance.balance = data.balances.balance_coins;
    }
  },
});
onMounted(() => {
  try {
    socketStore.connect();
  } catch (error) {}
});
</script>
<style lang="scss" scoped>
.header-bg {
  background-image: url('/imgs/layout_bg.png');
}
</style>
