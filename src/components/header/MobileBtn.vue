<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-button
    class="w-full mt-4 text-md"
    :class="{
      'justify-start': textStart,
      '!mt-2': !isLarge,
      'h-[40px]': isLarge,
      'text-primary-text': !color,
    }"
    :color="text ? 'transparent' : color || '#333741'"
    :text-color="color ? '#111729' : ''"
    :icon-placement="isLarge ? 'left' : 'right'"
  >
    <template v-if="icon" #icon>
      <BaseIcon :name="icon"></BaseIcon>
    </template>
    <slot></slot>
  </n-button>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    isLarge?: boolean;
    icon?: string;
    textStart?: boolean;
    text?: boolean;
    color?: string;
  }>(),
  {
    textStart: true,
    icon: undefined,
    color: undefined,
  },
);
</script>
<style lang="scss" scoped></style>
