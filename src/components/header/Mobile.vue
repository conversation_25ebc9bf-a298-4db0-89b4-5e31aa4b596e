<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="flex xl:hidden h-[50px] px-[12px] justify-between items-center">
    <a href="/"><img src="/imgs/home_logo.png" alt="logo" class="w-24" /></a>
    <HeaderButton
      v-if="isLogin"
      :item="items[1][2]"
      @click="handleItem(items[1][2])"
    />
    <HeaderButton v-else :item="items[1][4]" @click="handleItem(items[1][4])" />
    <div class="w-24 text-right" @click="openUser">
      <BaseIcon
        name="mobile-dialog"
        class="text-[#7D90CA] white"
        :class="{ 'text-theme-color': mobileOpenSlide }"
      ></BaseIcon>
    </div>

    <n-drawer v-model:show="mobileOpenSlide" :width="280">
      <n-drawer-content
        :native-scrollbar="false"
        body-content-class="bg-[#0A0D14] p-0 text-primary-text"
      >
        <div class="h-screen p-3 pt-[60px] overflow-y-scroll">
          <!-- 登录用户信息 -->
          <template v-if="isLogin">
            <!-- 用户信息 -->
            <div
              class="text-white bg-[#151A29] p-4 rounded-[4px] mb-1"
              @click="handleItem(items[1][3])"
            >
              <div class="flex items-center">
                <n-avatar
                  :src="userInfo?.user_avatar"
                  :size="40"
                  class="mr-2 cursor-pointer"
                />
                <div>{{ userInfo?.user_name }}</div>
              </div>
              <p class="text-[#7D90CA] mt-3 cursor-pointer" @click="copyUid">
                UID: {{ userInfo?.uid }}
              </p>
              <div class="mt-4">
                <div class="w-full flex justify-between text-theme-color">
                  <span>{{ levelInfo?.percent }}%</span>
                  <span>Lvl {{ levelInfo?.next_level_id }}</span>
                </div>
                <div class="w-full h-3 bg-[#06080E] rounded-full mt-2">
                  <div
                    :style="{ width: `${levelInfo?.percent}%` }"
                    class="h-3 bg-theme-color rounded-full"
                  ></div>
                </div>
              </div>
            </div>
            <!-- 钱包信息 -->
            <div
              class="bg-[#151A29] p-4 rounded-[4px] mb-1 flex items-center justify-between text-white"
              @click="handleItem(items[1][2])"
            >
              <div>
                <div class="text-[#7D90CA]">{{ $t('my_coin_quantity') }}</div>
                <div class="flex items-center mt-2 gap-x-1.5">
                  <BaseIcon name="gold"></BaseIcon>
                  <span class="font-bold">
                    {{ userInfo?.balance.balance }}
                  </span>
                </div>
              </div>
              <BaseIcon
                name="arrow"
                class="ml-5 -rotate-90 !text-[12px]"
              ></BaseIcon>
            </div>
          </template>
          <!-- 登录 -->
          <HeaderButton
            v-else
            :item="items[1][4]"
            class="w-full mb-4"
            @click="handleItem(items[1][4])"
          />
          <!-- 其他页面跳转 -->
          <div class="flex flex-col gap-y-1">
            <template v-for="(part, partIndex) in items" :key="partIndex">
              <template v-for="item in part" :key="item.name">
                <template
                  v-if="
                    item.needLogin === undefined ||
                    !(
                      (item.needLogin !== isLogin || !item.badge) &&
                      partIndex === 1
                    )
                  "
                >
                  <HeaderLanguage
                    v-if="item.name === 'Language'"
                    isMobile
                    @change-lang="mobileOpenSlide = false"
                  />
                  <div v-else class="flex items-center justify-between">
                    <HeaderButton
                      :item="{
                        ...item,
                        primary: false,
                      }"
                      :active="highlightTab(item.path)"
                      class="justify-start px-4 w-full rounded-[4px]"
                      :class="{ 'bg-[#151A29]': partIndex === 0 }"
                      @click="handleItem(item)"
                    />
                    <n-badge
                      v-if="item.badge"
                      color="#FF5555"
                      :value="badge[item.badge as keyof typeof badge] || 0"
                      :max="99"
                    />
                  </div>
                </template>
              </template>
            </template>
          </div>
          <div v-if="isLogin" class="border-t border-white/10">
            <HeaderButton
              :item="logoutItem"
              class="justify-start px-4"
              @click="handleItem(logoutItem)"
            />
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '~/stores/modules/app';
import { useSettingStore } from '~/stores/modules/setting';
import type { HeaderItemType } from '~/types/base';
withDefaults(
  defineProps<{
    items: HeaderItemType[][];
  }>(),
  {},
);
const settingStore = useSettingStore();
const appStore = useAppStore();
const isLogin = computed(() => !!settingStore.userInfo);
const userInfo = computed(() => settingStore.userInfo);
const levelInfo = computed(() => settingStore.userInfo?.level_info);
const $router = useRouter();
const { t } = useI18n();
const { copy } = useCopy();
const toast = useAppToast();
const logoutItem = computed(() => ({
  name: 'Log out',
  label: t('log_out'),
  path: 'logout',
  textColor: '#7D90CA',
}));
// 数量提醒
const badge = computed(() => {
  return {
    notification: appStore.msgTotal,
    backpack: appStore.headerBackpackTotal,
  };
});
const highlightTab = computed(() => {
  const curPath = $router.currentRoute.value.path;
  return (path: string) => {
    if (curPath !== '/' && path === '/') {
      return false;
    }
    return curPath.includes(path);
  };
});
const mobileOpenSlide = ref(false);
const openUser = () => {
  mobileOpenSlide.value = !mobileOpenSlide.value;
};
const $emit = defineEmits(['handleItem']);
const handleItem = (item: HeaderItemType) => {
  mobileOpenSlide.value = false;
  $emit('handleItem', item);
};
const copyUid = () => {
  copy(userInfo.value?.uid);
  toast.success({ content: t('replicating_success') });
};
</script>
<style lang="scss" scoped></style>
