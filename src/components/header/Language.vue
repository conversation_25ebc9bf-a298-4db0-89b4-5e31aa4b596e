<template>
  <Tooltip
    :trigger="isMobile ? 'click' : 'hover'"
    scrollable
    :style="{
      maxHeight: '530px',
      border: '1px solid #323a51',
      boxSizing: 'content-box',
    }"
    :show-arrow="false"
  >
    <template #trigger>
      <div
        class="tags-box h-full flex items-center text-[#7D90CA] cursor-pointer font-bold"
        :class="{
          'px-[14px] !h-[42px]': isMobile,
        }"
      >
        <img
          :src="`/imgs/language/${currentLangItem.upload_filename}.png`"
          alt=""
          class="w-[18px] mr-1.5"
        />
        {{ currentLangItem.simplified_name }}
        <BaseIcon name="arrow" class="!text-[8px] ml-[8px]"></BaseIcon>
      </div>
    </template>
    <div class="bg-[#151A29] p-[6px] text-[#7D90CA]">
      <div
        v-for="langItem in langs"
        :key="langItem.upload_filename"
        class="w-[200px] h-[42px] rounded-[4px] hover:bg-[#25314B] hover:text-[white] flex items-center px-[16px] mb-1 cursor-pointer"
        :class="{
          'bg-[#25314B] text-[white]':
            currentLanguage === langItem.upload_filename,
        }"
        @click="changeLanguage(langItem)"
      >
        <img
          :src="`/imgs/language/${langItem.upload_filename}.png`"
          alt=""
          class="w-[18px] mr-1.5"
        />
        {{ langItem.full_name }}
      </div>
    </div>
  </Tooltip>
</template>
<script lang="ts" setup>
import { useAppStore } from '~/stores/modules/app';
import { STORAGE_KEYS } from '~/constants';
withDefaults(
  defineProps<{
    isMobile?: boolean;
  }>(),
  {
    isMobile: false,
  },
);
const appStore = useAppStore();
const $emit = defineEmits(['change-lang']);
// 支持语言
const langs = computed(() => appStore.languages);

// 更改语言
const { setLocale, defaultLocale, locale } = useI18n();
// 设置当前语言
appStore.setCurrentLang(locale.value);

// 人工客服语言
const { setLocale: chatSetLocale } = useChatWoot();
const localeMap: Record<string, string> = {
  'zh-Hans': 'zh_CN',
  'zh-Hant': 'zh_TW',
};
const setChatLocale = (locale: string) => {
  chatSetLocale(localeMap[locale] ?? locale);
};
if (import.meta.client) {
  window.addEventListener('chatwoot:ready', () => {
    setChatLocale(locale.value);
  });
}

const langCookie = useCookie(STORAGE_KEYS.COOKIES.I18N_REDIRECTED);
let cookieLocale;
if (langCookie.value) {
  cookieLocale = useCookieLocale();
}
const lang =
  cookieLocale?.value || langCookie.value || locale.value || defaultLocale;
const currentLanguage = ref(lang);
const currentLangItem = computed(() => {
  const item = langs.value.filter(
    (el: any) => el.upload_filename === currentLanguage.value,
  );
  return item[0] || {};
});
// 切换语言
const changeLanguage = async (option: V1LanguageTypeInfo) => {
  const langName = option.upload_filename || defaultLocale;
  if (currentLanguage.value === langName) return;
  setChatLocale(langName);
  currentLanguage.value = langName;
  await setLocale(langName as any);
  appStore.globalRefreshKey++;
  $emit('change-lang');
};
</script>
<style lang="scss" scoped>
.language {
  :deep(.n-base-selection__border) {
    border: none;
  }
  :deep(.n-base-selection-input) {
    padding-left: 0;
    padding-right: 14px;
  }
  :deep(.n-base-selection .n-base-suffix) {
    right: 0;
  }
}
</style>
