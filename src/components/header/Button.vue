<template>
  <div class="flex" :class="className">
    <MainButton v-if="item.path === 'login'">
      <div class="flex items-center gap-x-2.5 uppercase font-bold">
        <BaseIcon v-if="item.icon" :name="item.icon" class="!text-[20px]" />
        {{ item.label }}
      </div>
    </MainButton>
    <n-button
      v-else
      :text="!item.primary"
      :color="item.color"
      :text-color="item.textColor || '#ffffff'"
      size="large"
      class="h-[42px] xl:min-w-[42px] font-bold max-xl:!text-base"
      :class="{
        'text-[12px]': index[0] === 0,
        'text-[14px]': index[0] === 1,
        '!text-theme-color': active && item.name !== 'Coins',
        'px-[10px]': item.primary,
        'border-1 border-solid border-theme-color':
          item.primary && active && item.name !== 'Coins',
        'hover:text-theme-color': !item.primary && !item.textColor,
        'hover:text-white': !item.primary && item.textColor,
        'w-full justify-start px-[16px] py-[12px] hover:bg-[#25314B] rounded-[4px]':
          popoverBtn,
        'rounded-r-none': item.name === 'Coins',
        uppercase: !popoverBtn,
      }"
    >
      <div class="flex items-center gap-x-2.5">
        <BaseIcon
          v-if="item.icon"
          :name="item.icon"
          :class="item.iconClass || 'xl:!text-[20px]'"
        />
        {{
          item.name === 'Coins'
            ? balance
            : isUser && item.needLogin
              ? ''
              : item.label
        }}
        <BaseIcon
          v-if="item.suffixIcon"
          :name="item.suffixIcon"
          class="xl:!text-[20px]"
        />
      </div>
    </n-button>
    <MainButton
      v-if="item.name === 'Coins'"
      borderRadius="0 4px 4px 0"
      minWidth="42px"
      class="w-[42px]"
    >
      <BaseIcon name="plus" class="!text-[16px] text-[#0A0D14]" />
    </MainButton>
  </div>
</template>
<script lang="ts" setup>
import { useSettingStore } from '~/stores/modules/setting';
import type { HeaderItemType } from '~/types/base';
const props = withDefaults(
  defineProps<{
    item: HeaderItemType;
    active?: boolean;
    index?: number[];
    popoverBtn?: boolean;
  }>(),
  {
    active: false,
    index: () => [],
    popoverBtn: false,
  },
);
const settingStore = useSettingStore();
const balance = computed(() => settingStore.userInfo?.balance.balance);
const isGame = computed(() => {
  if (props.index.length < 1) return false;
  const rowIndex = props.index[0];
  const partIndex = props.index[1];
  return rowIndex === 1 && partIndex === 0;
});
const isUser = computed(() => {
  if (props.index.length < 1) return false;
  const rowIndex = props.index[0];
  const partIndex = props.index[1];
  return rowIndex === 1 && partIndex === 1;
});
const className = computed(() => {
  if (isGame.value) {
    if (props.active) {
      return 'bg-game-color px-5';
    }
    return 'px-5';
  } else if (isUser.value) {
    if (props.item.name === 'Coins') {
      return 'mx-[16px]';
    }
  }
  return '';
});
</script>
<style lang="scss" scoped>
.bg-game-color {
  @apply h-full border-b-3 border-theme-color flex items-center;
  background-image: linear-gradient(
      180deg,
      rgba(255, 188, 54, 0.1) 2%,
      rgba(248, 184, 56, 0.02) 100%
    ),
    radial-gradient(
      53% 38% at 50% 95%,
      rgba(248, 184, 56, 0.24) 2%,
      rgba(248, 184, 56, 0) 100%
    );
}
</style>
