<template>
  <div
    v-for="(_item, index) in length"
    :key="index"
    class="relative h-full rounded-lg bg-dark-3 overflow-hidden"
    :class="className"
    :style="{ aspectRatio: '222/269' }"
  >
    <div class="flex h-full flex-col rounded-lg relative z-1 px-md">
      <div class="absolute top-4 left-4 max-sm:top-2 max-sm:left-2">
        <n-skeleton height="20px" width="20px" />
      </div>
      <div class="h-[42%] mt-[16%]">
        <base-icon
          name="gun-skeleton"
          class="px-lg size-full relative text-white"
          :font-controlled="false"
        />
      </div>

      <div class="relative w-full h-[20px] mt-[5%]">
        <n-skeleton text :repeat="1" />
      </div>
      <div class="mt-[4%] h-[20px]">
        <n-skeleton text :repeat="1" />
      </div>
      <div class="flex justify-center h-[20px] max-sm:mt-[4%] mt-[8%]">
        <n-skeleton text width="60px" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{ loading: boolean; length?: number; className?: string }>(),
  {
    loading: true,
    length: 1,
    // className:
    //   'grid grid-cols-[repeat(auto-fill,minmax(174px,1fr))] gap-x-[8px] gap-y-[8px] sm:grid-cols-[repeat(auto-fill,minmax(190px,1fr))]',
  },
);
</script>

<style lang="scss" scoped></style>
