<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="py-24 w-full flex flex-col items-center">
    <NuxtImg src="/imgs/empty.png" width="180" />
    <p class="mt-[30px] mb-3 text-lg/5 font-bold text-white">
      {{ $t(content) }}
    </p>
    <span v-if="subContent" class="mb-6 text-[#C9C2BF] text-base/4">
      {{ $t(subContent) }}
    </span>
  </div>
</template>

<script setup lang="ts">
defineProps({
  showResetButton: {
    type: Boolean,
    default: true,
  },
  subContent: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: 'no_record_found',
  },
});
// const emit = defineEmits(['resetOptions']);
// const resetOptions = () => {
//   emit('resetOptions');
// };
</script>

<style scoped lang="scss"></style>
