<template>
  <div class="mb-lg text-lg leading-7">
    {{ $t('exchange_item_confirm') }}
  </div>
  <div
    :class="[
      'gap-lg flex justify-center p-[14px] mb-[24px] bg-dark-2 rounded-md',
      isMobile ? 'flex-col items-center' : 'flex-row',
    ]"
  >
    <div class="relative w-[166px] h-[124px] bg-[#121621]/90 overflow-hidden">
      <div
        class="absolute top-[30%] left-[50%] translate-x-[-50%] z-0 h-[100%] w-[80%]"
        :style="gradientStyle"
      ></div>
      <div
        class="absolute top-[46%] z-0 h-full left-[50%] translate-x-[-50%] w-[110%]"
        :style="gradientStyle"
      ></div>
      <img
        class="w-full h-full item-shadow relative z-10 object-contain"
        :src="iconUrl"
        alt=""
      />
      <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <GradientBorder :startColor="opacityColor()" />
      </div>
    </div>

    <div
      :class="[
        'flex-1',
        isMobile
          ? 'w-full flex-col justify-center items-center text-center'
          : 'flex-row',
      ]"
    >
      <p class="mb-[10px] text-[20px] text-white ellipsis">
        {{ marketName }}
      </p>
      <GoodsTag
        :tags="tags"
        :style-name="styleName"
        :class="['mb-[28px]', isMobile ? 'justify-center' : '']"
      />
      <Currency class="text-[16px] font-bold" :amount="Number(price)" />
    </div>
  </div>
  <div class="flex justify-end items-center gap-lg">
    <n-button class="btn-gradient-gray font-bold uppercase" @click="cancel">
      {{ $t('cancel') }}
    </n-button>
    <AmountButton
      :amount="Number(price)"
      :text="$t('buy')"
      :loading="loading"
      @click="exchange"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  iconUrl: string;
  marketName: string;
  price?: number | string | null;
  tags?: MarketGoodsInfotagList;
  goodsId?: number;
  loading?: boolean;
  styleName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  tags: () => ({}),
  price: 0,
  goodsId: 0,
  loading: false,
  styleName: '',
});

const dialogRef = inject('dialogRef') as UseDialogOptions;

const isMobile = useIsMobile();

const cancel = () => {
  dialogRef?.destroy();
};
const exchange = () => {
  dialogRef?.onConfirm({
    iconUrl: props.iconUrl,
    marketName: props.marketName,
    price: props.price,
    tags: props.tags,
    goods_id: props.goodsId,
  });
};
const opacityColor = (opacity: number = 1) => {
  return hexToRgba(props.tags?.rarity?.color ?? '', opacity);
};

const gradientStyle = computed(() => ({
  background: `radial-gradient(41% 41% at 50% 50%, ${opacityColor(0.4)} 0%, ${opacityColor(0.07)} 100%)`,
  filter: 'blur(10px)',
}));
</script>
