<template>
  <n-spin
    :show="loading && !isPageLoading"
    class="flex-layout min-h-[400px]"
    content-class="flex-layout"
  >
    <div
      class="grid grid-cols-[repeat(auto-fill,minmax(104px,1fr))] gap-[9px] sm:grid-cols-[repeat(auto-fill,minmax(190px,1fr))]"
    >
      <template v-if="!isPageLoading">
        <SkinCard
          v-for="item in items"
          v-bind="attrs"
          :key="item.goods_info?.goods_id"
          :item="item.goods_info"
          :isAvailable="
            (item.is_limited && item?.inv_num > 0) || !item.is_limited
          "
          :disabled="item.is_limited && item?.inv_num === 0"
          :actionShow="true"
          :colorCode="item.goods_info?.tags?.rarity?.color || ''"
          :iconUrl="item.goods_info?.icon_url"
          :categoryName="item.goods_info?.category_name || ''"
          :title="$td(item.goods_info?.steam_item_name)"
          :titleTooltip="$td(item.goods_info?.market_hash_name)"
          :price="item.price"
          :actionText="$t('exchange')"
          :disabledText="$t('no_more')"
          isAvailableBg="#05060A"
          :action-loading="actionLoading"
          tagWrapperClass="max-sm:pl-[0.8em] max-sm:pt-[0.7em]"
          topRightWrapperClass="max-sm:pr-[0.8em] max-sm:pt-[0.4em]"
          @action-click="emit('exchange', item)"
        >
          <template v-if="item.is_limited && item?.inv_num" #topRight>
            <Tooltip trigger="hover">
              <template #trigger>
                <div class="flex items-center gap-sm">
                  <BaseIcon name="sell" />
                  <span class="text-lg max-sm:text-sm">
                    {{ item?.inv_num || 0 }}
                  </span>
                </div>
              </template>
              <span>{{ $t('remaining_quantity') }}</span>
            </Tooltip>
          </template>
        </SkinCard>
      </template>
      <template v-else>
        <ExchangeCardSkeleton
          :length="24"
          :loading="loading"
        ></ExchangeCardSkeleton>
      </template>
    </div>
    <slot v-if="!items.length && !loading" name="empty">
      <ClientOnly>
        <Empty :msg="$t('no_record_found')" column />
      </ClientOnly>
    </slot>
  </n-spin>
</template>
<script setup lang="ts">
interface Props {
  items?: any; // 饰品
  isPageLoading?: boolean; // 页面加载状态
  loading?: boolean; // 加载状态
  subContent?: string; // 子内容
  lazyImg?: boolean; // 开启图片懒加载
  showResetBtn?: boolean; // 显示重置按钮
  actionLoading?: boolean; // 操作加载状态
}

withDefaults(defineProps<Props>(), {
  items: () => [],
  loading: true,
  actionLoading: false,
  subContent: '',
  lazyImg: false,
  showResetBtn: false,
  isPageLoading: false,
});

const attrs = useAttrs();
const emit = defineEmits(['resetOptions', 'exchange']);
</script>
