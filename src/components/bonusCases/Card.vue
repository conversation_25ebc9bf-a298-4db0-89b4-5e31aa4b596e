<template>
  <div
    class="flex flex-col items-center px-4 pb-4 pt-2 bg-[#20263B] rounded-md text-white group cursor-pointer"
    @click="openCase"
  >
    <img
      :src="item.image_url"
      class="w-[120px] h-[120px] object-contain mx-1 group-hover:scale-125 duration-300"
      alt=""
    />
    <div class="mt-3 text-lg">{{ item.case_name }}</div>
    <div class="mt-3 flex items-center justify-center text-[12px]">
      <img :src="item.level_key_icon" alt="" class="w-[24px]" />
      <!-- <svgo-key-icon class="w-[24px] h-[24px]" filled></svgo-key-icon> -->
      <span>x{{ item.key_qty }}</span>
    </div>
    <div
      class="mt-[12px] bg-[#272F4A] w-full h-[42px] flex items-center justify-center rounded font-bold cursor-pointer"
    >
      <span>{{ $t('open') }}</span>
    </div>
    <div
      class="w-full opacity-0 duration-300 -mt-[42px] group-hover:opacity-100"
    >
      <MainButton min-width="100%" @click="openCase">
        {{ $t('open') }}
      </MainButton>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    item: V1CasesItem;
  }>(),
  {
    item: () => ({}),
  },
);
const emit = defineEmits(['open-case']);
const openCase = () => {
  emit('open-case', props.item);
};
</script>
