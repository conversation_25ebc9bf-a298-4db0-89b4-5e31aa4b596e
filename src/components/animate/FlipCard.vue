<template>
  <div
    class="rounded-xl relative cursor-pointer"
    :class="{ 'flip-card-hover': $slots.back }"
  >
    <!-- 正面 -->
    <div class="flip-card-face flip-card-face-front">
      <slot name="front"></slot>
    </div>
    <!-- 反面 -->
    <div v-if="$slots.back" class="flip-card-face flip-card-face-back">
      <slot name="back"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped>
.flip-card-face {
  @apply w-full h-full transition-transform duration-500 absolute left-0 bottom-0;
  backface-visibility: hidden;
}
.flip-card-face-front {
  transform: rotateY(0deg);
  z-index: 1;
}
.flip-card-face-back {
  transform: rotateY(180deg);
}
.flip-card-hover:hover {
  .flip-card-face-front {
    transform: rotateY(180deg);
  }
  .flip-card-face-back {
    transform: rotateY(360deg);
  }
}
</style>
