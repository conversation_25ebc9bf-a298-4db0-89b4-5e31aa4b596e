<template>
  <transition-group tag="div" name="new-case">
    <slot></slot>
  </transition-group>
</template>
<script lang="ts" setup></script>
<style lang="scss">
/* 移动动画 */
.new-case-move {
  transition: transform 400ms linear;
  transition-delay: 30ms;
  will-change: transform;
}

/* 进入动画 */
.new-case-enter-active,
.new-case-leave-active {
  transition: transform 400ms linear;
  will-change: transform;
}

.new-case-enter-from {
  transform: translateX(-100%);
}
</style>
