<template>
  <div
    ref="cardRef"
    class="relative hover-container"
    :class="{
      'not-available': !isAvailable || disabled,
      disabled: !actionShow,
      'mobile-interactive': isMobile && actionShow,
    }"
    :style="{
      '--not-available-opacity': isAvailableOpacity,
      '--not-available-bg': isAvailableBg,
      '--card-height': `${cardHeight}px`,
      '--price-margin-ratio': isMobile ? 0.04 : 0.06,
      '--card-text-font-size': cardTextFontSize,
    }"
    @mouseover="!isMobile && (cardHover = true)"
    @mouseleave="!isMobile && (cardHover = false)"
    @click="handleCardClick"
  >
    <!-- 款式标签 -->
    <div
      :class="[
        'hoverable-element absolute left-0 top-0 pl-4 pt-lg z-30',
        tagWrapperClass,
      ]"
      :style="{ opacity: getElementOpacity() }"
      @mouseover="hoverable = true"
      @mouseleave="hoverable = false"
    >
      <slot name="tag">
        <AccessoryTag
          :info="item"
          :class="tagClass"
          :size="isMobile ? 12 : 17"
          :gap="isMobile ? 2 : 4"
          :fontSize="isMobile ? 12 : 16"
        />
      </slot>
    </div>
    <!-- 右上角 -->
    <div
      :class="[
        'hoverable-element absolute right-0 top-0 pr-4 pt-[12px] z-30',
        topRightWrapperClass,
      ]"
      :style="{ opacity: getElementOpacity() }"
      @mouseover="hoverable = true"
      @mouseleave="hoverable = false"
    >
      <slot name="topRight"></slot>
    </div>

    <!-- 遮罩层 -->
    <div
      v-if="actionShow"
      class="z-20 mask-layer absolute top-0 left-0 w-full h-full text-center"
      :style="{
        opacity: getMaskOpacity(),
        pointerEvents: getMaskOpacity() > 0 ? 'auto' : 'none',
      }"
      @click="handleMaskClick"
    >
      <slot name="mask">
        <div
          class="mask absolute left-0 top-0 size-full"
          @click="handleMaskBackgroundClick"
        ></div>
        <div
          class="action-element size-full flex flex-col items-center justify-center gap-6 px-2"
        >
          <slot name="action-button">
            <n-button
              v-if="isAvailable && !disabled"
              :class="[btnType, 'z-40 uppercase', actionBtnClass]"
              :loading="actionLoading"
              @click="handleActionClick"
            >
              <span :class="[actionTextClass]">{{ actionText }}</span>
            </n-button>
            <template v-else>
              <slot name="disabled-content">
                <AngleBadge :text="disabledText" class="z-40 uppercase" />
              </slot>
            </template>
          </slot>
        </div>
      </slot>
    </div>

    <!--  饰品内容 -->
    <div
      class="relative flex w-full h-full flex-col items-center justify-center rounded-lg overflow-hidden bg-dark-3"
      :style="{ aspectRatio: aspectRatio }"
    >
      <!-- 饰品背景 -->
      <div
        class="absolute top-[108px] left-[-25%] z-0 h-[235px] w-[150%]"
        :style="gradientStyle"
      ></div>
      <div
        class="absolute top-[60px] z-0 h-[202px] w-full"
        :style="gradientStyle"
      ></div>

      <!-- 饰品图片 -->
      <div
        class="relative flex-shrink-0 mb-auto z-10"
        :style="{
          height: 'var(--image-height)',
          marginTop: 'var(--image-margin-top)',
        }"
      >
        <div
          class="absolute left-0 top-0 z-0 h-full w-full opacity-20"
          :style="{
            background: `radial-gradient(50% 50% at 50% 50%, #${colorCode} 0%, rgba(74, 34, 34, 0) 100%) `,
          }"
        ></div>
        <MediaDisplay
          :src="iconUrl"
          class="relative z-10 h-full w-full object-contain"
        />
        <div
          class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        >
          <slot name="border">
            <GradientBorder
              :startColor="opacityColor()"
              :width="gradientBorderSize"
              :height="gradientBorderSize"
              :borderRadius="isMobile ? 15 : 20"
            />
          </slot>
        </div>
      </div>

      <!-- 文本内容 -->
      <div
        class="relative flex flex-col items-center w-full card-text-container flex-grow"
        :style="{
          marginTop: 'var(--text-margin-top)',
        }"
      >
        <slot name="category">
          <Text
            :size="isMobile ? 'xs' : 'large'"
            line-height="tight"
            ellipsis
            class="text-center text-white/40 px-1 card-text leading-4"
          >
            {{ categoryName }}
          </Text>
        </slot>
        <div
          class="w-full hoverable-element relative px-[16px] z-30"
          :style="{
            opacity: getElementOpacity(),
            marginTop: 'var(--title-margin-top)',
          }"
          @mouseover="hoverable = true"
          @mouseleave="hoverable = false"
        >
          <slot name="title">
            <Text
              align="center"
              :size="isMobile ? 'xs' : 'large'"
              :tooltip-text="titleTooltip"
              :disabled="true"
              ellipsis
              bold
              class="card-text leading-4"
            >
              {{ title }}
            </Text>
          </slot>
        </div>
        <slot name="price">
          <Currency
            class="relative max-sm:gap-[3px] font-normal card-text leading-4"
            :style="{
              marginTop: 'var(--price-margin-top)',
            }"
            :amount="price"
            :size="currencyIconSize"
          />
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type Component, type VNode } from 'vue';
import AngleBadge from './AngleBadge.vue';

interface Props {
  item: any;
  isAvailable?: boolean;
  disabled?: boolean;
  actionShow?: boolean;
  aspectRatio?: string;
  colorCode?: string;
  iconUrl?: string;
  categoryName?: string;
  title?: string;
  titleTooltip?: string;
  price?: number | string;
  actionText?: string;
  disabledText?: string | Function | Component | VNode | any;
  btnType?: 'primary' | 'purple' | 'gray';
  isAvailableOpacity?: number;
  isAvailableBg?: string;
  renderContent?: string | Function | Component | VNode | any;
  actionBtnClass?: string;
  actionTextClass?: string;
  actionLoading?: boolean;
  tagWrapperClass?: string;
  topRightWrapperClass?: string;
  tagClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  isAvailable: true,
  disabled: false,
  actionShow: false,
  aspectRatio: '222/269',
  colorCode: '',
  iconUrl: '',
  categoryName: '',
  title: '',
  titleTooltip: '',
  price: 0,
  actionText: 'convert',
  disabledText: 'converted',
  btnType: 'primary',
  isAvailableOpacity: 0.6,
  isAvailableBg: '#05060A',
  renderContent: '',
  actionBtnClass: 'min-w-[57%] max-w-[98%] max-sm:text-xs max-sm:p-1',
  actionTextClass: 'truncate',
  actionLoading: false,
  tagWrapperClass: '',
  topRightWrapperClass: '',
  tagClass: '',
});

const isMobile = useIsMobile();
const cardHover = ref(false);
const hoverable = ref(false);
const cardTextFontSize = ref('12px');
const currencyIconSize = ref('12px');
const gradientBorderSize = ref(80);
const cardRef = ref<HTMLElement | null>(null);

// 移动端蒙层控制
const mobileShowMask = ref(false);

const cardHeight = ref(0);

const shouldBeFullyOpaque = computed(
  () => !props.isAvailable || props.disabled || !props.actionShow,
);

const isHoveringInteractive = computed(() => hoverable.value);

const getElementOpacity = () => {
  if (shouldBeFullyOpaque.value) return 1;
  if (isHoveringInteractive.value) return 1;

  // 移动端显示蒙层时隐藏标签和右上角元素
  if (isMobile.value && mobileShowMask.value) return 0;

  if (cardHover.value) return 0.1;
  return 1;
};

const getMaskOpacity = () => {
  if (!props.isAvailable || props.disabled) {
    return 1;
  }
  if (isHoveringInteractive.value) {
    return 0;
  }
  // 移动端
  if (isMobile.value) {
    return mobileShowMask.value ? 1 : 0;
  } else {
    return cardHover.value ? 1 : 0;
  }
};

const opacityColor = (opacity: number = 1) => {
  return hexToRgba(props.colorCode || '', opacity);
};

const btnType = computed(() => {
  if (!props.isAvailable && props.disabled) {
    return 'btn-gradient-gray';
  }
  return `btn-gradient-${props.btnType}`;
});

const gradientStyle = computed(() => ({
  background: `radial-gradient(41% 41% at 50% 50%, ${opacityColor(0.4)} 0%, ${opacityColor(0.07)} 100%)`,
  filter: 'blur(10px)',
}));

const emit = defineEmits(['action-click']);

const handleCardClick = (event: Event) => {
  if (!isMobile.value || !props.actionShow) return;

  if (!props.isAvailable || props.disabled) return;

  event.stopPropagation();

  if (!mobileShowMask.value) {
    mobileShowMask.value = true;
  }
};

const handleMaskClick = (event: Event) => {
  event.stopPropagation();
};

const handleMaskBackgroundClick = (event: Event) => {
  if (!isMobile.value) return;

  event.stopPropagation();
  // 点击蒙层背景隐藏蒙层
  mobileShowMask.value = false;
};

const handleActionClick = (event: Event) => {
  event.stopPropagation();

  if (isMobile.value) {
    // 移动端
    if (mobileShowMask.value) {
      emit('action-click', props.item);
      mobileShowMask.value = false;
    }
  } else {
    emit('action-click', props.item);
  }
};

interface ResponsiveConfig {
  fontSize: {
    min: number;
    max: number;
    ratio: number;
  };
  iconSize: {
    min: number;
    max: number;
    ratio: number;
  };
  borderSize: {
    min: number;
    max: number;
    ratio: number;
  };
}

const responsiveConfig: ResponsiveConfig = {
  fontSize: {
    min: 11,
    max: 16,
    ratio: 0.059,
  },
  iconSize: {
    min: 14,
    max: 22,
    ratio: 0.081,
  },
  borderSize: {
    min: 52,
    max: 88,
    ratio: 0.32,
  },
};

const calculateResponsiveValue = (
  height: number,
  config: { min: number; max: number; ratio: number },
): number => {
  return Math.max(config.min, Math.min(config.max, height * config.ratio));
};

const updateResponsiveSizes = () => {
  const height = cardRef.value?.offsetHeight ?? 0;
  if (height === 0) return;

  cardHeight.value = height;

  const fontSize = calculateResponsiveValue(height, responsiveConfig.fontSize);
  const iconW = calculateResponsiveValue(height, responsiveConfig.iconSize);
  const bgW = calculateResponsiveValue(height, responsiveConfig.borderSize);

  cardTextFontSize.value = `${fontSize}px`;
  currencyIconSize.value = `${iconW}px`;
  gradientBorderSize.value = bgW;
};

const debouncedUpdateResponsiveSizes = useDebounceFn(
  updateResponsiveSizes,
  30,
  { maxWait: 100 },
);

// 处理点击外部隐藏蒙层
const handleClickOutside = (event: Event) => {
  if (!isMobile.value || !mobileShowMask.value) return;

  const target = event.target as HTMLElement;
  if (cardRef.value && !cardRef.value.contains(target)) {
    mobileShowMask.value = false;
  }
};

onMounted(() => {
  useResizeObserver(cardRef, (entries) => {
    if (entries.length > 0) {
      debouncedUpdateResponsiveSizes();
    }
  });
  updateResponsiveSizes();

  if (isMobile.value) {
    document.addEventListener('click', handleClickOutside);
  }
});

onUnmounted(() => {
  if (isMobile.value) {
    document.removeEventListener('click', handleClickOutside);
  }
});
</script>

<style scoped lang="scss">
.hover-container {
  position: relative;

  --icon-size-base: calc(var(--card-height) * 0.081);
  --border-size-base: calc(var(--card-height) * 0.32);
  --image-height: calc(var(--card-height) * 0.42);
  --image-margin-top: calc(var(--card-height) * 0.15);
  --text-margin-top: calc(var(--card-height) * 0.03);
  --price-margin-top: calc(var(--card-height) * var(--price-margin-ratio));
  --title-margin-top: calc(var(--card-height) * 0.02);

  .hoverable-element {
    z-index: 30;
    transition: opacity 0.3s ease;
  }

  .mask-layer {
    transition: opacity 0.3s ease;
    .mask {
      background: linear-gradient(
        180deg,
        rgba(21, 26, 41, 0.9) 0%,
        #151a29 48%,
        rgba(21, 26, 41, 0.9) 100%
      );
      cursor: pointer; // 移动端点击背景可关闭
    }
  }

  .hoverable-element:hover {
    z-index: 40;
  }

  &.not-available {
    pointer-events: none;
    .hoverable-element {
      opacity: 0.15 !important;
      pointer-events: none;
    }

    .mask-layer {
      opacity: 1 !important;
      .mask {
        z-index: 40;
        opacity: var(--not-available-opacity) !important;
        background: var(--not-available-bg) !important;
      }
    }
  }

  &.disabled {
    .hoverable-element {
      opacity: 1;
    }
    &:hover .mask-layer {
      opacity: 0 !important;
    }
  }

  // 移动端交互
  &.mobile-interactive {
    cursor: pointer;

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }

    .mask-layer {
      &[style*='opacity: 1'] {
        .action-element {
          animation: fadeInUp 0.3s ease;
        }
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
:global(.card-text) {
  font-size: var(--card-text-font-size) !important;
}
</style>
