<template>
  <component :is="content" v-if="isComponentType" v-bind="$attrs" />
  <component :is="vNodeResult" v-else-if="vNodeResult" v-bind="$attrs" />
  <span v-else v-bind="$attrs">
    {{ textResult }}
  </span>
</template>

<script setup lang="ts">
import { isVNode, computed, type Component, type VNode } from 'vue';

type FunctionContent = (...args: any[]) => any;
type ContentType =
  | string
  | Component
  | VNode
  | FunctionContent
  | null
  | undefined;

interface Props {
  content?: ContentType;
  functionArgs?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  content: undefined,
  functionArgs: () => [],
});

/**
 * 检查内容是否为组件或VNode
 */
const isComponent = (value: any): boolean => {
  if (value == null) return false;

  if (isVNode(value)) return true;

  if (
    typeof value === 'object' &&
    (value.render || value.setup || value.template)
  ) {
    return true;
  }

  return false;
};

const isComponentType = computed(() => isComponent(props.content));

const vNodeResult = computed(() => {
  if (typeof props.content !== 'function') return null;
  try {
    const func = props.content as FunctionContent;
    const result = func(...props.functionArgs);
    return isVNode(result) ? result : null;
  } catch (error) {
    console.error('渲染函数执行错误:', error);
    return null;
  }
});

const textResult = computed(() => {
  if (props.content == null) return '';
  if (typeof props.content === 'string') {
    return props.content;
  }
  if (typeof props.content === 'function') {
    try {
      const func = props.content as FunctionContent;
      const result = func(...props.functionArgs);
      if (isVNode(result)) return '';
      return String(result ?? '');
    } catch (error) {
      console.error('渲染函数执行错误:', error);
      return '';
    }
  }
  return String(props.content);
});
</script>
