<template>
  <div
    class="angle-badge min-w-[57%] text-base max-sm:text-xs max-sm:p-1 flex items-center justify-center text-white text-center bg-white/10 rounded-[4px] px-sm py-xs"
    :class="[customClass]"
    :style="{
      transform: `rotate(${rotate}deg)`,
      minWidth: minWidth ? `${minWidth}px` : '',
      minHeight: minHeight ? `${minHeight}px` : '29px',
      fontSize: textSize ? `${textSize}px` : '',
      lineHeight: textSize ? `${textSize * 1.2}px` : '16.8px',
      ...customStyle,
    }"
  >
    <slot>{{ text }}</slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  text: {
    type: String,
    default: '',
  },
  textSize: {
    type: Number,
    default: 0,
  },
  rotate: {
    type: Number,
    default: -20,
  },

  minWidth: {
    type: [Number, String],
    default: 0,
  },

  minHeight: {
    type: [Number, String],
    default: 29,
  },
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style scoped lang="scss">
.angle-badge {
  display: inline-block;
  font-size: 14px;
  font-weight: 700;
  user-select: none;
  z-index: 30;
}
</style>
