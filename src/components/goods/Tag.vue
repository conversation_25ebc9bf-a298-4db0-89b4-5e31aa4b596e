<template>
  <div class="flex gap-2 items-center">
    <div
      v-for="tag in tagList"
      :key="tag.title"
      class="rounded-sm"
      :style="TAG_STYLE"
    >
      <span
        class="text-[16px] px-[6px] py-[2px]"
        :style="{
          color: tag.color ? `#${tag.color}` : undefined,
        }"
      >
        {{ tag.title }}
      </span>
    </div>
    <template v-if="styleName">
      <Gem v-if="!isPTag(styleName)" :title="styleName" class="!size-[24px]" />
      <div v-else :style="TAG_STYLE">
        <span class="text-[16px] px-[6px] py-[2px] text-white">
          {{ styleName }}
        </span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
interface Props {
  tags: V1MarketGoodsInfo['tags'];
  styleName?: V1MarketGoodsInfo['style_name'];
}

const props = defineProps<Props>();

const TAG_STYLE = {
  background: 'rgba(255, 255, 255, 0.08)',
} as const;

const P_TAGS = ['P1', 'P2', 'P3', 'P4'] as const;
type PTag = (typeof P_TAGS)[number];

const isPTag = (styleName: string): styleName is PTag => {
  if (!styleName) return false;
  return P_TAGS.includes(styleName as PTag);
};

const tagList = computed(() => {
  if (!props.tags) return [];
  return Object.values(props.tags).filter(
    (tag) => tag?.title && tag?.is_show !== false,
  );
});
</script>
