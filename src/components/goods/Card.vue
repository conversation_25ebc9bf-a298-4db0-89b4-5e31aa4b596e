<template>
  <div :class="['flex gap-4 ', className]">
    <div class="relative overflow-hidden rounded-md">
      <div
        class="absolute top-[18px] left-[-25%] z-0 h-full w-[150%]"
        :style="gradientStyle"
      ></div>
      <div
        class="absolute top-[18px] z-0 h-[80px] w-[80px]"
        :style="gradientStyle"
      ></div>
      <GoodsCover class="!w-[128px] !h-[95px] relative z-10" :cover="cover" />
      <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <GradientBorder
          :width="60"
          :height="60"
          :borderRadius="16"
          :startColor="opacityColor()"
        />
      </div>
    </div>
    <div class="flex flex-col justify-center gap-4">
      <div class="w-[320px]">
        <GoodsMarketName
          :name="$td(name)"
          :ellipsis="ellipsis"
          :marketNameClassName="marketNameClassName"
        />
      </div>

      <GoodsTag :tags="tags" />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    cover: string;
    name: string;
    className?: string;
    marketNameClassName?: string;
    tags?: MarketGoodsInfotagList;
    ellipsis?: boolean;
  }>(),
  {
    className: '',
    marketNameClassName: '',
    tags: () => ({}) as MarketGoodsInfotagList,
    ellipsis: true,
  },
);

const opacityColor = (opacity: number = 1) => {
  return hexToRgba(props.tags?.rarity?.color, opacity);
};

const gradientStyle = computed(() => ({
  background: `radial-gradient(41% 41% at 50% 50%, ${opacityColor(0.4)} 0%, ${opacityColor(0.07)} 100%)`,
  filter: 'blur(10px)',
}));
</script>
