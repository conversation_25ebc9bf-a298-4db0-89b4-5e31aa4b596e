<template>
  <div>
    <p
      :class="[
        'text-lg text-white leading-[1.1]',
        marketNameClassName,
        { truncate: ellipsis },
      ]"
    >
      {{ name }}
    </p>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    name: string;
    marketNameClassName?: string;
    ellipsis?: boolean;
  }>(),
  {
    ellipsis: true,
    marketNameClassName: '',
  },
);
</script>
