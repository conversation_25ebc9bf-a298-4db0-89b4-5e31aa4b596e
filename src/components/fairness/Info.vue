<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="leading-6 pb-8">
    <h2 class="text-[18px] font-bold text-white mb-4">
      {{ $t('fairness?') }}
    </h2>
    <p class="text-white">
      {{ $t('fairness_p_1') }}
    </p>
    <p class="my-2 text-white">
      {{ $t('fairness_p_2') }}
    </p>
    <p class="text-white">
      {{ $t('fairness_p_3') }}
    </p>
    <div class="bg-[#151A29] px-6 py-[16px] rounded-lg my-4">
      <p class="text-white">
        {{ $t('fairness_block_p_1') }}
      </p>
      <p class="text-white font-bold mt-4 mb-2.5">
        {{ $t('client_seed') }}
      </p>
      <p class="text-[#7D90CA] leading-6">
        {{ $t('fairness_block_clinet_seed_info') }}
      </p>
      <ClientOnly>
        <div class="flex mt-3 gap-x-2">
          <n-input
            v-model:value="settingStore.clientSeed"
            :placeholder="$t('client_seed_length')"
            class="bg-[#7D90CA]/0"
          >
            <template #suffix>
              <n-button text type="primary" class="font-bold" @click="create">
                {{ $t('randomly_new') }}
              </n-button>
            </template>
          </n-input>
          <MainButton @click="save">{{ $t('save') }}</MainButton>
        </div>
      </ClientOnly>
      <p class="text-white font-bold mt-4 mb-2.5">
        {{ $t('server_seed') }}
      </p>
      <p class="text-[#7D90CA] leading-6">
        {{ $t('fairness_lock_server_seed_info') }}
      </p>
      <template v-if="isLogin">
        <div class="flex mt-3 gap-x-2">
          <n-input
            placeholder="Server Seed"
            readonly
            class="bg-[#7D90CA]/0"
            :maxlength="32"
            :value="serverSeed"
          ></n-input>
          <MainButton @click="resetServerSeed">
            {{ $t('regenerate') }}
          </MainButton>
        </div>
        <p
          class="text-[#7D90CA] mt-2.5"
          @click="openCodePage"
          v-html="$t('fairness_block_server_seed_validate')"
        ></p>
      </template>
      <p
        v-else
        class="mt-4"
        @click="login"
        v-html="$t('log_in_to_view_server_seed')"
      ></p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useSettingStore } from '~/stores/modules/setting';
import { ClientSeed } from '~/utils';
const settingStore = useSettingStore();
const { fairnessApi } = useApi();
const toast = useAppToast();
const { t } = useI18n();
const serverSeed = ref('');
const serverSeedLoading = ref(false);
const isLogin = computed(() => settingStore.userInfo?.uid);
// 获取服务端种子
const getServerSeed = async () => {
  if (!isLogin.value) return;
  const { data: req } = await fairnessApi.getServerSeed();
  const data = req.value?.data;
  if (data) {
    serverSeed.value = data.hashed_seed;
  }
};
// 重置服务器种子
const resetServerSeed = async () => {
  if (!checkLogin()) return;
  serverSeedLoading.value = true;
  const { data: req } = await fairnessApi.resetServerSeed();
  serverSeedLoading.value = false;
  const data = req.value?.data;
  if (data) {
    serverSeed.value = data.hashed_seed;
    toast.success({ content: t('obtained_a_new_server_seed') });
  }
};
// 创建新的客户端种子
const create = () => {
  settingStore.clientSeed = ClientSeed.create();
};
// 保存客户端种子
const save = () => {
  if (!checkLogin()) return;
  const isValide = ClientSeed.valide(settingStore.clientSeed);
  if (isValide) {
    ClientSeed.save(settingStore.clientSeed);
    toast.success({ content: t('successfully_saved') });
  } else if (settingStore.clientSeed.length !== 32) {
    toast.error({ content: t('client_seed_length') });
  } else {
    toast.error({
      content: t('client_seed_rule'),
    });
  }
};
const login = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    settingStore.signin();
  }
};
const openCodePage = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    openNewPage('https://go.dev/play/p/BMwV-ynsBUJ');
  }
};
await getServerSeed();
onUnmounted(() => {
  settingStore.clientSeed = ClientSeed.get();
});
</script>
<style lang="scss" scoped>
:deep(.n-input__border) {
  @apply border-[#7D90CA]/30;
}
</style>
