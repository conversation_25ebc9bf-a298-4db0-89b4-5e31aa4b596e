<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <slot></slot>
    <div v-if="isLogin" class="mt-4">
      <BaseDataTable
        class="rounded-lg"
        :columns="column"
        :data="data"
        :loading="loading"
        :paginator="paginator"
        :emptyMsg="$t('no_record_found')"
        :ellipsis="true"
        :bordered="false"
        :scroll-x="1300"
        @update:page="updatePage"
      >
        <template #empty>
          <Empty column class="bg-[#151A29]">
            <div
              class="flex flex-col items-center justify-center h-[188px] text-[#68779F]"
            >
              <BaseIcon
                name="empty-record"
                class="!text-[84px] mb-4"
              ></BaseIcon>
              <div>{{ $t('no_record_found') }}</div>
            </div>
          </Empty>
        </template>
      </BaseDataTable>
    </div>
    <div
      v-else
      class="h-[188px] bg-[#151A29] rounded-[8px] flex justify-center items-center mt-4"
      @click="login"
      v-html="$t('log_in_to_view_records')"
    ></div>
  </div>
</template>
<script lang="ts" setup>
import type { DataTableColumns } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';
import type { PaginatorType } from '~/types/base';

const settingStore = useSettingStore();
const isLogin = computed(() => settingStore.userInfo?.uid);
const $emit = defineEmits(['update:page']);

withDefaults(
  defineProps<{
    column: DataTableColumns<any>;
    paginator: PaginatorType;
    data: any[];
    loading: boolean;
    label: string;
  }>(),
  {},
);
const login = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    settingStore.signin();
  }
};
const updatePage = (page: { page: number }) => {
  $emit('update:page', page.page);
};
</script>
<style lang="scss" scoped></style>
