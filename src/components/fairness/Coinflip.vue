<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <h4 class="text-xl text-white font-bold mt-4 mb-2">
      {{ $t('coinflip_technical_details') }}
    </h4>
    <!-- details -->
    <p class="text-white">
      {{ $t('fairness_coinflip_info') }}
    </p>
    <div
      class="bg-[#151A29] px-6 py-4 rounded-lg my-4 text-[#7D90CA] leading-6"
    >
      <p v-html="$t('fairness_coinflip_private_seed')"></p>
      <p v-html="$t('fairness_coinflip_public_seed')"></p>
      <p v-html="$t('fairness_coinflip_game_ID')"></p>
      <p
        class="mt-6"
        @click="openGolangCodePage"
        v-html="$t('fairness_game_golang_code')"
      ></p>
    </div>

    <h4 class="text-xl text-white font-bold pt-4 pb-2">
      {{ $t('coinflip_record') }}
    </h4>
  </div>
</template>
<script lang="ts" setup>
const openGolangCodePage = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    return openNewPage('https://go.dev/play/p/XP5SwRZ40XN');
  }
};
</script>
<style lang="scss" scoped></style>
