<template>
  <div class="pt-2">
    <h2 class="text-2xl text-center text-white font-bold mb-8">
      {{ $t('case_battle_details') }}
    </h2>
    <div class="flex justify-around flex-wrap gap-[5px] sm:gap-[9px]">
      <FairnessBattlesDetailUser
        v-for="user in info.players"
        :key="user.uid"
        :user="playerInfo(user)"
      />
    </div>
    <n-scrollbar class="max-h-[60vh] mt-[22px]">
      <!-- 普通局 -->
      <FairnessBattlesDetailRound
        v-for="(round, index) in rounds"
        :key="index"
        :rounds="round"
        class="mb-[22px]"
      />
      <!-- 决胜局 -->
      <FairnessBattlesDetailTiebreaker
        v-if="info.battle_info?.is_tiebreaker === 1"
        :tiebreaker="tiebreaker"
        :roundId="rounds.length + 1"
      />
    </n-scrollbar>
  </div>
</template>
<script lang="ts" setup>
type CaseBattleRowType = V1BattleItem & {
  goods: Map<number, V1MarketGoodsInfo>;
  cases: Map<string, V1CasesItem>;
  users: Map<string, V1SimpleUserInfo>;
};
const props = withDefaults(
  defineProps<{
    info: CaseBattleRowType;
  }>(),
  {},
);
// 玩家用户信息
const playerInfo = computed(() => {
  return (user: V1BattlePlayer) => {
    if (!user.uid) return {};
    const mapUserInfo = props.info.users.get(user.uid);
    return {
      ...user,
      ...mapUserInfo,
    };
  };
});
type RecordType = V1BattleRecord &
  V1MarketGoodsInfo &
  V1CasesItem &
  V1SimpleUserInfo;
// 对战list
const rounds = computed(() => {
  const records = props.info.records || [];
  return records.reduce((res: RecordType[][], cur: V1BattleRecord) => {
    if (!cur.case_slug || !cur.goods_id || !cur.uid) return res;
    const record = {
      ...cur,
      ...props.info.cases.get(cur.case_slug),
      ...props.info.goods.get(cur.goods_id),
      ...props.info.users.get(cur.uid),
    };
    const index = (record.round_id || 0) - 1;
    if (res[index]) {
      res[index].push(record);
    } else {
      res[index] = [record];
    }
    return res;
  }, []);
});
type TiebreakerType = V1TiebreakerItem & V1SimpleUserInfo;
// 加赛
const tiebreaker = computed(() => {
  const items = props.info.tiebreaker || [];
  return items.reduce((res: TiebreakerType[], cur: V1TiebreakerItem) => {
    if (cur.uid) {
      res.push({
        ...cur,
        ...props.info.users.get(cur.uid),
      });
    }
    return res;
  }, []);
});
</script>
<style lang="scss" scoped>
.sub-title {
  @apply text-lg text-white font-bold;
}
</style>
