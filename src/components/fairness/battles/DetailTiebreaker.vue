<template>
  <div class="mt-[28px] mb-[22px]">
    <div class="flex justify-between items-center mb-[28px]">
      <h3 class="text-white font-bold">{{ $t('tiebreaker') }} {{ roundId }}</h3>
      <div>{{ $t('ticket_quantity') }}: {{ tiebreaker.length }}</div>
    </div>
    <BaseDataTable
      :columns="columns"
      :data="tiebreaker"
      size="small"
      :ellipsis="true"
      :bordered="false"
      :scroll-x="700"
    ></BaseDataTable>
  </div>
</template>
<script lang="ts" setup>
import FairnessBattlesDetailUser from '~/components/fairness/battles/DetailUser.vue';
type TiebreakerType = V1TiebreakerItem & V1SimpleUserInfo;
withDefaults(
  defineProps<{
    tiebreaker: TiebreakerType[];
    roundId: number;
  }>(),
  {},
);
const { t } = useI18n();
const columns = [
  {
    title: t('profile'),
    key: 'nickname',
    width: '40%',
    render(row: TiebreakerType) {
      return h(FairnessBattlesDetailUser, {
        user: row,
        small: true,
        hideLevel: true,
      });
    },
  },
  {
    title: t('ticket'),
    key: 'ticket',
    width: '30%',
    render(row: TiebreakerType) {
      return h(
        'span',
        {
          style: { color: '#1A9DFF' },
        },
        row.ticket || '--',
      );
    },
  },
  {
    title: t('result'),
    key: 'is_win',
    width: '30%',
    render(row: TiebreakerType) {
      const isWin = row.is_win === 1;
      return h(
        'span',
        {
          style: { color: isWin ? '#3EFF95' : null },
        },
        isWin ? 'Win' : 'Lose',
      );
    },
  },
];
</script>
<style lang="scss" scoped></style>
