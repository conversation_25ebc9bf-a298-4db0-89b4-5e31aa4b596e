<template>
  <div>
    <div class="flex justify-between items-center mb-[25px]">
      <p class="text-white font-bold">
        {{ $t('round') }} {{ rounds[0].round_id }}
      </p>
      <div class="flex gap-x-2 items-center">
        <MediaDisplay
          :src="rounds[0].image_url"
          :class-name="'h-[34px] object-contain'"
          alt=""
        />
        <span>{{ rounds[0].case_name }}</span>
      </div>
    </div>
    <BaseDataTable
      :columns="columns"
      :data="rounds"
      :ellipsis="true"
      :bordered="false"
      :scroll-x="700"
    ></BaseDataTable>
  </div>
</template>
<script lang="ts" setup>
import SvgoGold from '~/assets/icons/gold.svg';
import FairnessBattlesDetailUser from '~/components/fairness/battles/DetailUser.vue';
type RecordType = V1BattleRecord &
  V1MarketGoodsInfo &
  V1CasesItem &
  V1SimpleUserInfo;
withDefaults(
  defineProps<{
    rounds: RecordType[];
  }>(),
  {},
);
const { t } = useI18n();
const { $td } = useNuxtApp();

const columns = [
  {
    title: t('profile'),
    key: 'nickname',
    width: '25%',
    render(row: RecordType) {
      return h(FairnessBattlesDetailUser, {
        user: row,
        small: true,
        hideLevel: true,
      });
    },
  },
  {
    title: t('result'),
    key: 'result',
    width: '15%',
    render(row: RecordType) {
      return h(
        'span',
        {
          style: { color: '#1A9DFF' },
        },
        row.result || '--',
      );
    },
  },
  {
    title: t('item'),
    key: 'market_hash_name',
    width: '45%',
    ellipsis: {
      tooltip: true,
    },
    render(row: RecordType) {
      const key = row.market_hash_name || '';
      return $td(key);
    },
  },
  {
    title: t('value'),
    key: 'pawn_price',
    width: '15%',
    render(row: RecordType) {
      return h(
        'div',
        {
          class: 'flex items-center gap-x-2',
        },
        [
          h(SvgoGold, { class: 'text-theme-color mb-0', filled: true }),
          h('span', null, row.pawn_price || '--'),
        ],
      );
    },
  },
];
</script>
<style lang="scss" scoped></style>
