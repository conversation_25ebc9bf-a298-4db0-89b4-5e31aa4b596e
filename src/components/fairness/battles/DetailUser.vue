<template>
  <div
    class="flex-1 flex items-center rounded-lg gap-x-3"
    :class="!hideLevel ? 'bg-[#20263B] p-2 sm:p-4' : ''"
  >
    <n-avatar :src="user.avatar" :size="30" class="flex-shrink-0" />
    <UserLevel v-if="!hideLevel" :level="user.level" />
    <n-ellipsis class="max-w-[150px]">
      {{ user.nickname }}
    </n-ellipsis>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    user: {
      uid?: string;
      avatar?: string;
      nickname?: string;
      level?: number;
      [key: string]: any;
    };
    small?: boolean;
    hideLevel?: boolean;
  }>(),
  {
    small: false,
    hideLevel: false,
  },
);
</script>
<style lang="scss" scoped></style>
