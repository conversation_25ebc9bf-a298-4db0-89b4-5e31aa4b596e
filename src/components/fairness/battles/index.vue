<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="leading-6">
    <h4 class="text-xl text-white font-bold mt-4 mb-2">
      {{ $t('case_battles_technical_details') }}
    </h4>
    <p class="text-white">
      {{ $t('fairness_battle_info') }}
    </p>
    <div
      class="bg-[#151A29] px-6 py-4 rounded-lg my-4 text-[#7D90CA] leading-6"
    >
      <p class="mb-2" v-html="$t('fairness_battle_server_seed')"></p>
      <p v-html="$t('fairness_battle_public_seed')"></p>
      <p class="mb-2">{{ $t('fairness_battle_EOS') }}</p>
      <p class="mb-2" v-html="$t('fairness_battle_round_number')"></p>
      <p v-html="$t(`fairness_battle_player_position`)"></p>
      <p class="my-4">
        <span class="text-white">
          {{ $t('fairness_battle_tie') }}
        </span>
      </p>
      <p v-html="$t('fairness_battle_tiebreaker')"></p>
      <p>
        {{ $t('fairness_battle_tickets') }}
      </p>
      <p>
        {{ $t('fairness_battle_position') }}
      </p>
      <p
        class="mt-6"
        @click="openGolangCodePage"
        v-html="$t('fairness_game_golang_code')"
      ></p>
    </div>
    <h4 class="text-xl text-white font-bold pt-4 pb-2">
      {{ $t('cases_battles_record') }}
    </h4>
  </div>
</template>
<script lang="ts" setup>
const openGolangCodePage = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    return openNewPage('https://go.dev/play/p/_eqanI54r9P');
  }
};
</script>
<style lang="scss" scoped></style>
