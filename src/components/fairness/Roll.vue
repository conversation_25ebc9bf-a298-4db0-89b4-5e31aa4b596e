<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="leading-6">
    <h4 class="text-xl text-white font-bold mt-4 mb-2">
      {{ $t('roll_technical_details') }}
    </h4>
    <!-- details -->
    <p class="text-white">
      {{ $t('fairness_roll_info') }}
    </p>
    <div
      class="bg-[#151A29] px-6 py-4 rounded-lg my-4 text-[#7D90CA] leading-6"
    >
      <p v-html="$t('fairness_roll_public_seed')"></p>
      <p v-html="$t('fairness_roll_prize_ID')"></p>
      <p v-html="$t('fairness_roll_PRNumber')"></p>
      <p v-html="$t('fairness_roll_location')"></p>
      <p class="mb-4" v-html="$t('fairness_roll_PLNumber')"></p>
      <p>{{ $t('fairness_roll_win') }}</p>
      <p class="mb-4">{{ $t('fairness_roll_rule') }}</p>

      <p
        class="mt-6"
        @click="openGolangCodePage"
        v-html="$t('fairness_game_golang_code')"
      ></p>
    </div>

    <h4 class="text-xl text-white font-bold pt-4 pb-2">
      {{ $t('roll_record') }}
    </h4>
  </div>
</template>
<script lang="ts" setup>
const openGolangCodePage = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    return openNewPage('https://go.dev/play/p/j-bcoEqEERY');
  }
};
</script>
<style lang="scss" scoped></style>
