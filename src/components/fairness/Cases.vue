<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <h4 class="text-xl text-white font-bold mt-4 mb-2">
      {{ $t('cases_technical_details') }}
    </h4>
    <!-- details -->
    <p class="text-white">
      {{ $t('fairness_case_info') }}
    </p>
    <div
      class="bg-[#151A29] px-6 py-4 rounded-lg my-4 text-[#7D90CA] leading-6"
    >
      <p v-html="$t('fairness_case_server_seed')"></p>
      <p class="my-2" v-html="$t('fairness_case_nonce')"></p>
      <p v-html="$t('fairness_case_client_seed')"></p>
      <p class="text-white my-4">
        {{ $t('fairness_case_result_rule') }}
      </p>
      <p
        @click="openGolangCodePage"
        v-html="$t('fairness_case_golang_code')"
      ></p>
      <p class="mt-2">
        {{ $t('fairness_case_regenerate_server_seed') }}
      </p>
    </div>

    <div class="flex justify-between items-center">
      <h4 class="text-xl text-white font-bold mt-4 mb-2">
        {{ $t('cases_record') }}
      </h4>
      <div>
        <span class="text-[#1A9DFF]">
          {{ $t('unhashed_server_seed') }}
        </span>
        <span class="text-[#FF5555] ml-10">
          {{ $t('hashed_server_seed') }}
        </span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const openGolangCodePage = (e: any) => {
  if (e.target.tagName === 'SPAN') {
    return openNewPage('https://go.dev/play/p/uUAp_6y3UXD');
  }
};
</script>
<style lang="scss" scoped></style>
