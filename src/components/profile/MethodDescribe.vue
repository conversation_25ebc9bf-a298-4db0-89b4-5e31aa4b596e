<template>
  <ClientOnly>
    <div class="flex items-center gap-2">
      <BaseIcon
        v-if="iconMap[item?.s_flow_type as keyof typeof iconMap]"
        :name="iconMap[item?.s_flow_type as keyof typeof iconMap]"
        class="rounded-sm size-[24px] p-1"
      />
      <NuxtImg v-else-if="item?.image" :src="item?.image" width="24" />
      <span>
        {{ $td(flowTypeTextMap[item?.s_flow_type], item.s_flow_type_text) }}
      </span>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
interface Props {
  item: ApiV1TradeTransactionsGet200ResponseDataListInner;
}

withDefaults(defineProps<Props>(), {});

const iconMap = {
  1: 'cases',
  2: 'case-battles',
  3: 'case-battles',
  4: 'case-battles',
  5: 'case-battles',
  6: 'coinflip',
  7: 'coinflip',
  8: 'coinflip',
  9: 'coinflip',
  10: 'csgo',
  11: 'cases',
  12: 'cases',
  13: 'referrals',
  14: 'csgo',
  23: 'coin',
  24: 'coin',
  25: 'coin',
  26: 'coin',
};
const flowTypeTextMap: Record<number, string> = {
  1: 'open_cases',
  2: 'create_cases_battle',
  3: 'join_cases_battle',
  4: 'cancel_cases_battle',
  5: 'exit_cases_battle',
  6: 'create_coinflip',
  7: 'join_coinflip',
  8: 'coinflip_win',
  9: 'cancel_coinflip',
  10: 'exchange_skin',
  11: 'open_free_case',
  12: 'open_bonus_cases',
  13: 'referrals',
  14: 'convert_skin',
  15: 'crypto_deposit',
  16: 'gift_card_deposit',
  17: 'bank_deposit',
  21: 'recharge_issue',
  22: 'recharge_issue',
  23: 'other',
  24: 'other',
  25: 'other',
  26: 'other',
};
</script>
