<template>
  <n-input
    v-bind="$attrs"
    size="large"
    class="h-[42px] rounded-md flex item-center"
    :theme-overrides="{
      placeholderColor: '#4B5679',
      border: 'solid 1px #323A51',
      color: 'transparent',
    }"
  >
    <slot></slot>
    <template v-for="(slotFn, name) in $slots" #[name]>
      <slot :name="name"></slot>
    </template>
  </n-input>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss"></style>
