<template>
  <div>
    <div class="w-full flex flex-col items-center">
      <n-avatar :src="userInfo.user_avatar" :size="72" />
      <div
        class="max-w-[90%] truncate text-white mt-2.5 text-2xl font-semibold"
      >
        {{ userInfo.user_name }}
      </div>
      <p
        class="text-[#7D90CA] mt-1 cursor-pointer"
        @click="copyUid(userInfo.uid)"
      >
        UID: {{ userInfo.uid }}
      </p>
      <div class="w-full pt-6 text-[#F8B838]">
        <div class="flex justify-between">
          <div>{{ userInfo?.level_info?.percent }}%</div>
          <div>Lv{{ userInfo?.level_info?.next_level_id }}</div>
        </div>
        <n-progress
          type="line"
          :show-indicator="false"
          color="#F8B838"
          rail-color="#28314A"
          :percentage="userInfo?.level_info?.percent ?? 0"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  userInfo: ApiV1UserInfoGet200ResponseData;
}
withDefaults(defineProps<Props>(), {});
const { t } = useI18n();
const { copy } = useCopy();
const toast = useAppToast();
const copyUid = (uid: string) => {
  copy(uid);
  toast.success({ content: t('replicating_success') });
};
</script>
