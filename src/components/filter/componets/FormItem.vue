<script setup lang="ts">
import { NInput, NInputNumber, NCheckbox } from 'naive-ui';
import InputSearch from '~/components/form/InputSearch.vue';
import Select from '~/components/form/Select.vue';
defineProps({
  fieldName: {
    type: String,
    required: true, // 表单项的字段名，必需
  },
  field: {
    type: Object,
    required: true, // 表单项的配置对象，包括类型、标签、组件类型等，必需
  },
  modelValue: {
    type: [String, Number, Array, Boolean],
    default: null, // 表单项的当前值
  },
});

const emit = defineEmits(['update:modelValue', 'handle:event']);

const lastValue = ref(null);

const updateValue = (value: any) => {
  if (value === lastValue.value || (lastValue.value === null && value === '')) {
    return;
  }
  lastValue.value = value;
  emit('update:modelValue', value);
};

// 映射组件
const componentMap: Record<string, Component> = {
  'n-input': NInput,
  'n-input-number': NInputNumber,
  'n-select': Select,
  'n-checkbox': NCheckbox,
  'n-input-search': InputSearch,
};
/**
 * 从给定的field对象中返回一个只包含特定属性的新对象。
 *
 * @param {any} field - 要从中提取属性的field对象。
 * @return {Record<string, any>} 一个新的对象，包含field对象的指定属性。
 */
const getComponentProps = (field: any) => {
  const propNames = [
    'options',
    'placeholder',
    'getShow',
    'class',
    'clearable',
    'auth',
    'placement',
    'label',
    'renderLabel',
    'renderTag',
    'consistentMenuWidth',
    'themeOverrides',
    'suffix',
  ];

  return propNames.reduce((props: Record<string, any>, propName: string) => {
    if (field[propName] !== undefined) {
      props[propName] = field[propName];
    }
    return props;
  }, {});
};
// const handleEvent = (eventName: string, value: any) => {
//   emit('handle:event', { eventName, value });
// };
/**
 * 根据提供的字段返回包含事件处理函数的对象。
 *
 * @param {any} field - 字段对象。
 * @return {Record<string, Function>} 包含事件处理函数的对象。
 */
const getComponentEvents = (field: any) => {
  const eventNames = ['change', 'custom:click', 'complete:select'];
  return eventNames.reduce(
    (events: Record<string, Function> = {}, eventName) => {
      if (field.listeners?.[eventName]) {
        events[eventName] = (value: any) =>
          emit('handle:event', { eventName, value });
      }
      return events;
    },
    {},
  );
};
</script>

<template>
  <n-form-item
    :show-label="!!field.field_label == true"
    :path="fieldName"
    :label="field.field_label"
    :label-style="field.label_style"
    :label-placement="field.field_label_place"
    :show-feedback="false"
    :style="field.field_style"
    :class="field.field_class"
  >
    <n-checkbox
      v-if="field.component === 'n-checkbox'"
      :checked="modelValue as boolean"
      v-bind="getComponentProps(field)"
      @update:checked="updateValue"
      v-on="getComponentEvents(field)"
    ></n-checkbox>
    <component
      :is="componentMap[field.component]"
      v-else
      :value="modelValue || null"
      v-bind="getComponentProps(field)"
      @update:value="updateValue"
      @update:checked="updateValue"
      v-on="getComponentEvents(field)"
    ></component>
  </n-form-item>
</template>

<style scoped lang="scss">
:global(.n-base-selection .n-base-selection-overlay) {
  position: static;
}
</style>
