<!-- eslint-disable vue/multi-word-component-names -->
<script setup lang="ts">
import type { FormInst } from 'naive-ui';
// const emit = defineEmits(['submit']);
const formRef = ref<FormInst | null>(null);
defineProps({
  formSchema: {
    type: Object,
    required: true,
  },
  formState: {
    type: Object,
    default: () => ({}),
  },
});

defineExpose({
  formRef,
});
</script>

<template>
  <n-form ref="formRef" :model="formState">
    <slot :form-state="formState" :form-schema="formSchema"></slot>
  </n-form>
</template>
