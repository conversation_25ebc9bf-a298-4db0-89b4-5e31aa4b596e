<template>
  <n-select v-bind="$attrs" v-model:show="show" clearable>
    <template #arrow>
      <div class="size-full flex items-center justify-center">
        <IconArrow
          :class="[
            'pt-[3px] pb-0 w-[6px] h-[8px] text-purple-1 font-bold',
            {
              'mt-[5px] rotate-180 text-white': show,
            },
          ]"
        ></IconArrow>
      </div>
    </template>
  </n-select>
</template>

<script setup lang="ts">
import IconArrow from '@/assets/icons/arrow.svg';
const show = ref(false);
</script>
