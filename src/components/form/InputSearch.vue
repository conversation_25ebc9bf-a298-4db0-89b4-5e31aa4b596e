<!-- eslint-disable vue/multi-word-component-names -->
<script setup lang="ts">
import { debounce } from 'lodash-es';
import type { SetupContext } from 'vue';
// const { marketApi } = useApi();
const attrs: SetupContext['attrs'] = useAttrs();
const options = ref([]);

const searchType = ref('select'); // 搜索类型
const searchValue = ref<any>(attrs.value);

watch(
  () => attrs.value,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      searchValue.value = newValue;
    }
  },
);

const debouncedEmit = debounce((newValue: string) => {
  emit('update:value', newValue);
}, 300);

watch(
  () => searchValue.value,
  (newValue, oldValue) => {
    if ((attrs as any)?.getShow && !(attrs as any)?.getShow()) return;
    if (newValue !== oldValue && newValue !== null) {
      debouncedEmit(newValue);
    }
  },
  {
    immediate: false,
  },
);

const emit = defineEmits(['update:value', 'complete:select']);

const handleChangeValue = (value: string) => {
  /** 限制60个字节 */
  searchValue.value = cuteToBytes(value, 60);
  if (value === null) {
    triggerSearch({ type: 'select' });
  }
};

const triggerSearch = (ev: Record<string, any>, selectItem?: any) => {
  if (attrs.auth && !checkLogin(false)) return;
  if (searchType.value === 'enter') return; // 阻止回车事件触发默认select行为
  options.value = [];
  if (
    ev.keyCode !== 13 &&
    ev.type !== 'mousedown' &&
    ev.type !== 'click' &&
    ev.type !== 'select'
  )
    return;
  searchType.value = 'input';
  // 清除搜索
  if (ev.type === 'mousedown') {
    emit('update:value', '');
  } else {
    if (selectItem) {
      return emit('complete:select', selectItem);
    }
    if (ev.keyCode === 13) {
      searchType.value = 'enter';
      nextTick(() => {
        searchType.value = '';
      });
      return emit('update:value', ev?.target?.value ?? '');
    }
    emit('update:value', searchValue.value);
  }
};

const handleSelect = (value: string) => {
  const item = options.value.find((e: any) => e.value === value);
  nextTick(() => {
    triggerSearch({ type: 'select' }, item);
  });
};
</script>

<template>
  <div class="flex w-full">
    <n-auto-complete
      v-bind="attrs"
      :options="options"
      :clearable="true"
      passively-activated
      :value="searchValue"
      @update:value="handleChangeValue"
      @select="handleSelect"
      @keydown.enter="triggerSearch"
    >
      <template v-if="attrs.suffix" #suffix>
        <div
          class="flex items-center justify-center cursor-pointer"
          @click="triggerSearch"
        >
          <svgo-search
            class="w-[18px] h-[24px] mb-0 text-[#7D90CA]"
          ></svgo-search>
        </div>
      </template>
      <template v-else #prefix>
        <div class="flex items-center justify-center">
          <svgo-search
            class="w-[18px] h-[24px] mb-0 text-[#7D90CA]"
          ></svgo-search>
        </div>
      </template>
    </n-auto-complete>
    <!-- <n-button
      color="rgba(255, 255, 255, 0.1)"
      text-color="#FFF"
      ghost
      class="rounded-r-lg rounded-l-none"
      @click.stop="triggerSearch"
    >
      search icon
    </n-button> -->
  </div>
</template>
