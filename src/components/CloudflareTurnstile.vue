<template>
  <div :id="containerId" ref="turnstileContainer" class="cf-turnstile"></div>
</template>

<script setup lang="ts">
const props = defineProps({
  siteKey: {
    type: String,
    required: true,
  },
  theme: {
    type: String,
    default: 'dark',
  },
  size: {
    type: String,
    default: 'normal',
  },
  action: {
    type: String,
    default: 'verify',
  },
  cData: {
    type: String,
    default: '',
  },
  retry: {
    type: String,
    default: 'never',
  },
  autoExecute: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['verified', 'error', 'expired']);

const { $renderTurnstile, $resetTurnstile } = useNuxtApp();
const turnstileContainer = ref(null);
const containerId = `turnstile-container-${Math.random().toString(36).substring(2, 9)}`;
const widgetId = ref(null);
const isWidgetRendered = ref(false);
const isDestroyed = ref(false);

// 执行验证
const executeInvisibleChallenge = () => {
  if (widgetId.value && (window as any).turnstile && !isDestroyed.value) {
    try {
      (window as any).turnstile.execute(widgetId.value);
    } catch (error) {
      console.error('Execute invisible challenge error:', error);
      if (!isDestroyed.value) {
        emit('error');
      }
    }
  }
};

onMounted(async () => {
  if (isDestroyed.value) return;

  try {
    const result: any = await $renderTurnstile(`#${containerId}`, {
      sitekey: props.siteKey,
      theme: props.theme,
      size: props.size,
      action: props.action,
      cData: props.cData,
      retry: props.retry,
      language: 'en',
      callback: (token: string) => {
        if (!isDestroyed.value) {
          emit('verified', token);
        }
      },
      'error-callback': () => {
        if (!isDestroyed.value) {
          emit('error');
        }
      },
      'expired-callback': () => {
        if (!isDestroyed.value) {
          emit('expired');
        }
      },
    });

    if (!isDestroyed.value) {
      widgetId.value = result.widgetId;
      isWidgetRendered.value = true;

      if (props.autoExecute) {
        setTimeout(() => {
          if (!isDestroyed.value && widgetId.value) {
            executeInvisibleChallenge();
          }
        }, 100);
      }
    }
  } catch (error) {
    console.error('Turnstile error:', error);
    if (!isDestroyed.value) {
      emit('error');
    }
  }
});

onUnmounted(() => {
  isDestroyed.value = true;

  if (widgetId.value && isWidgetRendered.value) {
    try {
      $resetTurnstile(widgetId.value);
    } catch (error) {
      console.error('Turnstile reset error:', error);
    }
  }

  widgetId.value = null;
  isWidgetRendered.value = false;
});

defineExpose({
  reset: () => {
    if (widgetId.value && isWidgetRendered.value && !isDestroyed.value) {
      try {
        $resetTurnstile(widgetId.value);
      } catch (error) {
        console.error('Turnstile reset error:', error);
      }
    }
  },
  execute: executeInvisibleChallenge,
});
</script>
