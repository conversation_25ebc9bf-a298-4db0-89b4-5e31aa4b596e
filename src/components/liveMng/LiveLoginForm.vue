<template>
  <div class="login-form">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :show-feedback="false"
      :show-require-mark="false"
      class="space-y-[20px]"
    >
      <!-- 电子邮箱 -->
      <n-form-item path="email">
        <template #label>
          <div class="flex items-center gap-[6px] text-[#333333]">
            <BaseIcon name="login-email" :filled="false" class="size-[14px]" />
            <span class="font-medium">Email</span>
          </div>
        </template>
        <n-input
          v-model:value="formData.email"
          placeholder="Enter your email address"
          :theme-overrides="inputTheme"
          @focus="handleFieldFocus"
        />
      </n-form-item>

      <!-- 密码 -->
      <n-form-item class="!mt-[12px]" path="password">
        <template #label>
          <div class="flex items-center gap-[6px] text-[#333333]">
            <BaseIcon
              name="login-lock"
              :filled="false"
              class="size-[14px] text-[#333333]"
            />
            <span class="font-medium">Password</span>
          </div>
        </template>
        <n-input
          v-model:value="formData.password"
          type="password"
          show-password-on="click"
          placeholder="Enter password"
          :theme-overrides="inputTheme"
          @focus="handleFieldFocus"
        />
      </n-form-item>
      <!-- Cloudflare验证 -->
      <client-only>
        <CloudflareTurnstile
          ref="turnstileRef"
          size="flexible"
          theme="light"
          :auto-execute="true"
          :site-key="config.public.TURNSTILE_SITE_KEY"
          @verified="handleTurnstileVerified"
          @error="handleTurnstileError"
        />
      </client-only>

      <!-- 登录按钮 -->
      <n-button
        type="primary"
        size="large"
        color="#419FFF"
        block
        :loading="loading"
        class="h-[42px] uppercase"
        @click="handleLogin"
      >
        Confirm Login
      </n-button>

      <!-- 分割线 -->
      <div class="flex items-center my-6 font-medium">
        <div class="flex-1 h-px bg-[#DFE4ED]"></div>
        <span class="px-4 text-[#ACB4C4] text-sm uppercase">
          {{ 'or select' }}
        </span>
        <div class="flex-1 h-px bg-[#DFE4ED]"></div>
      </div>

      <!-- Steam登录 -->
      <n-button
        block
        color="#419FFF"
        ghost
        class="mx-auto w-[194px] h-12 font-semibold"
        @click="handleSteamLogin"
      >
        <template #icon>
          <BaseIcon name="login" class="w-[22px] h-[22px]" />
        </template>
        SIGN IN WITH STEAM
      </n-button>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormRules } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';

interface LoginFormData {
  email: string;
  password: string;
}

interface Props {
  showTermsAndPrivacy?: boolean;
}

interface Emits {
  (e: 'success'): void;
  (e: 'switch-to-register'): void;
}

withDefaults(defineProps<Props>(), {
  showTermsAndPrivacy: true,
});

const emit = defineEmits<Emits>();

const formRef = ref<FormInst>();
const turnstileRef = ref();
const loading = ref(false);
const turnstileToken = ref('');

const config = useRuntimeConfig();
const settingStore = useSettingStore();
const { loginApi } = useApi();
const { t } = useI18n();
const toast = useAppToast();

const formData = reactive<LoginFormData>({
  email: '',
  password: '',
});

const inputTheme = {
  color: '#F3F5F9',
  colorFocus: '#F3F5F9',
  heightMedium: '38px',
};

const rules: FormRules = {
  email: [
    {
      required: true,
      message: t(
        'please_enter_login_credentials',
        'Please enter your login email and password.',
      ),
      trigger: ['blur'],
    },
    {
      type: 'email',
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
    {
      validator: (_rule, value) => {
        return isValidEmail(value);
      },
      message: t(
        'email_format_error',
        'The email format is incorrect. Please re-enter.',
      ),
      trigger: ['blur'],
    },
  ],
  password: [
    {
      required: true,
      message: t('password_required', 'Enter password'),
      trigger: ['blur'],
    },
    {
      min: 8,
      max: 16,
      message: t(
        'password_complexity',
        'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
      ),
      trigger: ['blur'],
    },
    {
      validator: (_rule, value) => {
        if (!value) return true;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

        const validTypes = [
          hasUpperCase,
          hasLowerCase,
          hasNumbers,
          hasSpecialChar,
        ].filter(Boolean).length;
        return validTypes >= 2;
      },
      message: t(
        'password_complexity',
        'Password: 8-16 chars, 2+ types (A-Z, a-z, 0-9, or symbols)',
      ),
      trigger: ['blur'],
    },
  ],
};

const handleTurnstileVerified = (token: string) => {
  turnstileToken.value = token;
};

const handleTurnstileError = (error: any) => {
  console.error('Turnstile verification failed:', error);
  turnstileToken.value = '';
};

const handleLogin = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    if (!turnstileToken.value) {
      loading.value = true;
      // 触发隐形验证
      turnstileRef.value?.execute();
      return;
    }

    try {
      loading.value = true;
      const res = await loginApi.emailLogin({
        email: formData.email,
        password: formData.password,
        cf_token: turnstileToken.value,
        platform: 1,
      });

      if (res.data.value.code === 0) {
        await settingStore.setTokenSafely(res.data.value.data.token);
        emit('success');
      } else {
        // 重置验证码
        turnstileRef.value?.reset();
        turnstileToken.value = '';
      }
    } catch (error) {
      turnstileRef.value?.reset();
      turnstileToken.value = '';
    }
  } catch (error: any) {
    if (Array.isArray(error)) {
      const flatErrors = error.flat();
      if (error.length === 1 && flatErrors[0]?.message) {
        toast.error({
          content: t(flatErrors[0].message),
        });
      } else {
        toast.error({
          content: t(
            'email_or_password_incorrect',
            'Please fill in the correct information',
          ),
        });
      }
    }
  } finally {
    loading.value = false;
  }
};

const handleSteamLogin = () => {
  settingStore.signin({
    openDialog: false,
    event: 'login',
    isLive: true,
  });
};

const handleFieldFocus = () => {
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
};
</script>

<style scoped lang="scss">
.login-form {
  iframe {
    width: 100%;
  }
}
</style>
