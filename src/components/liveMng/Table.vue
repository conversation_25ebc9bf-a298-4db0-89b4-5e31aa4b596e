<template>
  <div>
    <slot name="title"></slot>
    <n-data-table v-bind="$attrs">
      <template #empty>
        <div class="h-[250px] flex items-center justify-center">
          <n-empty description="No matching data available" />
        </div>
      </template>
    </n-data-table>
    <!-- paginator -->
    <div
      v-if="paginator"
      v-show="paginatorShow"
      class="wallet-list px-[1.25rem] py-3.5"
    >
      <div class="py-3">
        <n-space justify="end">
          <n-pagination
            :page="paginator.page"
            :page-size="paginator.page_size"
            :page-sizes="[10, 20, 50]"
            :page-count="pageCount"
            show-size-picker
            @update:page="updatePage"
            @update:page-size="updatePageSize"
          ></n-pagination>
        </n-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
type Pagination = {
  total: number;
  page: number;
  page_size: number;
};
type TableProps = {
  paginator?: Pagination;
  emptyMsg?: string;
};
const props = defineProps<TableProps>();

const pageCount = computed(() => {
  if (props.paginator) {
    return Math.ceil(props.paginator.total / props.paginator.page_size);
  }
  return 0;
});

const paginatorShow = computed(() => {
  if (props.paginator) {
    return props.paginator?.total > 10;
  }
  return false;
});

const emit = defineEmits(['update:page', 'update:pageSize']);

const updatePage = (page: number) => {
  emit('update:page', page);
};

const updatePageSize = (pageSize: number) => {
  emit('update:pageSize', pageSize);
};
</script>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-th) {
  border-bottom: none !important;
}
</style>
