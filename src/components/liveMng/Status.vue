<template>
  <div class="flex items-center">
    <div
      class="flex items-center text-[12px] gap-[4px] px-[8px] py-[4px] rounded-[2px] font-medium"
      :style="{ background: bg, color: color }"
    >
      <BaseIcon :name="icon" :filled="true" class="w-[14px]"></BaseIcon>
      <span>{{ label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  icon: {
    type: String,
    default: 'success-ic',
  },
  label: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: '#00B42A',
  },
  bg: {
    type: String,
    default: '#E8FFEA',
  },
});
</script>
