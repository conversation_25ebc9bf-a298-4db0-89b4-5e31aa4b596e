<template>
  <n-form
    ref="formRef"
    :model="formModel"
    :rules="formRules"
    label-placement="left"
    label-align="left"
    label-width="100px"
    require-mark-placement="left"
  >
    <n-form-item
      v-for="field in fields"
      :key="field.key"
      :label="field.label"
      :path="field.key"
      :show-feedback="field.required === true"
      :label-style="{ color: '#1D2129', fontWeight: '500' }"
    >
      <!-- 普通输入框 -->
      <n-input
        v-if="field.type === 'input'"
        :value="formModel[field.key]"
        :placeholder="field.placeholder"
        :theme-overrides="inputThemeOverrides"
        v-bind="field.props"
        @update:value="updateField(field.key, $event)"
      />

      <!-- 文本域 -->
      <n-input
        v-else-if="field.type === 'textarea'"
        :value="formModel[field.key]"
        type="textarea"
        :placeholder="field.placeholder"
        :theme-overrides="inputThemeOverrides"
        v-bind="field.props"
        @update:value="updateField(field.key, $event)"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import type { InputProps, FormInst, FormRules } from 'naive-ui';

interface FormField {
  key: string;
  label: string;
  type: 'input' | 'textarea';
  placeholder?: string;
  required?: boolean;
  rules?: any[];
  props?: Record<string, any>;
}

interface FormConfig {
  fields: FormField[];
  rules?: FormRules;
}

const props = withDefaults(
  defineProps<{
    defineValue?: Record<string, any>;
    config: FormConfig;
  }>(),
  {
    defineValue: () => ({}),
  },
);

const formRef = ref<FormInst | null>(null);
const { fields } = props.config;

const internalModel = ref<Record<string, any>>({});

// 初始化内部状态
const initInternalModel = () => {
  const data: Record<string, any> = {};
  fields.forEach((field) => {
    data[field.key] = '';
  });
  return {
    ...data,
    ...props.defineValue,
  };
};

const formModel = computed(() => {
  if (Object.keys(internalModel.value).length === 0) {
    internalModel.value = initInternalModel();
  }
  return internalModel.value;
});

function updateField(key: string, value: any) {
  const newValue = {
    ...formModel.value,
    [key]: value,
  };
  internalModel.value = newValue;
}

// 动态生成表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {};

  if (props.config.rules) {
    Object.assign(rules, props.config.rules);
  }

  fields.forEach((field) => {
    if (field.required || field.rules) {
      const arr: any[] = [];
      if (field.required) {
        arr.push({
          required: true,
          message: `${field.label} is required`,
          trigger: ['blur', 'input'],
        });
      }

      if (field.rules) {
        arr.push(...field.rules);
      }

      rules[field.key] = arr;
    }
  });

  return rules;
});

const inputThemeOverrides: NonNullable<InputProps['themeOverrides']> = {
  placeholderColor: '#86909C',
  border: 'solid 1.5px rgba(209, 213, 219)',
  borderHover: 'solid 1.5px #3b82f6',
  borderFocus: 'solid 1.5px #3b82f6',
  textColor: '#1D2129',
  color: '#fff',
  colorFocus: '#fff',
  colorFocusError: '#fff',
  caretColor: '#4B5679',
  boxShadowFocus: '0 0 8px 0 rgba(59, 130, 246, 0.3)',
};

function validate() {
  return formRef.value?.validate();
}

function resetForm() {
  formRef.value?.restoreValidation();
  const resetData: Record<string, any> = {};
  fields.forEach((field) => {
    resetData[field.key] = '';
  });
  internalModel.value = resetData;
}

function getFormData() {
  return { ...formModel.value };
}

function validateField(key: string) {
  return formRef.value?.validate(undefined, (rule) => rule?.key === key);
}

defineExpose({
  validate,
  resetForm,
  getFormData,
  validateField,
});
</script>
