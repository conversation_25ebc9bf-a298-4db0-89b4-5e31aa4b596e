<template>
  <div class="text-[#1D2129]">
    <div class="text-[#3b82f6] mb-[20px]">
      Set permissions individually for each user. By default, members will have
      access to view their personal invitation records after joining.
    </div>
    <n-spin :show="loading" stroke="#3b82f6">
      <n-table :bordered="false" :single-line="true">
        <thead>
          <tr>
            <th>Page Name</th>
            <th v-for="option in permissionOptions" :key="option.value">
              {{ option.label }}
            </th>
          </tr>
        </thead>

        <tbody>
          <tr v-for="page in pageList" :key="page.key">
            <td>{{ page.label }}</td>
            <td v-for="option in permissionOptions" :key="option.value">
              <n-radio
                :theme-overrides="radioTheme"
                :disabled="page?.disabled && option.value === Permission.OWN"
                :checked="
                  permission ? permission[page.key] === option.value : false
                "
                @update:checked="() => handleChange(page.key, option.value)"
              />
            </td>
          </tr>
        </tbody>
      </n-table>
    </n-spin>
  </div>
</template>
<script lang="ts" setup>
import { permissionOptions, pageList } from '~/constants/liveMng';
import { Permission, LiveMngPage, type PermissionModel } from '~/types/liveMng';

const props = defineProps<{
  uid: string;
}>();
const { liveMngApi } = useApi();
const permission = ref<PermissionModel>();
const loading = ref(false);

const getPermissio = async () => {
  loading.value = true;
  const { data: res } = await liveMngApi.getStreamerPermissionInfo({
    streamer_uid: props.uid,
  });
  if (res.value.code === 0) {
    permission.value = res.value.data.guild_permission;
  }
  loading.value = false;
};

const handleChange = (key: LiveMngPage, value: Permission) => {
  if (!permission.value) return;
  permission.value = {
    ...permission.value,
    [key]: value,
  };
};

const radioTheme = {
  dotColorActive: '#3b82f6',
  colorDisabled: '#E5E7EB',
  boxShadowActive: 'inset 0 0 0 1px rgba(59, 130, 246, 0.3)',
  boxShadowHover: 'inset 0 0 0 1px rgba(59, 130, 246, 0.3)',
  boxShadowFocus: 'inset 0 0 0 1px rgba(59, 130, 246, 0.3)',
};

const getCurrentPermission = () => permission.value;

defineExpose({
  getCurrentPermission,
});

onMounted(() => {
  getPermissio();
});
</script>
