<template>
  <div class="bg-white p-[20px] rounded-md">
    <div>
      <div class="text-lg text-[#1D2129] font-medium">
        {{ isPersona ? 'My Income' : 'Guild Information' }}
      </div>
    </div>
    <div class="my-4">
      The cashback platform involves your asset information, so please do not
      let others manage your account on your behalf.
    </div>
    <div class="grid grid-cols-2 gap-[20px]">
      <LiveMngCard
        title="Income Balance"
        :content="incomeBalance"
        bgColor="#B0EDEDd0"
        icon="income"
      >
        <div class="flex-1 text-end text-[#165DFF]">
          <span
            class="cursor-pointer"
            @click="navigateTo('live-mng/withdrawals')"
          >
            Withdrawals
          </span>
        </div>
      </LiveMngCard>
      <LiveMngCard
        title="Blocked Income"
        :content="blockedIncome"
        bgColor="#F5E8FF"
        icon="blocked"
      >
        <n-tooltip
          placement="right"
          trigger="hover"
          :show-arrow="false"
          :style="{
            padding: '12px',
            background: 'white',
          }"
        >
          <template #trigger>
            <div>
              <BaseIcon
                name="warning"
                class="w-[16px] text-[rgb(0,0,0,0.1)] ml-[6px] mt-[1px]"
                :filled="true"
              ></BaseIcon>
            </div>
          </template>
          <div class="w-[280px] text-[#1d2129]">
            {{
              isPersona
                ? `Frozen earnings are unverified rebate earnings that will be credited to your balance upon verification. Earnings will be invalidated in cases of referrer chargebacks, credit card fraud, or fraudulent transactions.`
                : `Frozen earnings are unverified rebate proceeds. Upon confirmation, they will be added to the guild's balance. However, these earnings will be invalidated if linked to referrer refunds, credit card fraud, or other deceptive transactions.`
            }}
          </div>
        </n-tooltip>
      </LiveMngCard>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  isPersona: boolean;
  incomeBalance: string;
  blockedIncome: string;
}

defineProps<Props>();
</script>
