<template>
  <div class="p-[20px] rounded-md bg-white">
    <div
      class="mb-6 flex gap-[20px]"
      :class="hasSlot ? 'flex-col' : ' flex-row items-center justify-between'"
    >
      <div class="text-lg text-[#1D2129] font-medium">{{ title }}</div>
      <div class="flex gap-[20px]">
        <slot name="prefix"></slot>
        <div class="flex-1"></div>
        <FilterForm
          v-if="hasForm"
          class="flex items-center max-sm:gap-2"
          :form-schema="formSchema"
          :form-state="formState"
        >
          <template v-for="item in formSchema" :key="item.key">
            <FilterComponetsFormItem
              :class="{
                'w-[320px] ml-[20px]': item?.key === 'search',
                'w-[200px]': item?.key !== 'search',
              }"
              :field="item"
              :field-name="item.key"
              :model-value="formState[item.key]"
              @update:model-value="handleUpdate(item.key, $event)"
            />
            <div v-if="item.key === 'search'" class="grow"></div>
          </template>
        </FilterForm>
        <slot name="suffix"></slot>
      </div>
    </div>

    <LiveMngTable
      :columns="columns"
      :data="tableData"
      :ellipsis="true"
      :bordered="false"
      :loading="loading"
      :paginator="pagination"
      :scroll-x="1150"
      @update:page="updatePage"
      @update:page-size="updatePageSize"
    />
  </div>
</template>

<script setup lang="ts">
import type { FilterFormSchema } from '~/types/filter';

const props = defineProps<{
  title?: string;
  fetchFn: (params: any) => Promise<any>;
  columns: any[];
  formSchema: FilterFormSchema;
  formState: Record<string, any>;
}>();
const slots = useSlots();
const hasSlot = computed(() => Object.keys(slots).length > 0);
const hasForm = computed(() => Object.keys(props.formSchema).length > 0);
const loading = ref(false);
const tableData = ref([]);
const pagination = reactive({
  page_size: 10,
  page: 1,
  total: 0,
});

const getData = async () => {
  loading.value = true;
  const { data: res } = await props.fetchFn({
    ...pagination,
    ...props.formState,
  });
  const data = res.value?.data;
  if (data) {
    tableData.value = data.items;
    pagination.total = data.total;
  }
  loading.value = false;
};

const { handleUpdate } = useFormUpdate(props.formState, () => {
  pagination.page = 1;
  getData();
});

const updatePage = (page: number) => {
  pagination.page = page;
  getData();
};
const updatePageSize = (size: number) => {
  pagination.page_size = size;
  pagination.page = 1;
  getData();
};

defineExpose({
  refreshTable: () => {
    pagination.page = 1;
    getData();
  },
});
onMounted(() => {
  getData();
});
</script>
