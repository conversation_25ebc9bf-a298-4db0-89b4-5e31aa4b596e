<template>
  <n-tooltip
    :show-arrow="false"
    placement="bottom"
    :style="{
      padding: '12px',
      background: 'white',
    }"
  >
    <template #trigger>
      <div v-if="userInfo" class="flex items-center cursor-pointer">
        <n-avatar round size="small" :src="userInfo.user_avatar" />
        <span class="ml-[8px]">{{ userInfo.user_name }}</span>
        <BaseIcon name="arrow" class="!text-[8px] ml-[8px]"></BaseIcon>
      </div>
      <div v-else></div>
    </template>
    <div class="text-[#1D2129]">
      <div class="flex items-center w-[120px]">
        <div class="flex items-center">
          <n-avatar round size="small" :src="userInfo.user_avatar" />
        </div>
        <n-ellipsis :tooltip="false">
          <span class="ml-[8px]">
            {{ userInfo.user_name }}
          </span>
        </n-ellipsis>
      </div>
      <n-divider class="my-[12px]" />
      <div
        class="cursor-pointer hover:bg-[#F7F8FA] px-[10px] py-[8px] rounded"
        @click="logout"
      >
        <BaseIcon name="logout" class="!text-[12px] mr-[4px]"></BaseIcon>
        Log out
      </div>
    </div>
  </n-tooltip>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
const settingStore = useSettingStore();
const userInfo = computed(() => settingStore.userInfo);
const { openLiveMngDialog } = useLiveMngDialog();

const logout = () => {
  openLiveMngDialog({
    class: 'w-[436px] rounded-[8px]',
    title: 'Log out',
    content: 'Do you wish to log out?',
    confirmText: 'Log out',
    onConfirm: () => {
      settingStore.logout(true);
    },
  });
};
</script>
