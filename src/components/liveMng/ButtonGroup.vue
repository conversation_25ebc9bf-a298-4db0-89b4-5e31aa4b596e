<template>
  <div class="flex">
    <button
      v-for="option in options"
      :key="option.value"
      :class="[
        'px-4 py-[4px] border first:rounded-l first:mr-[-1px] last:rounded-r last:ml-[-1px]',
        modelValue === option.value
          ? 'bg-blue-500 text-white border-blue-600'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100',
      ]"
      @click="select(option.value)"
    >
      {{ option.label }}
    </button>
  </div>
</template>

<script lang="ts" setup>
interface Option {
  label: string;
  value: number | string;
}
const modelValue = defineModel<number | string>();

defineProps<{
  options: Option[];
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | string): void;
  (e: 'change', value: number | string): void;
}>();

function select(value: number | string) {
  modelValue.value = value;
  emit('change', value);
}
</script>
