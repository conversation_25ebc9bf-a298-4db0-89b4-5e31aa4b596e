<template>
  <div class="p-[20px] bg-white rounded-md">
    <div class="text-lg text-[#1D2129] font-medium">Guild Information</div>
    <div class="flex mt-4 gap-[20px]">
      <div class="w-[60px] h-[60px]">
        <img :src="guildAvatar" alt="" />
      </div>
      <div class="flex-1">
        <div
          class="text-lg text-[#1D2129] font-medium my-2 h-[25px] leading-[25px]"
        >
          {{ guildName }}
        </div>
        <div>Guild Master: {{ guildMaster }}</div>
      </div>
      <n-button v-if="!isGuildMaster" type="info" ghost @click="leaveGuild">
        Leave Guild
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import guildAvatar from '~/assets/icons/guild_avatar.png';
interface Props {
  guildName: string;
  guildMaster: string;
  isGuildMaster: boolean;
  leaveGuild: () => void;
}
defineProps<Props>();
</script>
