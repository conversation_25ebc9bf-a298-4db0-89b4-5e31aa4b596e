<template>
  <div class="bg-white p-[20px] rounded-md">
    <div>
      <div class="text-lg text-[#1D2129] font-medium">My Promotion Link</div>
    </div>
    <div class="my-4">
      When other users successfully register through your invitation link, a
      promotion binding relationship will be formed. Copy the invitation address
      below to start promoting!
    </div>
    <div
      class="px-4 h-[42px] rounded flex items-center justify-between bg-[#F7F8FA] text-[#1D2129] mt-4 mb-2 cursor-pointer"
      @click="copy(inviteUrl)"
    >
      <ClientOnly>
        <div class="flex-1">{{ inviteUrl }}</div>
      </ClientOnly>
      <svgo-copy class="w-[20px] h-[20px]"></svgo-copy>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';

const settingStore = useSettingStore();

const inviteUrl = computed(() => {
  if (import.meta.server) return '';
  return settingStore.userInfo?.invite_code;
});
</script>
