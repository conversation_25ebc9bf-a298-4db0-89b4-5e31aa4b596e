<template>
  <div
    class="pt-[20px] pb-[22px] px-[16px] rounded-md flex"
    :style="{ background: bgColor }"
  >
    <div v-if="icon" class="mr-[20px] flex items-center">
      <BaseIcon :name="icon" class="w-[60px] h-[60px] relative top-[4px]" />
    </div>
    <div class="flex-1">
      <div class="flex items-center">
        <div class="font-medium">{{ title }}</div>
        <slot></slot>
      </div>

      <div class="text-3xl mt-2.5 font-bold">
        {{ content }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  bgColor: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
});
</script>
