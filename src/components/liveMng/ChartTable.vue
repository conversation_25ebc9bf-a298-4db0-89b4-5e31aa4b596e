<template>
  <div class="bg-white p-[20px] rounded-md flex-1">
    <div class="mb-6 flex items-center justify-between">
      <div class="text-lg text-[#1D2129] font-medium">Earnings Trend</div>
      <LiveMngButtonGroup v-model="days" :options="buttonOptions" />
    </div>
    <div ref="chartRef" class="w-full h-[350px]" />

    <div class="mb-6 mt-2">
      <div class="text-lg text-[#1D2129] font-medium">Income Details</div>
    </div>

    <n-data-table
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :pagination="paginationReactive"
      :loading="loading"
    />
  </div>
</template>

<script setup lang="ts">
import type { EChartsOption } from 'echarts';

const props = defineProps<{
  data: any[];
  loading: boolean;
}>();

const chartRef = ref<HTMLElement | null>(null);

const { init, setOption } = useEchart(chartRef);

const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
});
const buttonOptions = [
  { label: '30 days', value: 30 },
  { label: '60 days', value: 60 },
  { label: '90 days', value: 90 },
];
const days = ref(30);
const tableData = computed(() => props.data.slice(0, days.value));
watch(days, () => {
  paginationReactive.page = 1;
  changeChartData([...tableData.value].reverse());
});

const changeChartData = (data: any[]) => {
  const xData: string[] = [];
  const yData: string[] = [];

  data.forEach((item: any) => {
    xData.push(item.data_time);
    yData.push(item.expected_return);
  });

  option.value = {
    ...option.value,
    xAxis: { type: 'category', data: xData },
    series: [{ data: yData, type: 'line' }],
  };
  setOption(option.value);
};

const option = ref({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      data: [],
      type: 'line',
    },
  ],
}) as Ref<EChartsOption>;
const columns = [
  {
    title: 'Date',
    key: 'data_time',
    width: '50%',
  },
  {
    title: 'Expected Return',
    key: 'expected_return',
    render(row: any) {
      return '$ ' + row.expected_return;
    },
  },
];

// 抛出方法给父组件使用
defineExpose({ changeChartData });

onMounted(() => {
  init(option.value);
});
</script>
