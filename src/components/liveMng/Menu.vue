<template>
  <div class="w-[210px] px-[2px] pt-[14px] bg-white">
    <n-menu
      :options="resolvedOptions"
      :indent="20"
      :root-indent="14"
      :theme-overrides="{
        itemColorActive: 'rgb(22,93,255,0.05)',
        itemTextColorActive: '#165DFF',
        itemColorHover: 'rgb(22,93,255,0.05)',
        itemColorActiveHover: 'rgb(22,93,255,0.05)',
        itemTextColorActiveHover: '#165DFF',
        itemTextColor: '#4E5969',
      }"
      :value="selectedTab"
      @update:value="handleSelectTab"
    />
  </div>
</template>

<script setup lang="ts">
import { menuList, pageMap } from '~/constants/liveMng';
import { useLiveMngStore } from '~/stores/modules/liveMng';
import { Permission } from '~/types/liveMng';
const router = useRouter();
const tabConfig = useTabRouteConfig(menuList);
const { tabs, selectedTab } = tabConfig;
const liveMngStore = useLiveMngStore();

const permission = computed(
  () => liveMngStore.permissionInfo?.guild_permission,
);

const resolvedOptions = computed(() => {
  const wrap = (items: Tab[]): any[] =>
    items.map((item) => {
      const isActive = item.value === selectedTab.value;
      const permissionKey = pageMap[item.value];
      const iconFn = () => {
        const Icon = isActive ? item.action_icon || item.icon : item.icon;
        return Icon
          ? h(Icon, { filled: true, class: 'w-[18px] h-[18px]' })
          : null;
      };
      const lableFn = () => {
        if (item.type === 'group') return item.title;
        return h('span', { class: isActive ? 'font-medium' : '' }, item.title);
      };
      const isShow = () => {
        if (item.value === 'home') return true;
        if (liveMngStore.isPersona && item.value === 'member') return false;
        if (!permission.value || liveMngStore.isPersona) return true;
        if (permission.value[permissionKey] === Permission.NONE) return false;
        return true;
      };

      return {
        ...item,
        icon: iconFn,
        show: liveMngStore.isMaster ? true : isShow(),
        title: lableFn,
        key: item.value,
        children: item.children ? wrap(item.children as Tab[]) : undefined,
      };
    });

  return wrap(tabs);
});

const handleSelectTab = (key: string, tab: any) => {
  selectedTab.value = key;
  router.push(tab.route ? `/live-mng/${tab.route}` : '/live-mng');
};
watch(
  () => router.currentRoute.value,
  () => {
    selectedTab.value = tabConfig.handleRouteChange(router);
  },
  { immediate: true },
);
</script>
