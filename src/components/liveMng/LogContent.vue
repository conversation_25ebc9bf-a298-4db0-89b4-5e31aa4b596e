<template>
  <p class="[&>b]:text-[#f4b438] [&>b]:font-medium [&>span]:mx-2">
    <template v-if="type === 1">
      <b>{{ streamer }}</b>
      <span>has accepted</span>
      <b>{{ operation }}</b>
      <span>'s invitation and joined the guild.</span>
    </template>
    <template v-else-if="type === 2">
      <b>{{ streamer }}</b>
      <span>has left the guild.</span>
    </template>
    <template v-else-if="type === 3">
      <b>{{ streamer }}</b>
      <span>was removed from the guild by</span>
      <b>{{ operation }}</b>
      .
    </template>
  </p>
</template>

<script setup lang="ts">
interface Props {
  type: 1 | 2 | 3; // 操作类型 1邀请加入公会 2主动退出公会 3被踢出公会
  streamer: string;
  operation?: string;
}

defineProps<Props>();
</script>
