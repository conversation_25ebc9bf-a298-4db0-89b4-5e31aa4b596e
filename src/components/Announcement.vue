<!-- eslint-disable vue/no-v-html -->
<template>
  <client-only>
    <div
      v-if="visible"
      class="announcement mb-lg p-lg flex items-center gap-lg"
    >
      <div class="size-[26px]">
        <base-icon name="speaker" class="text-red-1 size-full" />
      </div>
      <div ref="carouselRef" class="flex-1">
        <n-carousel
          v-if="!mobile"
          autoplay
          direction="vertical"
          :show-dots="false"
          :interval="10000"
          :style="carouselStyles"
        >
          <div
            v-for="item in announcements"
            :key="item.sort_num"
            class="h-full flex items-center"
          >
            <p class="leading-snug break-words text-base/[20px] font-medium">
              <span>{{ item.content }}</span>
              <span
                v-if="!!item.detail_addr"
                class="ml-md text-primary-400 cursor-pointer hover:text-primary-500 transition-colors"
                @click="handleViewDetails(item)"
              >
                {{ t('view_details', 'View details') }}
              </span>
            </p>
          </div>
        </n-carousel>
        <n-marquee v-else>
          <p v-html="marqueeText"></p>
        </n-marquee>
      </div>
      <base-icon
        name="close-stroke"
        class="text-white size-[18px] cursor-pointer hover:opacity-80 transition-opacity"
        :filled="false"
        @click="close"
      />
    </div>
  </client-only>
</template>

<script setup lang="ts">
interface Props {
  announcements?: ApiV1NotifyNoticeGet200ResponseDataListInner[];
}

interface Emits {
  (e: 'close'): void;
  (e: 'view-details', item: ApiV1NotifyNoticeGet200ResponseDataListInner): void;
}

const props = withDefaults(defineProps<Props>(), {
  announcements: () => [],
});

const emit = defineEmits<Emits>();

const { t } = useI18n();
const mobile = useIsMobile();
const visible = ref(false);
const carouselRef = ref<HTMLElement>();
const containerWidth = ref(800);
const defaultContentHeight = 22;
const STORAGE_KEY = 'announcement';

const marqueeText = computed(() => {
  return props.announcements
    .map((item) => item.content || '')
    .join('&nbsp;     &nbsp;'.repeat(8));
});

const getAnnouncementDigest = (): string => {
  if (!props.announcements || props.announcements.length === 0) return '';

  const stringHash = (str: string): string => {
    let hash = 0;
    if (str.length === 0) return hash.toString(16);

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }

    return hash.toString(16);
  };

  return props.announcements
    .map(
      (item) =>
        `${item.sort_num}-${stringHash(item.content || '')}-${item.detail_addr || ''}`,
    )
    .join('|');
};

const getMeasuredCarouselHeight = (): number => {
  if (import.meta.server || props.announcements.length === 0) {
    return defaultContentHeight;
  }
  if (containerWidth.value <= 0) return defaultContentHeight;
  try {
    let maxHeight = defaultContentHeight;
    props.announcements.forEach((item) => {
      const measureContainer = document.createElement('div');
      measureContainer.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        visibility: hidden;
        width: ${containerWidth.value}px;
        display: flex;
        align-items: center;
      `;

      const itemDiv = document.createElement('div');
      itemDiv.className = `h-full flex items-center`;

      const pElement = document.createElement('p');
      pElement.className =
        'leading-snug break-words text-base/[20px] font-medium';

      const contentSpan = document.createElement('span');
      contentSpan.textContent = item.content || '';
      pElement.appendChild(contentSpan);

      if (item.detail_addr) {
        const linkSpan = document.createElement('span');
        linkSpan.textContent = t('view_details', 'View details');
        linkSpan.style.cssText = `margin-left: 16px;`;
        pElement.appendChild(linkSpan);
      }

      itemDiv.appendChild(pElement);
      measureContainer.appendChild(itemDiv);
      document.body.appendChild(measureContainer);
      const height = measureContainer.offsetHeight;
      maxHeight = Math.max(maxHeight, height);
      document.body.removeChild(measureContainer);
    });
    return Math.max(maxHeight + 8, defaultContentHeight);
  } catch (error) {
    return defaultContentHeight;
  }
};

// 轮播高度计算
const carouselHeight = computed(() => {
  return getMeasuredCarouselHeight();
});

const carouselStyles = computed(() => ({
  width: '100%',
  height: `${carouselHeight.value}px`,
}));

let resizeObserver: ResizeObserver | null = null;

const updateContainerWidth = () => {
  if (carouselRef.value) {
    const newWidth = carouselRef.value.offsetWidth;
    if (newWidth > 0 && newWidth !== containerWidth.value) {
      containerWidth.value = newWidth;
    }
  }
};

const observeResize = () => {
  if (
    typeof window !== 'undefined' &&
    window.ResizeObserver &&
    carouselRef.value
  ) {
    resizeObserver = new ResizeObserver(() => {
      updateContainerWidth();
    });
    resizeObserver.observe(carouselRef.value);
  }
};

const unobserveResize = () => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
};

// 是否显示公告
const shouldShowAnnouncement = (): boolean => {
  if (props.announcements.length === 0) return false;

  try {
    const closedData = sessionStorage.getItem(STORAGE_KEY);
    if (!closedData) return true;
    const { digest } = JSON.parse(closedData);
    const currentDigest = getAnnouncementDigest();
    return digest !== currentDigest;
  } catch (error) {
    return true;
  }
};

const close = () => {
  visible.value = false;
  try {
    sessionStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({
        closedAt: Date.now(),
        digest: getAnnouncementDigest(),
      }),
    );
  } catch (error) {
    console.error('Error:', error);
  }
  emit('close');
};

const handleViewDetails = (
  item: ApiV1NotifyNoticeGet200ResponseDataListInner,
) => {
  emit('view-details', item);
  if (item.detail_addr) {
    const url =
      item.detail_addr.startsWith('http://') ||
      item.detail_addr.startsWith('https://')
        ? item.detail_addr
        : `https://${item.detail_addr}`;
    window.open(url, '_blank');
  }
};

watch(
  () => props.announcements,
  (
    newAnnouncements:
      | ApiV1NotifyNoticeGet200ResponseDataListInner[]
      | undefined,
  ) => {
    if (newAnnouncements && newAnnouncements.length > 0) {
      visible.value = shouldShowAnnouncement();
    } else {
      visible.value = false;
    }
  },
  { immediate: true },
);

watch(
  carouselRef,
  () => {
    updateContainerWidth();
  },
  { immediate: true },
);

onMounted(() => {
  nextTick(() => {
    observeResize();
    if (props.announcements && props.announcements.length > 0) {
      visible.value = shouldShowAnnouncement();
    }
  });
});

onUnmounted(() => {
  unobserveResize();
});

defineExpose({
  close,
  visible: readonly(visible),
});
</script>

<style lang="scss" scoped>
.announcement {
  @apply relative rounded-[8px] overflow-hidden;
  &::before {
    content: '';
    @apply absolute top-0 left-0 w-[40%] h-full pointer-events-none;
    background: linear-gradient(
      96deg,
      rgba(248, 184, 56, 0.3) 0%,
      rgba(21, 26, 41, 0) 99%
    );
  }
  &::after {
    content: '';
    @apply absolute top-0 right-0 w-[23%] h-full pointer-events-none rotate-180;
    background: linear-gradient(
      101deg,
      rgba(248, 184, 56, 0.15) 0%,
      rgba(21, 26, 41, 0) 99%
    );
  }
}
</style>
