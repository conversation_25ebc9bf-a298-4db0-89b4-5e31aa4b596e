<template>
  <div
    class="spin-container w-[100vw] h-[100vh] flex items-center justify-center fixed top-0 left-0"
  >
    <n-spin size="large" :theme-overrides="spinThemeOverrides" v-bind="attr" />
  </div>
</template>

<script setup lang="ts">
import type { SpinProps } from 'naive-ui';
type SpinPropsThemeOverrides = NonNullable<SpinProps['themeOverrides']>;

const primaryColor = '#FFE100';
const spinThemeOverrides: SpinPropsThemeOverrides = {
  color: primaryColor,
};

const attr = useAttrs();
</script>

<style scoped lang="scss">
.spin-container {
  z-index: 99999;
  background: #0a0d14;
  // background: rgba(255, 255, 255, 0.105);
  backdrop-filter: blur(6px) brightness(100%);
}
</style>
