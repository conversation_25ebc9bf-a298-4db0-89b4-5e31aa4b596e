<template>
  <Copy
    :tooltip-default="tooltipDefault"
    :disabled="disabled"
    :tooltip="tooltip"
    :text="text"
    :placement="placement"
    :text-to-copy="textToCopy"
    :on-success="onSuccess"
    :on-error="onError"
  >
    <Text v-bind="$attrs" :tooltip="false">
      <slot></slot>
    </Text>
  </Copy>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    tooltipDuration?: number;
    tooltipDefault?: string;
    disabled?: boolean;
    tooltip?: boolean;
    showIcon?: boolean;
    text?: string;
    textToCopy?: string;
    tooltipSuccess?: string;
    tooltipError?: string;
    placement?:
      | 'top-start'
      | 'top'
      | 'top-end'
      | 'right-start'
      | 'right'
      | 'right-end'
      | 'bottom-start'
      | 'bottom'
      | 'bottom-end'
      | 'left-start'
      | 'left'
      | 'left-end';
    onSuccess?: () => void;
    onError?: (error: Error) => void;
  }>(),
  {
    tooltipDuration: 1000,
    tooltipDefault: 'click_to_copy_to_clipboard',
    disabled: false,
    tooltip: true,
    showIcon: false,
    text: '',
    textToCopy: '',
    tooltipSuccess: 'replicating_success',
    tooltipError: '复制失败',
    placement: 'top',
    onSuccess: () => {},
    onError: () => {},
  },
);

defineOptions({
  inheritAttrs: false,
});
</script>
