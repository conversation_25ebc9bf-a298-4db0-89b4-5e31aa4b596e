<template>
  <div>
    <Table v-bind="$attrs" />
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
:deep(.n-data-table-table) {
  border-spacing: 0 8px !important;
}

:deep(.n-data-table-tr td:first-child) {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

:deep(.n-data-table-thead) {
  display: none !important;
}

:deep(.n-data-table-tr td:last-child) {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

:deep(.n-data-table-td) {
  color: #fff;
}
</style>
