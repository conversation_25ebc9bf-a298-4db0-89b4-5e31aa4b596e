<template>
  <div>
    <div class="flex items-center gap-2">
      <BaseIcon
        v-if="statusIcon"
        :name="statusIcon"
        :class="['mb-0 w-[20px] h-[20px]']"
      ></BaseIcon>
      <p :class="['text-base leading-tight', statusColorClass]">
        {{ statusText }}
      </p>
    </div>

    <p
      v-if="item.extract_status === 4"
      class="flex mt-[10px] text-[#fff] text-light-2 gap-1"
    >
      <Tooltip :trigger="item.extract_order_status === 12 ? 'hover' : 'manual'">
        <template #trigger>
          <span>{{ orderStatusText }}</span>
        </template>
        <p class="max-w-[300px]">
          {{ $t('steam_account_error') }}
        </p>
      </Tooltip>
    </p>
    <div v-if="showActions" class="mt-[10px] flex items-center gap-sm">
      <MainButton v-if="item.can_cancel" @click="handleCancel">
        {{ $t('cancel') }}
      </MainButton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  item: ApiV1BackpackExtractListGet200ResponseDataItemsInner;
}

const props = withDefaults(defineProps<Props>(), {
  item: () => ({}) as ApiV1BackpackExtractListGet200ResponseDataItemsInner,
});

const { t } = useI18n();

const statusText = computed(() => {
  const statusMap = {
    1: 'waiting_for_shipment',
    2: 'waiting_for_acceptance_of_quotation',
    3: 'extraction_successful',
    4: 'extraction_failed',
  } as const;
  return props.item.extract_status
    ? t(statusMap[props.item.extract_status as keyof typeof statusMap])
    : '';
});

const statusIcon = computed(() => {
  const statusMap = {
    1: 'goods-waiting',
    2: 'goods-waiting',
    3: 'goods-success',
    4: 'goods-error',
  };
  return statusMap[props.item.extract_status as keyof typeof statusMap] || null;
});

const orderStatusText = computed(() => {
  const statusMap = {
    11: 'this_skin_is_currently_out_of_stock',
    12: 'steam_account_abnormal',
    13: 'quotation_expired',
    14: 'you_refused_the_quotation',
  } as const;
  return props.item.extract_order_status
    ? t(statusMap[props.item.extract_order_status as keyof typeof statusMap])
    : '';
});

const statusColorClass = computed(() => {
  const colorMap = {
    1: 'text-white',
    2: 'text-white',
    3: 'text-green-500',
    4: 'text-warning-500',
  };
  return (
    colorMap[props.item.extract_status as keyof typeof colorMap] || 'text-white'
  );
});

const showActions = computed(
  () => props.item.extract_status && props.item.extract_status < 3,
);

const emit = defineEmits<{
  (e: 'cancel'): void;
}>();

// 取消取回
const handleCancel = () => {
  emit('cancel');
};
</script>
