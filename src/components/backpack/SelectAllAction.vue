<template>
  <div class="flex flex-1 items-center gap-4">
    <n-checkbox
      size="large"
      :checked="checked"
      :indeterminate="indeterminate"
      @update:checked="$emit('update:checked', $event)"
    >
      <span class="text-[16px]">{{ $t('select_all') }}</span>
    </n-checkbox>

    <MainButton
      class="uppercase max-md:!min-w-auto max-sm:!p-1"
      type="secondary"
      :disabled="disabled"
      :loading="loading"
      @click="$emit('convert')"
    >
      <span>{{ $t('convert') }}</span>
    </MainButton>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  checked: boolean;
  indeterminate: boolean;
  disabled: boolean;
  loading: boolean;
}>();

defineEmits<{
  (e: 'update:checked', value: boolean): void;
  (e: 'convert'): void;
}>();
</script>
