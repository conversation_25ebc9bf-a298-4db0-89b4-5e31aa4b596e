<template>
  <n-spin
    :show="loading && !isPageLoading"
    class="flex-layout min-h-[400px]"
    content-class="flex-layout"
  >
    <div
      class="grid grid-cols-[repeat(auto-fill,minmax(104px,1fr))] gap-[9px] sm:grid-cols-[repeat(auto-fill,minmax(190px,1fr))]"
    >
      <template v-if="!isPageLoading">
        <div
          v-for="item in items"
          :key="item.backpack_id"
          class="relative group"
        >
          <BackpackCardItem
            :id="item.backpack_id || 0"
            :key="item.backpack_id"
            :icon-url="item.goods_info?.icon_url || ''"
            :price="item.goods_info?.pawn_price || 0"
            :tags="item.goods_info?.tags"
            :info="item.goods_info"
            :show-count="false"
            :action-loading="actionLoading"
            tagWrapperClass="max-sm:pl-[0.8em] max-sm:pt-[0.7em]"
            @convert="openConvertTip(item)"
            @extract="openExtractTip(item)"
          ></BackpackCardItem>
          <div
            class="absolute right-4 top-4 group-hover:block z-40"
            :class="{ hidden: !item.selected }"
          >
            <n-checkbox
              size="large"
              :checked="item.selected"
              @update:checked="(value) => (item.selected = value)"
            ></n-checkbox>
          </div>
        </div>
      </template>
      <template v-else>
        <ExchangeCardSkeleton
          :length="24"
          :loading="loading"
        ></ExchangeCardSkeleton>
      </template>
    </div>
    <slot v-if="!items.length && !loading" name="empty">
      <ClientOnly>
        <Empty :msg="$t('no_record_found')" column />
      </ClientOnly>
    </slot>
  </n-spin>
</template>

<script setup lang="ts">
import type { SkinItemWithGoods } from '~/types/backpack';

interface Props {
  items: SkinItemWithGoods[];
  loading: boolean;
  isPageLoading?: boolean;
  showResetBtn?: boolean;
  subContent?: string;
  mask?: boolean;
  actionLoading?: string | null;
}

withDefaults(defineProps<Props>(), {
  loading: false,
  isPageLoading: false,
  showResetBtn: false,
  subContent: '',
  mask: true,
  actionLoading: null,
});

const emit = defineEmits<{
  (e: 'convert', data: ApiV1BackpackPawnPostRequest): void;
  (e: 'extract', data: ApiV1BackpackExtractPostRequest): void;
  (e: 'resetOptions'): void;
}>();

const openConvertTip = (item: any) => {
  emit('convert', {
    goods: [
      {
        backpack_id: item.backpack_id,
        pawn_price: Number(item.goods_info?.pawn_price),
      },
    ],
  });
};

const openExtractTip = (item: any) => {
  emit('extract', {
    backpack_id: item.backpack_id,
  });
};
</script>
