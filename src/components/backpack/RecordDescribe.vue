<template>
  <CopyableText
    :disabled="true"
    :tooltipDefault="
      $te(labelMap[type] ?? '')
        ? $t(labelMap[type]) + '：' + recordDetail
        : recordDetail
    "
    :ellipsis="true"
    :tooltip="type !== 1 && type !== 12"
    :class="['truncate', { '!flex items-center': type === 1 || type === 12 }]"
  >
    <span class="break-normal whitespace-nowrap">
      {{ $te(labelMap[type] ?? '') ? $t(labelMap[type]) + '：' : '' }}
    </span>
    <template v-if="type === 1 || type === 12">
      <Currency :amount="recordDetail" />
    </template>
    <template v-else>
      {{ recordDetail }}
    </template>
  </CopyableText>
</template>

<script setup lang="ts">
import type { BackpackRecordType } from '~/constants/backpack';
import { BACKPACK_RECORD_LABEL_MAP } from '~/constants/backpack';

interface Props {
  type: BackpackRecordType;
  recordDetail: string;
}

const labelMap = BACKPACK_RECORD_LABEL_MAP;

withDefaults(defineProps<Props>(), {
  type: 1,
  recordDetail: '',
});
</script>
