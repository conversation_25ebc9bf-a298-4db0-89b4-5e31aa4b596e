<template>
  <SkinCard
    :id="id"
    :item="info"
    :isAvailable="true"
    :disabled="false"
    :actionShow="true"
    :colorCode="info?.tags?.rarity?.color || ''"
    :iconUrl="info?.icon_url"
    :categoryName="info?.category_name || ''"
    :title="$td(info?.steam_item_name || '')"
    :titleTooltip="$td(info?.market_hash_name || '')"
    :price="price?.toString() || '0'"
    :actionText="$t('exchange')"
    :disabledText="$t('no_more')"
  >
    <template #action-button>
      <div class="grid grid-cols-1 gap-6 max-sm:gap-2">
        <MainButton
          class="uppercase max-sm:!min-w-[90px] max-sm:!text-xs max-sm:!p-1"
          type="secondary"
          :loading="actionLoading === 'convert'"
          @click="openConvertTip"
        >
          <span>{{ $t('convert') }}</span>
        </MainButton>

        <MainButton
          class="uppercase max-sm:!min-w-[90px] max-sm:!text-xs max-sm:!p-1"
          type="linear"
          :loading="actionLoading === 'extract'"
          @click="openExtractTip"
        >
          <span>{{ $t('extracting') }}</span>
        </MainButton>
      </div>
    </template>
  </SkinCard>
</template>

<script setup lang="ts">
import type { CaseSkinItemType } from '~/types/cases';

interface Props {
  id: number;
  iconUrl: string;
  price?: number | string | null;
  num?: number;
  // 是否开启蒙层
  mask?: boolean;
  info?: CaseSkinItemType;
  actionLoading?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  mask: true,
  price: 0,
  num: 0,
  info: () => ({}),
  actionLoading: null,
});

const emit = defineEmits<{
  (e: 'convert', data: ApiV1BackpackPawnPostRequest): void;
  (e: 'extract', data: ApiV1BackpackExtractPostRequest): void;
}>();

const openConvertTip = () => {
  emit('convert', {
    goods: [
      {
        backpack_id: props.id,
        pawn_price: Number(props.price),
      },
    ],
  });
};

const openExtractTip = () => {
  emit('extract', {
    backpack_id: props.id,
  });
};
</script>
