<template>
  <div class="h-[42px] flex items-center cursor-pointer">
    <div
      :class="[
        'px-[12px] h-full flex flex-col items-center justify-center rounded-l-[4px] bg-[#262D49]',
        currencyClass,
      ]"
    >
      <Currency
        :amount="amount"
        :isAnimated="isAnimated"
        :icon-color="iconColor"
        :size="mobile ? '18px' : '22px'"
      />
    </div>
    <n-button
      class="px-[16px] min-w-[128px] h-full flex items-center justify-center rounded-l-none rounded-r-[4px] uppercase overflow-hidden font-medium"
      :class="[
        theme === 'primary' ? 'btn-gradient-primary' : 'btn-gradient-purple',
        actionClass,
      ]"
      :loading="loading"
      :disabled="disabled"
      @click="$emit('click')"
    >
      <span class="whitespace-normal break-words">{{ text }}</span>
    </n-button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  amount: number | string;
  text: string;
  theme?: 'primary' | 'purple';
  iconColor?: 'dark' | 'light';
  amountWidth?: string;
  buttonWidth?: string;
  isAnimated?: boolean;
  loading?: boolean;
  actionClass?: string;
  currencyClass?: string;
  disabled?: boolean;
}

withDefaults(defineProps<Props>(), {
  iconColor: 'dark',
  theme: 'primary',
  amountWidth: 'inherit',
  buttonWidth: 'inherit',
  isAnimated: false,
  loading: false,
  actionClass: '',
  currencyClass: '',
  disabled: false,
});

const mobile = useIsMobile();

defineEmits<{
  (e: 'click'): void;
}>();
</script>
