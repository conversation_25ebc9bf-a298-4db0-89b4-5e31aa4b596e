<template>
  <template v-if="gemInfo">
    <svgo-diamond
      v-if="gemInfo.icon"
      :color="`#${gemInfo.color}`"
      :filled="false"
      :font-controlled="false"
      :class="['mt-[2px]', $attrs.class]"
      :style="{
        width: `${size || 17}px`,
        height: `${size || 17}px`,
      }"
    />
    <span v-else :style="{ color: `#${gemInfo.color}` }">
      {{ gemInfo.shortName || gemInfo.fullName }}
    </span>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { GEM_PHASE_MAPPING } from '~/constants/skin';

interface Props {
  title: string;
  size?: number;
}

const props = defineProps<Props>();

const gemInfo = computed(() => GEM_PHASE_MAPPING[props.title] || null);
</script>
