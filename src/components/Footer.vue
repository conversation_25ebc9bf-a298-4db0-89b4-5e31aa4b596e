<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="pt-[100px]">
    <div class="footer-bg">
      <div class="relative">
        <div class="footer-light"></div>
        <div class="footer-arrow"></div>
        <a href="/">
          <img
            src="/imgs/home_logo.png"
            alt="logo"
            class="h-[37px] object-contain max-sm:mx-2"
          />
        </a>
        <p class="mt-3 sm:w-[570px] max-sm:mx-2">
          Your Trusted CS2 Skin Marketplace Welcome to SkinBucks, the premier
          marketplace for CS2 (Counter-Strike 2) skins, where gamers and
          collectors come together to gain access to the rarest and most
          sought-after skins with ease and security.
        </p>
      </div>
      <div class="flex gap-x-10 sm:gap-x-20 gap-y-2">
        <div
          v-for="item in hrefLists"
          :key="item.title"
          class="flex flex-col gap-y-[17px]"
        >
          <div class="text-highlight-text mb-[13px]">{{ item.label }}</div>
          <div
            v-for="list in item.lists"
            :key="list.title"
            class="text-[#7D90CA] leading-none cursor-pointer"
            @click="goPage(list)"
          >
            {{ list.label }}
          </div>
        </div>
      </div>
    </div>
    <p class="text-center py-4 text-[12px]/none text-[#647388] w-full">
      skin-bucks is owned and operated by Case Alchemy Limited. Copyright &copy;
      2025 {{ DOMAIN_CONFIG.DOMAIN }} .All rights reserved.
    </p>
  </div>
</template>
<script lang="ts" setup>
import { FooterInfo } from '~/constants/layout';
import { DOMAIN_CONFIG } from '~/constants';

const { t } = useI18n();
type HrefList = {
  title: string;
  label: string;
  link: string;
};
type HrefLists = {
  title: string;
  label: string;
  lists: HrefList[];
};
const hrefLists = computed<HrefLists[]>(() => FooterInfo(t));
const $router = useRouter();
const goPage = (list: HrefList) => {
  $router.push(list.link);
};
</script>
<style lang="scss" scoped>
.footer-light {
  @apply w-[1062px] h-[88px] absolute -top-[126px] left-1/2;
  background: url('/imgs/footer_light.png');
  background-position: center 0;
  transform: translateX(-50%);
  background-repeat: no-repeat;
}
.footer-arrow {
  @apply w-[453px] h-[204px] absolute -top-[40px] -left-[99px];
  background: url('/imgs/footer_m.png');
  background-position: center 0;
  background-size: contain;
  background-repeat: no-repeat;
}
.footer-bg {
  @apply bg-[#151A29] min-h-[205px] py-[40px] flex justify-center flex-wrap gap-x-[135px] gap-y-[20px] max-lg:gap-x-[1000px];
  background-image: url('/imgs/footer_l.png'), url('/imgs/footer_r.png');
  background-position:
    left bottom,
    right top;
  background-size: auto;
  background-repeat: no-repeat;
}
</style>
