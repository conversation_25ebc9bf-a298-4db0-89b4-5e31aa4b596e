<template>
  <Teleport to="body">
    <div v-if="show">
      <div class="verification-modal">
        <div class="main-wrapper" role="main">
          <div class="main-content">
            <h1 class="zone-name-title h1">
              <img src="/favicon.ico" class="heading-favicon" alt="" />
              {{ DOMAIN_CONFIG.DOMAIN }}
            </h1>
            <p class="h2 spacer-bottom">
              Verifying you are human. This may take a few seconds
            </p>
            <div>
              <CloudflareTurnstile
                ref="turnstileRef"
                :site-key="siteKey"
                @verified="onVerified"
                @error="onError"
                @expired="onExpired"
              />
            </div>
            <div class="core-msg spacer spacer-top">
              {{ DOMAIN_CONFIG.DOMAIN }} needs to review the security of your
              connection before proceeding。
            </div>
            <div v-if="errorMessage !== ''" class="error-message">
              {{ $t(errorMessage) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { DOMAIN_CONFIG } from '~/constants';

const props = defineProps({
  show: Boolean,
  siteKey: {
    type: String,
    required: true,
  },
  reason: {
    type: String,
    default: 'normal',
  },
  errorMessage: {
    type: String,
    default: 'verification_failed',
  },
});

const emit = defineEmits(['verified', 'close', 'error']);

const turnstileRef = ref(null);
const error = ref(null);

const onVerified = (token) => {
  error.value = null;
  emit('verified', { token, reason: props.reason });
};

const onError = () => {
  error.value = props.errorMessage;
  emit('error', props.errorMessage);
};

const onExpired = () => {
  error.value = props.errorMessage;
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
};

const reset = () => {
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
  error.value = null;
};

defineExpose({
  reset,
});
</script>
<style scoped lang="scss">
.verification-modal {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 999999999;
  background: #0a0d14;
  color: #fff;
  display: flex;
  justify-content: center;
}
.main-content {
  margin: 30vh auto 0;
  max-width: max-content;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  width: 100%;
}
.spacer {
  margin: 2rem 0;
}
.spacer-top {
  margin-top: 4rem;
}
.spacer-bottom {
  margin-bottom: 2rem;
}
.heading-favicon {
  height: 2rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}
@media (width <= 720px) {
  .main-content {
    margin-top: 50%;
  }
  .heading-favicon {
    height: 1.5rem;
    width: 1.5rem;
  }
}
.main-wrapper {
  align-items: start;
  display: flex;
  flex: 1;
  flex-direction: column;
}
.error-message {
  color: red;
  margin-top: 50px;
}
</style>
