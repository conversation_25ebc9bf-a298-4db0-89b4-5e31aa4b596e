<!-- eslint-disable vue/multi-word-component-names -->
<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="text-center">
    <BaseIcon
      v-if="type !== 'default'"
      :name="icon.html"
      :class="`rounded-full !text-[44px] ${icon.class}`"
    ></BaseIcon>
    <div
      class="base-color text-center mt-3"
      :class="contentClass"
      v-html="message"
    ></div>
    <div v-if="btns" class="mt-5 flex justify-center">
      <n-button
        v-if="btns.closeBtn"
        ghost
        color="rgba(255, 255, 255, 0.2)"
        text-color="#BFBAB8"
        class="!w-[50%]"
        @click="onClose"
      >
        {{ btns.closeBtn }}
      </n-button>
      <n-button
        v-if="btns.confirmBtn"
        color="#FFE100"
        text-color="#000"
        class="gradient-color !ml-5 !w-[50%]"
        @click="onConfirm"
      >
        {{ btns.confirmBtn }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const dialogRef = inject('dialogRef') as UseDialogOptions;
type BtnsT = {
  closeBtn?: String;
  confirmBtn?: String;
};
const props = withDefaults(
  defineProps<{
    btns?: BtnsT;
    message: string;
    time: number;
    type: string;
    contentClass?: string;
    autoClose?: boolean;
  }>(),
  {
    btns: undefined,
    message: '',
    time: 3000,
    type: 'default',
    contentClass: '',
    autoClose: true,
  },
);
const iconfont = {
  error: {
    class: 'text-[#ffbaba] bg-[#fe1212]',
    html: 'toastError',
  },
  success: {
    class: 'text-[#b9e8b0] bg-[#149110]',
    html: 'toastSuccess',
  },
  warning: {
    class: 'text-[#ffd8af] bg-[#f85100]',
    html: 'toastWarning',
  },
};
const icon = computed(() => iconfont[props.type as keyof typeof iconfont]);
const timeout = ref<number>(props.time);
let timer: any = 0;
const countdown = () => {
  if (timeout.value <= 0) {
    dialogRef?.destroy();
  } else {
    timeout.value -= 1000;
    timer = setTimeout(countdown, 1000);
  }
};
if (props.autoClose) {
  countdown();
}
const onClose = async () => {
  await dialogRef.onClose?.();
  dialogRef?.destroy();
};
const onConfirm = () => {
  dialogRef.onConfirm?.('');
};
onUnmounted(() => {
  clearTimeout(timer);
});
</script>
