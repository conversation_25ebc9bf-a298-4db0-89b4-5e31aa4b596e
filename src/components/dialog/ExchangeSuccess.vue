<template>
  <div>
    <h2 class="text-[24px] text-center font-semibold uppercase mb-[24px]">
      {{ $t('exchange_successful') }}
    </h2>
    <p class="mb-[16px] text-[16px]">{{ $t('congratulations_obtain') }}</p>
    <div
      v-if="coinAmount"
      class="flex items-center gap-[6px] px-[16px] py-[8px] bg-[#20263b] rounded-md"
    >
      <BaseIcon name="gold" class="w-[24px] h-[24px]" filled></BaseIcon>
      <span class="text-[#fff] text-[16px] font-medium">
        {{ coinAmount }}
      </span>
    </div>
    <div v-if="goodsList.length" class="flex flex-col gap-[8px] mt-[8px]">
      <div
        v-for="item in goodsList"
        :key="item.goods_id"
        class="px-[12px] py-[8px] rounded-lg bg-[#20263b]"
      >
        <GoodsCard
          :tags="item?.tags"
          :name="item.market_hash_name"
          :cover="item.icon_url"
        />
      </div>
    </div>
    <div class="flex justify-center">
      <MainButton class="uppercase mt-[24px]" @click="onConfirm">
        {{ $t('confirm') }}
      </MainButton>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  goodsList: any;
  coinAmount: String;
  onConfirm: () => void;
}>();
</script>
