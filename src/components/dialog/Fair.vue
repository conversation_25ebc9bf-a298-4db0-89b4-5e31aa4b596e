<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="text-primary-text pt-6 required:">
    <div
      class="text-white font-bold text-2xl text-center absolute top-[-24px] left-0 right-0"
    >
      {{ $t('provably_fair') }}
    </div>
    <div class="mb-6">
      {{ fairInfo.msg }}
    </div>
    <div class="">
      <div
        v-for="info in fairInfo.seeds"
        :key="info.title"
        class="mb-2 sm:mb-4"
      >
        <p class="text-[#7D90CA] font-bold mb-2 sm:mb-4">{{ info.title }}</p>
        <div
          class="py-2.5 px-4 bg-[#20263B] text-white rounded flex items-center"
        >
          {{ info.value }}
        </div>
      </div>
    </div>
    <div class="flex justify-end pt-2">
      <MainButton :disabled="disabled" @click="verify">
        {{ $t('verify') }}
      </MainButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { FairInfoType } from '~/types/cases';
const props = withDefaults(
  defineProps<{
    fairInfo: FairInfoType;
  }>(),
  {
    fairInfo: () => ({
      msg: '',
      seeds: [],
    }),
  },
);
const disabled = computed(() => {
  const emptyValue = props.fairInfo.seeds.filter((el) => el.value === '--');
  return emptyValue.length > 0;
});
const dialogRef = inject('dialogRef') as UseDialogOptions;
const $router = useRouter();
const verify = () => {
  dialogRef.onClose?.();
  dialogRef.destroy();
  const routeName = $router.currentRoute.value.path.split('/')[1];
  $router.push(`/fairness?tab=${routeName}`);
};
</script>
<style lang="scss" scoped></style>
