<template>
  <div>
    <div class="flex justify-center gap-2 items-center -mt-[20px]">
      <img :src="method.logo_url" class="w-[40px] h-[40px]" alt="" />
      <h2 class="text-2xl text-center">
        {{ t('withdraw') }} {{ method.name }} {{ t('deposit') }}
      </h2>
    </div>
    <p class="text-lg leading-6 my-6">
      {{
        t('enter_wallet_address', {
          a: method.key,
        })
      }}
    </p>

    <div>
      <p class="font-bold text-[#7D90CA]">
        {{
          t('receiving_crypto_address', {
            a: method.key,
          })
        }}
      </p>
      <n-form-item
        :rule="rules.address"
        :show-feedback="false"
        :show-label="false"
        class="my-4"
      >
        <ProfileInput
          v-model:value="formValue.address"
          :placeholder="$t('paste_your_withdrawal_address_here')"
        />
      </n-form-item>
    </div>
    <div>
      <p class="font-bold text-[#7D90CA]">
        {{ t('withdrawal_of_coins') }}
      </p>
      <n-form-item
        :rule="rules.coin"
        :show-feedback="false"
        :show-label="false"
        class="my-4"
      >
        <ProfileInput
          v-model:value="formValue.coin"
          :placeholder="$t('enter_withdrawal_amount')"
        >
          <template #suffix>
            <div class="text-[#F8B838]">Max</div>
          </template>
        </ProfileInput>
      </n-form-item>
    </div>

    <div>
      <p class="flex justify-between mb-lg">
        <span class="text-lg">{{ t('coin_value') }}</span>
        <ProfileNumIcon :url="method.logo_url" :num="-123123" />
      </p>
      <p class="flex justify-between my-lg">
        <span class="text-lg">{{ t('network_fee') }}</span>
        <ProfileNumIcon :url="method.logo_url" :num="-123123" />
      </p>
      <p class="flex justify-between my-lg">
        <span class="text-lg">{{ t('approximate_total_to_receive') }}</span>
        <ProfileNumIcon
          :url="method.logo_url"
          :num="-123123"
          num-color="#F8B838"
        />
      </p>
    </div>
    <div
      class="h-[1px] w-[880px] bg-[#25304F] mt-lg relative left-[-30px]"
    ></div>
    <div class="mt-lg text-[#F8B838] text-lg leading-6">
      {{ $t('withdraw_limits_description') }}
    </div>
    <div class="mt-[32px] flex justify-end">
      <MainButton>{{ t('request_withdrawal') }}</MainButton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  method: ApiV1TradePaymentMethodsGet200ResponseDataDataInner;
}

withDefaults(defineProps<Props>(), {});

const { t } = useI18n();
const formValue = ref<any>({
  address: '',
  coin: '',
});

const validateAmount = (
  field: 'coin' | 'address',
  value: string,
  min: number,
  max: number,
) => {
  if (!value) return '请输入';

  if (field === 'coin') {
    const num = Number(value);
    if (isNaN(num)) return '请输入有效数字';
    if (num < min) return `最小金额为 ${min}`;
    if (num > max) return `最大金额为 ${max}`;
    const decimals = value.split('.')[1]?.length || 0;
    if (field === 'coin' && decimals > 2) return '最多支持2位小数';
  }

  return null;
};

const createValidator = (field: 'coin' | 'address') => {
  return (_: any, __: any, callback: Function) => {
    const value = formValue.value[field];
    const min = 0;
    const max = 1000;

    const error = validateAmount(field, value, min, max);

    if (error) {
      return callback(new Error(error));
    }

    return callback();
  };
};

const rules = {
  address: {
    trigger: ['input', 'blur'],
    required: true,
    validator: createValidator('address'),
  },
  coin: [
    {
      trigger: ['input', 'blur'],
      required: true,
      validator: createValidator('coin'),
    },
  ],
};
</script>
