<template>
  <div class="pt-[34px]">
    <div class="flex items-center justify-center w-full">
      <div
        v-for="(_, index) in battle?.player_number"
        :key="index"
        class="flex"
      >
        <div
          class="flex flex-col items-center cursor-pointer"
          @click="handleSelectPosition(index)"
        >
          <div
            class="flex justify-center items-center rounded-md size-[64px] max-md:size-[52px] box-border text-[18px] font-bold text-white border-transparent"
            :class="{
              'dashed-border': !battle.players[index],
              'dashed-border-none border-1  !border-primary-400 !text-primary-400 !bg-primary-400/10':
                battle?.players[index]?.uid === String(userId) ||
                selectedPosition === index,
            }"
            :style="{
              background: battle.players[index]
                ? `url(${battle.players[index].avatar}) no-repeat center / 100%`
                : '',
            }"
          >
            <span v-if="!battle.players[index]">{{ index + 1 }}</span>
          </div>
          <div class="ellipsis h-6 text-center mt-[12px] text-white/50">
            {{ battle.players[index]?.nickname }}
          </div>
        </div>
        <svgo-battle
          v-if="battle && index !== (battle?.player_number ?? 1) - 1"
          class="mt-[11px] sm:mx-5 mx-sm mb-0 size-[40px] max-md:size-[30px] text-[#58689E]"
        />
      </div>
    </div>
    <div
      class="mt-xl px-xl flex justify-end max-md:flex-wrap max-md:justify-center max-md:flex-col-reverse gap-[16px]"
    >
      <n-button
        class="text-white btn-gradient-gray"
        size="large"
        @click="cancel"
      >
        {{ $t('cancel') }}
      </n-button>
      <AmountButton
        :amount="battle.battle_cost ?? 0"
        :text="$t('join_battle')"
        :disabled="selectedPosition === -1"
        action-class="max-md:flex-1"
        theme="primary"
        @click="confirm"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import type { BattleCase, BattleItem } from '~/types/battles';

type Battle = BattleItem & {
  cases: BattleCase[];
};

interface Props {
  battle: Battle;
}

const props = withDefaults(defineProps<Props>(), {
  battle: () => ({
    id: '',
    name: '',
    player_number: 0,
    players: [],
    cases: [],
  }),
});

const dialogRef = inject('dialogRef') as UseDialogOptions;
const userStore = useSettingStore();
const userId = computed(() => userStore.userInfo?.uid ?? '0');

const joined = computed(() =>
  props.battle.players.some((player) => player?.uid === userId.value),
);

// 选中的位置
const selectedPosition = ref(-1);

// 处理位置选择
const handleSelectPosition = (index: number) => {
  if (
    index < 0 ||
    index >= (props.battle?.player_number || 0) ||
    joined.value ||
    props.battle?.players[index]
  ) {
    return;
  }
  selectedPosition.value = index;
};

const cancel = () => {
  dialogRef?.destroy();
};

const confirm = () => {
  if (selectedPosition.value === -1) {
    return;
  }
  if (
    selectedPosition.value < 0 ||
    selectedPosition.value >= (props.battle?.player_number || 0) ||
    props.battle?.players[selectedPosition.value]
  ) {
    getAvailablePositions();
    return;
  }
  dialogRef?.onConfirm({ position: selectedPosition.value });
};

const getAvailablePositions = () => {
  if (joined.value) {
    selectedPosition.value = -1;
    return;
  }
  const allAvailablePositions = props.battle.players
    .map((player, index) => (!player ? index : -1))
    .filter((index) => index !== -1);
  if (allAvailablePositions.length > 0) {
    handleSelectPosition(allAvailablePositions[0]);
  } else {
    selectedPosition.value = -1;
  }
};

watch(
  () => props.battle.players,
  () => {
    getAvailablePositions();
  },
  {
    deep: true,
    immediate: true,
  },
);
</script>

<style scoped lang="scss">
.opacity-50 {
  opacity: 0.5;
  cursor: not-allowed;
}
.dashed-border {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(@/assets/icons/dashed.svg) no-repeat center / 100% 100%;
  }
  &.dashed-border-none {
    &::before {
      display: none;
    }
  }
}
</style>
