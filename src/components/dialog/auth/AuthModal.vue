<template>
  <div class="">
    <!-- Header -->
    <div class="flex items-center justify-center pb-[20px] border-gray-700">
      <img src="/imgs/home_logo.png" alt="LKSK" class="w-[172px]" />
    </div>
    <div>
      <!-- 标签页切换 -->
      <div
        class="flex mb-[26px] p-[2px] rounded-[30px] bg-black/50 overflow-hidden"
      >
        <button
          :class="[
            'flex-1 h-[34px] text-center font-medium transition-all duration-200 rounded-[30px]',
            activeTab === 'login' ? 'bg-[#2E3757] text-white' : 'text-purple-1',
          ]"
          @click="activeTab = 'login'"
        >
          {{ $t('password_login', 'Password Login') }}
        </button>
        <button
          :class="[
            'flex-1 h-[34px] text-center font-medium transition-all duration-200 rounded-[30px]',
            activeTab === 'register'
              ? 'bg-[#2E3757] text-white'
              : 'text-purple-1',
          ]"
          @click="activeTab = 'register'"
        >
          {{ $t('email_register', 'Email Registration') }}
        </button>
      </div>

      <!-- 登录-->
      <LoginForm
        v-if="activeTab === 'login'"
        @success="handleAuthSuccess"
        @switch-to-register="activeTab = 'register'"
      />

      <!-- 注册-->
      <RegisterForm
        v-if="activeTab === 'register'"
        @success="handleAuthSuccess"
        @switch-to-login="activeTab = 'login'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LoginForm from '@/components/auth/LoginForm.vue';
import RegisterForm from '@/components/auth/RegisterForm.vue';
import { useAppStore } from '~/stores/modules/app';

interface Props {
  defaultTab?: 'login' | 'register';
}

interface Emits {
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  defaultTab: 'login',
});

const emit = defineEmits<Emits>();

const activeTab = ref<'login' | 'register'>(props.defaultTab);

const dialog = useAppDialog();

const appStore = useAppStore();

const closeModal = () => {
  dialog?.closeAll();
};

const handleAuthSuccess = () => {
  nextTick(() => {
    appStore.globalRefreshKey++;
    emit('success');
  });
  closeModal();
};

// 监听外部切换标签页
const switchTab = (tab: 'login' | 'register') => {
  activeTab.value = tab;
};

defineExpose({
  switchTab,
});
</script>

<style scoped lang="scss">
.auth-modal {
  :deep(.n-card) {
    background: #1a1f2e;
    border: 1px solid #2d3748;
  }

  :deep(.n-card__header) {
    border-bottom: 1px solid #2d3748;
  }
}
</style>
