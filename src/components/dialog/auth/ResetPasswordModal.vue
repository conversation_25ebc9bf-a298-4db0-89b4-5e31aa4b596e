<template>
  <div>
    <!-- 标题 -->
    <div class="mb-6">
      <h2 class="text-center text-[24px] font-bold">
        {{ $t('reset_password', 'Reset password') }}
      </h2>
      <p class="text-white text-[16px] mt-2">
        {{
          $t(
            'reset_password_description',
            'If you forgot or want to reset your password, you can do so by verifying your email',
          )
        }}
      </p>
    </div>

    <!-- 重置密码表单 -->
    <ResetPasswordForm
      :email="email"
      @success="handleResetSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import ResetPasswordForm from '@/components/auth/ResetPasswordForm.vue';

interface Props {
  email?: string;
}

interface Emits {
  (e: 'success'): void;
}

withDefaults(defineProps<Props>(), {
  email: '',
});
const emit = defineEmits<Emits>();

const dialog = useAppDialog();

const closeModal = () => {
  dialog?.closeAll();
};

const handleResetSuccess = () => {
  emit('success');
  closeModal();
};

const handleCancel = () => {
  closeModal();
};
</script>

<style scoped lang="scss"></style>
