<template>
  <div>
    <div class="mb-6">
      <!-- 标题 -->
      <h2 class="text-center text-[24px] font-bold">
        {{ $t('bind_email', 'Bind email') }}
      </h2>
      <p class="text-white text-[16px] mt-2">
        {{
          $t(
            'bind_email_login',
            'Bind your email to enable email login and enhance account security',
          )
        }}
      </p>
    </div>
    <!-- 绑定邮箱表单 -->
    <BindEmailForm
      :email="email"
      @success="handleBindSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import BindEmailForm from '@/components/auth/BindEmailForm.vue';

interface Props {
  email?: string;
}

interface Emits {
  (e: 'success'): void;
}
withDefaults(defineProps<Props>(), {
  email: '',
});

const emit = defineEmits<Emits>();

const dialog = useAppDialog();

const closeModal = () => {
  dialog?.closeAll();
};

const handleBindSuccess = () => {
  emit('success');
  closeModal();
};

const handleCancel = () => {
  closeModal();
};
</script>

<style scoped lang="scss">
.bind-email-modal-content {
  color: #ffffff;
}
</style>
