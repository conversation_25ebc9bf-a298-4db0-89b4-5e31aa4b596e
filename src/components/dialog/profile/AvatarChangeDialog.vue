<template>
  <div class="flex flex-col justify-center px-2">
    <h2 class="mb-[24px] text-center text-2xl font-bold">
      {{ $t('change_avatar') }}
    </h2>
    <p class="mb-4">{{ $t('choose_avatar') }}</p>
    <div
      v-if="pageLoading"
      class="grid grid-cols-[repeat(auto-fill,70px)] gap-2"
    >
      <div
        v-for="index in 15"
        :key="index"
        class="relative rounded overflow-hidden"
      >
        <n-skeleton class="rounded" height="70px" width="70px" />
      </div>
    </div>
    <div v-else class="grid grid-cols-[repeat(auto-fill,70px)] gap-2">
      <div
        v-for="item in avatarsList"
        :key="item.avatar_url"
        class="relative rounded overflow-hidden"
        @click="chooseAvatar(item.avatar_url || '')"
      >
        <img
          :src="item.avatar_url"
          alt=""
          class="w-[70px] h-[70px] rounded block"
        />
        <div
          v-show="item.avatar_url === selectAvatar"
          class="absolute top-0 left-0 right-0 bottom-0 bg-black/70 flex justify-center items-center rounded border border-theme-color"
        >
          <BaseIcon name="checked"></BaseIcon>
        </div>
      </div>
    </div>
    <!-- 按钮 -->
    <div class="flex justify-center mt-6">
      <MainButton :loading="btnLoading" class="uppercase" @click="handleSubmit">
        {{ $t('apply') }}
      </MainButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useSettingStore } from '~/stores/modules/setting';

withDefaults(
  defineProps<{
    defaultValue?: string;
  }>(),
  {
    defaultValue: '',
  },
);
const dialogRef = inject('dialogRef') as UseDialogOptions;
const settingStore = useSettingStore();
const toast = useAppToast();
const { settingApi } = useApi();
const { t } = useI18n();
const pageLoading = ref(true);
const btnLoading = ref(false);
const avatarsList = ref<V1AvatarMaterialItem[]>([]);
const selectAvatar = ref<string>(settingStore.userInfo?.user_avatar || '');

const getAvatar = async () => {
  pageLoading.value = true;
  const { data: req } = await settingApi.avatarList();
  const data = req.value?.data;
  if (data) {
    const list = data.system_avatars || [];
    const steamAvatar = data.steam_avatars?.[0];
    if (steamAvatar) {
      list.unshift(steamAvatar);
    }
    avatarsList.value = list;
  }
  pageLoading.value = false;
};
// 修改头像
const chooseAvatar = (avatar: string) => {
  selectAvatar.value = avatar;
};

// 保存头像
const handleSubmit = async () => {
  btnLoading.value = true;
  const { data: req } = await settingApi.setAvatar(selectAvatar.value);
  const data = req.value;
  if (data.data) {
    settingStore.userInfo!.user_avatar = selectAvatar.value;
    dialogRef.destroy();
    toast.success({ content: t('successfully_saved') });
  }
  btnLoading.value = false;
};
onMounted(() => {
  getAvatar();
});
</script>
<style lang="scss" scoped></style>
