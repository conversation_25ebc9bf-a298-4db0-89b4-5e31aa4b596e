<template>
  <div class="flex flex-col justify-center">
    <div
      v-if="$slots.icon"
      class="flex items-center justify-center mt-[-20px] mb-[24px]"
    >
      <slot name="icon"></slot>
    </div>
    <h2 v-if="title" class="mb-[24px] text-center text-2xl font-bold">
      {{ title }}
    </h2>
    <p v-if="description" class="text-light-1 mb-lg text-lg">
      {{ description }}
    </p>

    <div v-if="$slots.content"><slot name="content"></slot></div>
    <div>
      <slot name="form">
        <ProfileInput
          v-if="showDefaultInput"
          v-model:value="inputValue"
          class="mb-[40px]"
          :status="error ? 'error' : 'success'"
          :placeholder="inputPlaceholder ? inputPlaceholder : ''"
          :maxlength="maxlength"
          @input="handleInputChange"
        />
      </slot>
    </div>

    <!-- 按钮 -->
    <slot name="action">
      <div class="flex justify-center">
        <MainButton
          :disabled="inputValue === ''"
          :loading="loading"
          class="uppercase"
          @click="handleSubmit"
        >
          {{ submitText && $t(submitText) }}
        </MainButton>
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  description?: string;
  submitText?: string;
  maxlength?: number | undefined;
  inputValidate?: boolean;
  validateFn?: (value?: string) => boolean | Promise<boolean>;
  submitFn: (value?: string) => Promise<any>;
  onInput?: (value: string, newValue?: (newValue: string) => void) => void;
  showDefaultInput?: boolean;
  defaultValue?: string;
  inputPlaceholder?: string;
  onSubmit?: (data: any) => void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  description: '',
  submitText: 'apply',
  maxlength: undefined,
  validateFn: (_value?: string) => true,
  inputValidate: false,
  showDefaultInput: false,
  defaultValue: '',
  inputPlaceholder: '',
  onSubmit: undefined,
  submitFn: async () => {},
  onInput: async () => {},
});

const inputValue = ref(props.defaultValue);
const loading = ref(false);
const error = ref(false);

const handleSubmit = async () => {
  try {
    // 验证
    error.value = false;
    const isValid = await props.validateFn(inputValue.value);
    if (!isValid) {
      error.value = true;
      return;
    }
    loading.value = true;
    // 提交
    const res = await props.submitFn(
      props.showDefaultInput ? inputValue.value : undefined,
    );
    if (res.data.value.code === 0) {
      props.onSubmit?.(res.data.value?.data);
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const handleInputChange = async (value: string) => {
  props.onInput?.(value, (newValue: string) => {
    inputValue.value = newValue;
  });
  if (!props.inputValidate) return;
  error.value = false;
  const isValid = await props.validateFn(value);
  if (!isValid) {
    error.value = true;
  }
};

defineExpose({
  handleSubmit,
  inputValue,
});
</script>
