<template>
  <div>
    <h2 class="text-[24px] text-center font-semibold uppercase mb-9">
      {{ $t('gift_redeem') }}
    </h2>
    <p class="text-[#7D90CA] font-semibold">{{ $t('redeem_code') }}</p>
    <ProfileInput
      v-model:value="code"
      class="my-[16px]"
      type="text"
      :placeholder="$t('redeem_placeholder')"
    ></ProfileInput>
    <div class="min-h-[72px] mb-[16px]">
      <CloudflareTurnstile
        ref="turnstileRef"
        size="flexible"
        retry="auto"
        :auto-execute="true"
        :site-key="config.public.TURNSTILE_SITE_KEY"
        @verified="onVerified"
      />
    </div>
    <div>{{ $t('code_instruction') }}</div>
    <div class="flex justify-center">
      <MainButton
        class="uppercase mt-[24px]"
        :disabled="disabled"
        :loading="loading"
        @click="onSubmit"
      >
        {{ $t('confirm_exchange') }}
      </MainButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  onConfirm: (params: { content: String; cf_token: String }) => Promise<void>;
}>();
const config = useRuntimeConfig();
const turnstileRef = ref();
const code = ref('');
const cfToken = ref('');
const loading = ref(false);

const onVerified = (token: string) => {
  cfToken.value = token;
};

const disabled = computed(() => {
  if (code.value && cfToken.value) {
    return false;
  }
  return true;
});

const onSubmit = async () => {
  loading.value = true;
  await props.onConfirm({
    content: code.value,
    cf_token: cfToken.value,
  });
  loading.value = false;
};
</script>
<style lang="scss" scoped></style>
