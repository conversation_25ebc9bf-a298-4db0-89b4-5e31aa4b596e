<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="text-white pb-3">
    <div class="font-bold text-center text-[24px]">
      <template v-if="typeof title === 'string'">{{ title }}</template>
      <template v-else><component :is="title"></component></template>
    </div>
    <div
      class="py-7 text-[16px]"
      :class="{ 'text-center': typeof content === 'string' }"
    >
      <template v-if="typeof content === 'string'">{{ content }}</template>
      <template v-else><component :is="content"></component></template>
    </div>
    <div class="flex gap-x-4 justify-center">
      <n-button
        v-if="!hideCancelBtn"
        class="rounded-[4px] min-w-[128px] h-[42px] font-bold uppercase"
        :class="buttonClass"
        :color="cancelBg || '#2E3757'"
        :text-color="cancelColor || '#FFFFFF'"
        :ghost="cancelGhost"
        @click="cancel"
      >
        <template v-if="cancelIcon" #icon>
          <component :is="cancelIcon" class="mb-0"></component>
        </template>
        {{ cancelText || $t('cancel') }}
      </n-button>
      <template v-if="!hideConfirmBtn">
        <n-button
          v-if="confirmBg || confirmColor"
          class="rounded-[4px] min-w-[128px] h-[42px] font-bold uppercase"
          :class="buttonClass"
          :color="confirmBg || '#419FFF'"
          :text-color="confirmColor || '#FFFFFF'"
          @click="confirm"
        >
          <template v-if="confirmIcon" #icon>
            <component :is="confirmIcon" class="mb-0"></component>
          </template>
          {{ confirmText || $t('confirm') }}
        </n-button>
        <MainButton
          v-else
          class="uppercase"
          :class="buttonClass"
          @click="confirm"
        >
          <template v-if="confirmIcon" #icon>
            <component :is="confirmIcon" class="mb-0"></component>
          </template>
          {{ confirmText || $t('confirm') }}
        </MainButton>
      </template>
    </div>
    <div v-if="footer" class="text-center">
      <template v-if="typeof footer === 'string'">{{ footer }}</template>
      <template v-else><component :is="footer"></component></template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { VNodeChild } from 'vue';
const dialogRef = inject('dialogRef') as UseDialogOptions;
withDefaults(
  defineProps<{
    confirmText?: string;
    confirmIcon?: Component;
    confirmBg?: string;
    confirmColor?: string;
    cancelText?: string;
    cancelIcon?: Component;
    cancelBg?: string;
    cancelColor?: string;
    cancelGhost?: boolean;
    hideConfirmBtn?: boolean;
    hideCancelBtn?: boolean;
    content: string | VNodeChild;
    title?: string | VNodeChild;
    footer?: string | VNodeChild;
    buttonClass?: string;
  }>(),
  {
    confirmText: undefined,
    confirmIcon: undefined,
    confirmBg: undefined,
    confirmColor: undefined,
    cancelText: undefined,
    cancelIcon: undefined,
    cancelGhost: false,
    cancelBg: undefined,
    cancelColor: undefined,
    hideConfirmBtn: false,
    hideCancelBtn: false,
    content: '',
    title: '',
    footer: '',
    buttonClass: '',
  },
);
const $emit = defineEmits(['confirm', 'cancel']);
const confirm = () => {
  $emit('confirm');
};
const cancel = () => {
  $emit('cancel');
  dialogRef.onClose?.();
  dialogRef.destroy();
};
</script>
<style lang="scss" scoped></style>
