<template>
  <div class="payment-provider-selector pb-[4px]">
    <!-- 标题 -->
    <div class="flex justify-center gap-[12px] items-center mb-7">
      <img
        :src="method.logo_url"
        class="w-[54px] h-[34px] object-contain"
        alt=""
      />
      <h2 class="text-[24px] text-center font-semibold">
        {{ $t('select_payment_provider', 'Select Payment Provider') }}
      </h2>
    </div>
    <!-- 支付渠道列表 -->
    <div class="space-y-[8px] px-[2px]">
      <!-- 空状态 -->
      <div v-if="providers.length === 0" class="text-center py-8">
        <BaseIcon name="wallet" class="text-4xl mb-2" />
        <p>
          {{ $t('no_providers_available', 'No payment providers available') }}
        </p>
      </div>

      <!-- 渠道列表 -->
      <div
        v-for="provider in providers"
        :key="provider.key"
        class="flex items-center gap-[14px] p-[10px] rounded cursor-pointer transition-all duration-200 ease-in-out bg-[#1C243A] border border-transparent hover:border-[#F8B838] hover:text-[#F8B838] hover:bg-[#F8B838]/10"
        :class="{
          selected: selectedProvider?.key === provider.key,
          'opacity-50 cursor-not-allowed': provider.is_under_maintenance,
        }"
        @click="selectProvider(provider)"
      >
        <!-- 左侧图标区域 -->
        <div class="flex-shrink-0">
          <img
            v-if="provider.logo_url"
            :src="provider.logo_url"
            :alt="provider.name"
            class="w-[50px] h-[50px] object-contain rounded"
            @error="handleImageError"
          />
          <!-- 默认图标占位符 -->
          <div
            v-else
            class="w-[50px] h-[50px] bg-gray-600 rounded flex items-center justify-center"
          ></div>
        </div>

        <!-- 中间内容区域 -->
        <div class="flex-1 min-w-0">
          <div class="text-base font-medium mb-[7px] truncate">
            {{ provider.name }}
          </div>
          <div class="provider-fee" :class="getFeeColorClass(provider)">
            {{ formatFee(provider) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ProcessedProvider {
  key: string;
  name: string;
  logo_url?: string;
  mode?: number;
  configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration;
  fee_percentage: number;
  is_under_maintenance: boolean;
}

interface Props {
  method: ApiBffInterfaceV1PaymentMethodsData;
}

const props = withDefaults(defineProps<Props>(), {
  method: () => ({}) as ApiBffInterfaceV1PaymentMethodsData,
});

const dialogRef = inject('dialogRef') as UseDialogOptions;

const providers = computed<ProcessedProvider[]>(() => {
  if (!props.method?.configurations?.length) {
    return [];
  }

  return props.method.configurations.map((config): ProcessedProvider => {
    const provider = config.provider || {};
    const feePercentage = parseFloat(
      String((config as any).fee_percentage || '0'),
    );

    return {
      key: provider.key || String(config.id || ''),
      name:
        provider.name ||
        (config as any).provider_name_override ||
        'Unknown Provider',
      logo_url: provider.logo_url,
      mode: (config as any).mode,
      configuration: config,
      fee_percentage: feePercentage,
      is_under_maintenance: Boolean((config as any).is_under_maintenance),
    };
  });
});

const selectedProvider = ref<ProcessedProvider | null>(null);

const selectProvider = (provider: ProcessedProvider) => {
  selectedProvider.value = provider;
  dialogRef?.onConfirm?.(provider);
};

const formatNumber = (value: number): string => {
  return value % 1 === 0
    ? value.toString()
    : parseFloat(value.toFixed(2)).toString();
};

const formatFee = (provider: ProcessedProvider): string => {
  if (provider.fee_percentage === 0) {
    return '0% FEE';
  }

  const percentage = provider.fee_percentage * 100;
  return `${formatNumber(percentage)}% FEE`;
};

const getFeeColorClass = (provider: ProcessedProvider): string => {
  return provider.fee_percentage === 0 ? 'text-green-1' : 'text-[#7D90CA]';
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
};
</script>
