<template>
  <div>
    <div class="flex justify-center gap-[12px] items-center mb-8">
      <img
        :src="method.logo_url"
        class="w-[54px] h-[34px] object-contain"
        alt=""
      />
      <h2 class="text-[24px] text-center font-semibold uppercase">
        {{ method.name }} {{ t('recharge', 'Recharge') }}
      </h2>
    </div>
    <p class="mb-lg text-lg">
      {{
        $t(
          'gift_card_instruction',
          'Please enter the gift card code below and then click \"Redeem\".',
        )
      }}
    </p>
    <div class="my-lg flex items-center gap-md">
      <ProfileInput
        v-model:value="giftCode"
        type="text"
        :placeholder="$t('gift_card_code_placeholder', 'Enter gift card code')"
      ></ProfileInput>
      <MainButton
        class="uppercase"
        :loading="loading"
        :disabled="!giftCode"
        @click="handleRedeemGiftCode"
      >
        {{ $t('redeem', 'redeem') }}
      </MainButton>
    </div>
    <p class="mb-9 text-theme-color text-lg">
      {{
        $t(
          'gift_card_buy_prompt',
          "Don't have a gift card? Click below to buy one.",
        )
      }}
    </p>
    <MainButton v-if="url" class="ml-auto uppercase" @click="jumpToBuyGiftCard">
      {{ $t('buy_gift_card_btn', 'GO TO BUY A GIFT CARD') }}
    </MainButton>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
interface Props {
  method: ApiBffInterfaceV1PaymentMethodsData;
}
const props = withDefaults(defineProps<Props>(), {});

const settingStore = useSettingStore();
const { depositApi } = useApi();
const { t } = useI18n();
const toast = useAppToast();
const giftCode = ref('');
const loading = ref(false);

const url = computed(() => {
  return props.method?.configurations?.[0]?.provider_name_override || '';
});

const handleRedeemGiftCode = async () => {
  if (!giftCode.value) {
    return;
  }
  loading.value = true;
  try {
    const { data: res } = await depositApi.redeemGiftCode({
      gift_code: giftCode.value,
    });
    if (res.value.code === 0) {
      toast.success({
        content: t('success'),
      });
      settingStore.getUserInfo();
    }
  } finally {
    loading.value = false;
  }
};
const jumpToBuyGiftCard = () => {
  if (url.value) {
    window.open(url.value, '_blank');
  }
};
</script>
