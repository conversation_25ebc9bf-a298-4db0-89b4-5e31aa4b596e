<template>
  <div
    class="relative h-full flex max-lg:flex-col gap-[62px] text-white min-h-[600px]"
  >
    <div
      class="max-lg:hidden absolute w-[1px] bg-dark-2 top-[-54px] left-[50%] -translate-x-[50%]"
    ></div>
    <!-- 左侧：金币选择和费用计算 -->
    <div class="flex-1">
      <!-- 标题 -->
      <div class="text-lg">
        {{
          $t(
            'enter_amount_prompt',
            'Please enter the amount of Coins you would like to purchase below.',
          )
        }}
      </div>

      <!-- 金额输入 -->
      <div class="my-lg">
        <DepositAmountInput
          ref="depositAmountInputRef"
          :deposit-amount="depositAmount"
          :deposit-quick-options="depositQuickOptions"
          :rules="amountRules"
          :only-allow-number="onlyAllowNumber"
          :input-theme="inputTheme"
          :placeholder="
            $t(
              'coin_deposit_placeholder',
              'Enter the amount of coins to top up',
            )
          "
          @amount-input="handleAmountInput"
          @quick-option-click="handleQuickOptionClick"
        />
      </div>

      <!-- 费用明细 -->
      <DepositSummary
        :base-amount="baseAmount"
        :fee-amount="feeAmount"
        :total-amount="totalAmount"
      />
    </div>

    <!-- 右侧：支付信息表单 -->
    <div class="flex-1 mb-[14px]">
      <!-- AML 提示信息 -->
      <div class="text-lg text-theme-color mb-[14px]">
        {{
          $t(
            'aml_notice',
            'As required by AML regulations, we need your details for this payment verification only. No information will be stored.',
          )
        }}
      </div>

      <!-- 支付表单 -->
      <n-form
        ref="formRef"
        class="space-y-[12px]"
        :model="formData"
        :rules="rules"
        :show-feedback="false"
      >
        <!-- 邮箱 -->
        <n-form-item path="email" :show-label="false">
          <div class="w-full">
            <label class="block text-purple-1 font-semibold mb-[12px]">
              {{ $t('email', 'Email') }}
            </label>
            <n-input
              v-model:value="formData.email"
              :placeholder="$t('enter_your_email', 'Enter your email')"
              class="h-12"
              :theme-overrides="inputTheme"
            />
          </div>
        </n-form-item>

        <!-- 手机号码 -->
        <!-- <n-form-item  :show-label="false"> -->
        <div class="w-full">
          <label class="block text-purple-1 font-semibold mb-[12px]">
            {{ $t('phone_number', 'Phone number') }}
          </label>
          <div class="flex gap-2">
            <n-form-item path="countryCode" :show-label="false" class="flex-1">
              <n-select
                v-model:value="formData.countryCode"
                :options="countryCodeOptions"
                :theme-overrides="selectTheme"
                filterable
                :render-label="renderLabel"
                :placeholder="$t('select_country_code', 'Select country code')"
              />
            </n-form-item>
            <n-form-item path="phone" :show-label="false" class="flex-1">
              <n-input
                v-model:value="formData.phone"
                :placeholder="$t('enter_phone', 'Enter phone number')"
                class="flex-1 h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
          </div>
        </div>
        <!-- </n-form-item> -->

        <!-- 银行卡号 -->
        <n-form-item path="cardNumber" :show-label="false">
          <div class="w-full">
            <label class="block text-purple-1 font-semibold mb-[12px]">
              {{ $t('bank_card_number', 'Bank Card Number') }}
            </label>
            <n-input
              :value="formData.cardNumber"
              placeholder="1234 1234 1234 1234"
              class="h-12"
              :theme-overrides="inputTheme"
              @input="formatCardNumber"
            >
              <template #suffix>
                <div class="flex gap-1">
                  <base-icon name="visa" class="w-[29px] h-[20px]" filled />
                  <base-icon name="master" class="w-[29px] h-[20px]" filled />
                  <!-- <base-icon name="jcb" class="w-6 h-4" filled /> -->
                </div>
              </template>
            </n-input>
          </div>
        </n-form-item>

        <!-- 有效期和CVV -->
        <div class="grid grid-cols-2 gap-4">
          <n-form-item path="expiryDate" :show-label="false">
            <div class="w-full">
              <n-input
                :value="formData.expiryDate"
                :placeholder="$t('expiry_date', 'Expiry Date') + ': (MM/YY)'"
                class="h-12"
                :theme-overrides="inputTheme"
                @input="formatExpiryDate"
              />
            </div>
          </n-form-item>
          <n-form-item path="cvv" :show-label="false">
            <div class="w-full">
              <n-input
                v-model:value="formData.cvv"
                placeholder="CVV/CVC"
                class="h-12"
                :theme-overrides="inputTheme"
                type="password"
                show-password-on="click"
              />
            </div>
          </n-form-item>
        </div>

        <!-- 持卡人信息 -->
        <div class="space-y-4">
          <label class="block text-purple-1 font-semibold">
            {{ $t('cardholder_information', 'Cardholder Information') }}
          </label>

          <div class="grid grid-cols-2 gap-4">
            <n-form-item path="lastName" :show-label="false">
              <n-input
                v-model:value="formData.lastName"
                :placeholder="$t('last_name', 'Last Name')"
                class="h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
            <n-form-item path="firstName" :show-label="false">
              <n-input
                v-model:value="formData.firstName"
                :placeholder="$t('first_name', 'First Name')"
                class="h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <n-form-item path="country" :show-label="false">
              <n-select
                v-model:value="formData.country"
                :options="countryOptions"
                :placeholder="$t('select_country', 'Select country')"
                :theme-overrides="selectTheme"
                :consistent-menu-width="false"
              />
            </n-form-item>
            <n-form-item path="state" :show-label="false">
              <n-input
                v-model:value="formData.state"
                :placeholder="$t('state_province', 'State/Province')"
                class="h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <n-form-item path="city" :show-label="false">
              <n-input
                v-model:value="formData.city"
                :placeholder="$t('city', 'City')"
                class="h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
            <n-form-item path="zipCode" :show-label="false">
              <n-input
                v-model:value="formData.zipCode"
                :placeholder="$t('zip_postal_code', 'Zip / Postal Code')"
                class="h-12"
                :theme-overrides="inputTheme"
              />
            </n-form-item>
          </div>

          <n-form-item path="address" :show-label="false">
            <n-input
              v-model:value="formData.address"
              :placeholder="
                $t('address_additional', 'Address (Additional information)')
              "
              class="h-12"
              :theme-overrides="inputTheme"
            />
          </n-form-item>
        </div>

        <!-- 购买按钮 -->
        <div class="!mt-[16px] flex justify-end">
          <n-button
            class="btn-gradient-primary min-w-[214px] h-12 text-lg font-medium"
            :loading="loading"
            @click="handlePurchase"
          >
            {{
              $t(
                'buy_coins_for',
                { a: depositAmount, b: totalAmount },
                'BUY {a} COINS FOR ${b}',
              )
            }}
          </n-button>
        </div>
      </n-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import allCountries, { getCountryTranslation } from '~/constants/country';
import { useSettingStore } from '~/stores/modules/setting';
import { useCountryLocale } from '~/composables/useCountryLocale';
import { useAppStore } from '~/stores/modules/app';
import DepositAmountInput from '~/components/deposit/DepositAmountInput.vue';
import DepositSummary from '~/components/deposit/DepositSummary.vue';
import { useDepositCalculation } from '~/composables/useDepositCalculation';
import { useDepositAmount } from '~/composables/useDepositAmount';
import { useDepositPolling } from '~/composables/useDepositPolling';

interface Props {
  method: ApiBffInterfaceV1PaymentMethodsData;
  configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration;
  freeConfig: any;
}

interface FormData {
  email: string;
  countryCode?: string;
  phone: string;
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  lastName: string;
  firstName: string;
  country?: string;
  state: string;
  city: string;
  zipCode: string;
  address: string;
}

const inputTheme = {
  placeholderColor: '#4B5679',
  border: 'none',
  color: '#20263B',
};

const selectTheme = {
  peers: {
    InternalSelection: {
      color: '#20263B',
      colorActive: '#20263B',
      border: '1px solid #4A5568',
      borderFocus: '1px solid #F8B838',
      borderHover: '1px solid #718096',
      textColor: '#FFFFFF',
      placeholderColor: '#4B5679',
      colorActiveError: '#20263B',
    },
  },
};

const props = withDefaults(defineProps<Props>(), {});

// 使用国家语言包
const { loadCountryLocale, getCountryName, getCountryOptions } =
  useCountryLocale();

const formRef = ref();
const depositAmountInputRef = ref<any>(null);
const localeUpdateKey = ref(0); // 强制更新

// 表单数据
const formData = reactive<FormData>({
  email: '',
  phone: '',
  cardNumber: '',
  expiryDate: '',
  cvv: '',
  lastName: '',
  firstName: '',
  state: '',
  city: '',
  zipCode: '',
  address: '',
});

const { depositApi } = useApi();
const { locale } = useI18n();
const settingStore = useSettingStore();
const appStore = useAppStore();
const { exchangeRateMap } = storeToRefs(appStore);

const exchangeRate = computed(() => {
  return exchangeRateMap.value.get('USD');
});

// 限制的输入数字
const minAmount = ref(1);
const maxAmount = ref(999999);

const freeConfig = computed(() => {
  if (props.freeConfig) {
    return props.freeConfig;
  }
  return {
    fee_percentage: parseFloat(props.configuration?.fee_percentage || '0'),
    fee_minimum: parseFloat(props.configuration?.fee_minimum || '0'),
    currency: props.configuration?.currency || 'USD',
  };
});

const { baseAmount, feeAmount, totalAmount, updateTotalAmount } =
  useDepositCalculation({
    freeConfig,
    exchangeRate,
  });

const {
  depositAmount,
  depositQuickOptions,
  rules: amountRules,
  onlyAllowNumber,
  handleAmountInput: handleAmountInputLogic,
  handleQuickOptionClick,
  setInitialValue,
} = useDepositAmount({
  initialAmount: '100',
  minAmount: minAmount.value,
  maxAmount: maxAmount.value,
  onAmountChange: updateTotalAmount,
});

const { loading, startDepositProcess, stopDepositProcess } =
  useDepositPolling();

// 国家选项
const countryOptions = computed(() => {
  // eslint-disable-next-line no-unused-expressions
  localeUpdateKey.value;
  return getCountryOptions(locale.value);
});

const countryCodeOptions = computed(() => {
  const currentLocale = locale.value;
  // eslint-disable-next-line no-unused-expressions
  localeUpdateKey.value;

  const options = allCountries
    .map((country) => {
      try {
        let countryName = getCountryName(country.iso2, currentLocale);
        if (countryName === 'AC') {
          countryName = getCountryTranslation('AC', currentLocale);
        }
        const uniqueValue = `${country.iso2}-${country.dialCode}`;
        return {
          label: `+${country.dialCode} ${countryName}`,
          value: uniqueValue,
          dialCode: country.dialCode,
          country: countryName,
          iso2: country.iso2,
          priority: country.priority || 0,
        };
      } catch (e) {
        return null;
      }
    })
    .filter((option): option is NonNullable<typeof option> => option !== null);

  return options;
});

// 获取选中的国家代码信息
const getSelectedCountryCodeInfo = computed(() => {
  if (!formData.countryCode) return null;

  const selectedOption = countryCodeOptions.value.find(
    (option) => option.value === formData.countryCode,
  );

  return selectedOption || null;
});

// 金额验证
const validateAmount = (_field: 'amount', value: string) => {
  if (!value) return '请输入金额';
  if (value.length > 1 && value.startsWith('0') && !value.startsWith('0.')) {
    return '请输入有效数字格式';
  }
  const num = Number(value);
  if (isNaN(num)) return '请输入有效数字';
  if (num < 1) return '最小金额为 1';
  if (num > 999999) return '最大金额为 999999';

  return null;
};

const createAmountValidator = (field: 'amount') => {
  return (_: any, __: any, callback: Function) => {
    const value = depositAmount.value;
    const error = validateAmount(field, value);
    if (error) {
      return callback(new Error(error));
    }
    return callback();
  };
};

// 表单验证规则
const rules = {
  depositAmount: [
    {
      trigger: ['input', 'blur'],
      required: true,
      validator: createAmountValidator('amount'),
    },
  ],
  email: {
    required: true,
    message: 'Please enter your email',
    trigger: ['blur', 'input'],
  },
  countryCode: {
    required: true,
    message: 'Please select country code',
    trigger: ['blur', 'change'],
  },
  phone: {
    required: true,
    message: 'Please enter your phone number',
    trigger: ['blur', 'input'],
    validator: (_rule: any, value: string) => {
      if (!value) return new Error('Please enter your phone number');
      if (!/^\d+$/.test(value))
        return new Error('Please enter a valid phone number');
      return true;
    },
  },
  cardNumber: {
    required: true,
    message: 'Please enter your card number',
    trigger: ['blur', 'input'],
  },
  expiryDate: {
    required: true,
    message: 'Please enter expiry date',
    trigger: ['blur', 'input'],
  },
  cvv: {
    required: true,
    message: 'Please enter CVV',
    trigger: ['blur', 'input'],
  },
  lastName: {
    required: true,
    message: 'Please enter last name',
    trigger: ['blur', 'input'],
  },
  firstName: {
    required: true,
    message: 'Please enter first name',
    trigger: ['blur', 'input'],
  },
  country: {
    required: true,
    message: 'Please select country',
    trigger: ['blur', 'change'],
  },
  state: {
    required: true,
    message: 'Please enter state',
    trigger: ['blur', 'input'],
  },
  city: {
    required: true,
    message: 'Please enter city',
    trigger: ['blur', 'input'],
  },
  zipCode: {
    required: true,
    message: 'Please enter zip code',
    trigger: ['blur', 'input'],
  },
  address: {
    required: true,
    message: 'Please enter address',
    trigger: ['blur', 'input'],
  },
};

const renderLabel = (option: any) => {
  return h(
    'span',
    {
      class: 'w-full flex items-center justify-start gap-1',
    },
    {
      default: () => `${option.label}`,
    },
  );
};

// 处理金额输入事件
const handleAmountInput = (value: string) => {
  handleAmountInputLogic(value);
};

const formatCardNumber = (value: string) => {
  // 移除所有非数字字符
  const numbersOnly = value.replace(/[^0-9]/g, '');

  // 限制最大长度为16位
  const truncated = numbersOnly.substring(0, 16);

  // 添加空格
  const formatted = truncated.replace(/(\d{4})(?=\d)/g, '$1 ');

  formData.cardNumber = formatted;
};

const formatExpiryDate = (value: string) => {
  let cleaned = value.replace(/[^0-9/]/g, '');
  const slashIndex = cleaned.indexOf('/');
  if (slashIndex !== -1) {
    cleaned =
      cleaned.substring(0, slashIndex + 1) +
      cleaned.substring(slashIndex + 1).replace(/\//g, '');
  }
  const numbersOnly = cleaned.replace(/\//g, '');
  if (numbersOnly.length === 0) {
    formData.expiryDate = '';
  } else if (numbersOnly.length === 1) {
    formData.expiryDate = numbersOnly;
  } else if (numbersOnly.length === 2) {
    if (cleaned.includes('/')) {
      formData.expiryDate = cleaned;
    } else {
      formData.expiryDate = numbersOnly;
    }
  } else {
    // 超过2位数字时确保有斜杠
    formData.expiryDate =
      numbersOnly.substring(0, 2) + '/' + numbersOnly.substring(2, 4);
  }
};

const handlePurchase = async () => {
  try {
    loading.value = true;

    // 验证金额
    await depositAmountInputRef.value?.validate();
    // 验证表单
    await formRef.value?.validate();

    // 从选中的国家代码信息中获取实际的区号
    const selectedCountryInfo = getSelectedCountryCodeInfo.value;
    const actualDialCode = selectedCountryInfo?.dialCode || '';
    const res = await depositApi.createDepositGateway({
      uid: settingStore.userInfo?.uid || '',
      currency: 'USD',
      token_amount: depositAmount.value,
      payment_method: props.method.key || '',
      provider: props.configuration.provider?.key || '',
      recharge_fee_in_yuan: feeAmount.value,
      recharge_amount_in_yuan: baseAmount.value + '',
      card_number: formData.cardNumber.replace(/\s/g, ''),
      card_month: formData.expiryDate.split('/')[0],
      card_year: formData.expiryDate.split('/')[1],
      card_security_code: formData.cvv,
      last_name: formData.lastName,
      first_name: formData.firstName,
      payers_country: getCountryName(formData.country ?? '', 'en'),
      payers_state: formData.state,
      payers_city: formData.city,
      payers_zipcode: formData.zipCode,
      payers_address: formData.address,
      email: formData.email,
      payers_phone: formData.phone,
      payers_area_code: actualDialCode,
    });

    if (res.data.value.code === 0) {
      openNewPage(res.data.value.data.redirect_url!);
      const rechargeNumber = res.data.value.data.recharge_no ?? '';
      startDepositProcess(rechargeNumber);
    } else {
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
  }
};

// 监听语言变化
watch(
  locale,
  (newLocale, oldLocale) => {
    if (newLocale !== oldLocale) {
      loadCountryLocale(newLocale);
      // 强制更新computed属性
      localeUpdateKey.value++;
    }
  },
  { immediate: false },
);

onMounted(() => {
  setInitialValue('100');

  // 确保当前语言的语言包已加载
  loadCountryLocale(locale.value);
  // 强制更新computed属性
  localeUpdateKey.value++;
});

onBeforeUnmount(() => {
  stopDepositProcess();
});
</script>

<style lang="scss" scoped>
:deep(.n-base-selection .n-base-selection-overlay) {
  position: absolute !important;
}
</style>
