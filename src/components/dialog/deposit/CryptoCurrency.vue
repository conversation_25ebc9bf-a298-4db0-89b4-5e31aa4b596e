<template>
  <div>
    <div class="flex justify-center gap-2 items-center -mt-[20px]">
      <img :src="method.logo_url" class="w-[40px] h-[40px]" alt="" />
      <h2 class="text-2xl text-center">{{ method.name }} {{ t('deposit') }}</h2>
    </div>
    <p class="text-lg leading-6 mt-6 text-center">
      {{ t('deposit_receive_info', { a: method.name }) }}
    </p>
    <div class="my-xl text-center">
      <n-qr-code
        value="http://**************:9100/api/v1/user/qr_code_scan?k=afe64f3a-e66d-43b8-a62c-21626be659c1"
        :size="160"
      />
    </div>
    <div
      class="text-[#fff] font-bold rounded px-4 h-[42px] flex items-center justify-between bg-[#20263B] my-4"
      @click="copyAddress"
    >
      <div class="flex-1">{{ cryptoAddress }}</div>
      <svgo-copy class="text-[#7D90CA] w-[20px] h-[20px]"></svgo-copy>
    </div>

    <div class="mt-lg text-[#F8B838] text-lg leading-6">
      {{
        $t('verify_deposit_min', {
          a: method.name,
          b: minCryptoAmount.toString(),
        })
      }}
    </div>
    <div
      class="h-[1px] w-[880px] bg-[#25304F] mt-lg relative left-[-30px]"
    ></div>
    <div
      v-if="cryptoExchangeRateData?.rate"
      class="pt-lg flex gap-xl justify-between items-center"
    >
      <div class="flex-1">
        <h4 class="text-lg">Deposit Value in Coin</h4>
        <n-form-item
          :rule="rules.depositValueInCoin"
          :show-feedback="false"
          :show-label="false"
          class="my-4"
        >
          <n-input
            size="large"
            class="h-[42px] rounded-md flex item-center"
            :theme-overrides="{
              placeholderColor: '#4B5679',
              border: 'none',
              color: '#20263B',
            }"
            :value="depositValueInCoin"
            @update:value="handleInputCoins"
          >
            <template #prefix>
              <svgo-gold class="w-4 h-4" filled />
            </template>
          </n-input>
        </n-form-item>
      </div>
      <div class="flex-1">
        <h4 class="text-lg">
          {{ t('deposit_value_in_crypto', { a: method.name }) }}
        </h4>
        <n-form-item
          :rule="rules.depositValueInUSD"
          :show-feedback="false"
          :show-label="false"
          class="my-4"
        >
          <n-input
            size="large"
            class="h-[42px] rounded-md flex item-center"
            :theme-overrides="themeOverrides"
            :value="depositValueInUSD"
            :allow-input="onlyAllowNumber"
            @update:value="handleInputCrypto"
          >
            <template #prefix>
              <img :src="method.logo_url" class="w-4 h-4 mb-0.5" />
            </template>
          </n-input>
        </n-form-item>
      </div>
    </div>
    <p class="text-lg leading-6">
      {{ t('exchange_rate_info') }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { useDepositStore } from '~/stores/modules/deposit';

interface Props {
  method: ApiV1TradePaymentMethodsGet200ResponseDataDataInner;
}

const props = withDefaults(defineProps<Props>(), {});
const { copy } = useCopy();
const depositStore = useDepositStore();
const { t } = useI18n();
const toast = useAppToast();
const depositValueInCoin = ref<string>('0');
const depositValueInUSD = ref('0');

/// 限制的最小加密数字
const minCryptoAmount = computed(() => {
  return 1 / Math.pow(10, Number(cryptoExchangeRateData.value?.precision || 0));
});

const themeOverrides = {
  placeholderColor: '#4B5679',
  border: 'none',
  color: '#20263B',
};

const validateAmount = (field: 'coin' | 'usd', value: string, min: number) => {
  if (!value) return '请输入金额';

  const num = Number(value);
  if (isNaN(num)) return '请输入有效数字';
  if (num < min) return `最小金额为 ${min}`;

  const decimals = value.split('.')[1]?.length || 0;
  if (field === 'coin' && decimals > 2) return '最多支持2位小数';
  if (field === 'usd' && decimals > cryptoExchangeRateData.value?.precision)
    return `${cryptoExchangeRateData.value?.precision}位小数`;

  return null;
};

const createValidator = (field: 'coin' | 'usd') => {
  return (_: any, __: any, callback: Function) => {
    const value =
      field === 'coin' ? depositValueInCoin.value : depositValueInUSD.value;
    const min = field === 'coin' ? 0.01 : minCryptoAmount.value;

    const error = validateAmount(field, value, min);

    if (error) {
      return callback(new Error(error));
    }
    return callback();
  };
};

const rules = {
  depositValueInCoin: [
    {
      trigger: ['input', 'blur'],
      required: true,
      validator: createValidator('coin'),
    },
  ],
  depositValueInUSD: [
    {
      trigger: ['input', 'blur'],
      required: true,
      validator: createValidator('usd'),
    },
  ],
};

const onlyAllowNumber = (value: string) => {
  if (!value) return true;
  return /^\d+\.?\d*$/.test(value);
};

const cryptoAddress = computed(() => {
  return depositStore.cryptoDepositAddresses?.[props.method?.key];
});

const cryptoExchangeRateData = computed(() => {
  return depositStore.cryptoExchangeRateData?.[props.method?.key];
});

const convertAmount = (
  value: number,
  type: 'coinsToCrypto' | 'cryptoToCoins',
) => {
  if (!value || isNaN(value)) return '0';

  const rate = cryptoExchangeRateData.value?.rate || 0;
  const precision = cryptoExchangeRateData.value?.precision || 8;

  try {
    const result =
      type === 'coinsToCrypto'
        ? (value / rate).toFixed(precision)
        : (value * rate).toFixed(2);
    return result;
  } catch (error) {
    return '0';
  }
};

const handleInputCoins = (value: string) => {
  if (value === '') {
    depositValueInUSD.value = '';
    depositValueInCoin.value = '';
    return;
  }
  if (isNaN(Number(value))) {
    return;
  }
  depositValueInCoin.value = enforceSingleDecimalFormat(value);
  depositValueInUSD.value = convertAmount(Number(value), 'coinsToCrypto');
};

const handleInputCrypto = (value: string) => {
  if (value === '') {
    depositValueInCoin.value = '';
    depositValueInUSD.value = '';
    return;
  }
  if (isNaN(Number(value))) {
    return;
  }
  depositValueInUSD.value = enforceSingleDecimalFormat(value);
  depositValueInCoin.value = convertAmount(Number(value), 'cryptoToCoins');
};

const copyAddress = async () => {
  if (!cryptoAddress.value) {
    // toast.error({ content: t('地址不可用') });
    return;
  }

  try {
    await copy(cryptoAddress.value);
    toast.success({ content: t('replicating_success') });
  } catch (error) {
    toast.error({ content: t('copy_failed') });
  }
};

const setInitialValue = () => {
  depositValueInCoin.value = '100.00';
  handleInputCoins(depositValueInCoin.value);
};

watch(cryptoExchangeRateData, () => {
  setInitialValue();
});
</script>
