<template>
  <div>
    <div class="flex justify-center gap-[12px] items-center mb-8">
      <img
        :src="method.logo_url"
        class="w-[54px] h-[34px] object-contain"
        alt=""
      />
      <h2 class="text-[24px] text-center font-semibold">
        {{ method.name }} {{ t('deposit') }}
      </h2>
    </div>

    <p class="mb-lg text-lg">
      {{
        t(
          'enter_amount_prompt',
          'Please enter the amount of Coins you would like to purchase below.',
        )
      }}
    </p>

    <DepositAmountInput
      ref="depositAmountInputRef"
      :deposit-amount="depositAmount"
      :deposit-quick-options="depositQuickOptions"
      :rules="rules"
      :only-allow-number="onlyAllowNumber"
      :placeholder="
        $t('coin_deposit_placeholder', 'Enter the amount of coins to top up')
      "
      @amount-input="handleAmountInput"
      @quick-option-click="handleQuickOptionClick"
    />

    <DepositSummary
      :base-amount="baseAmount"
      :fee-amount="feeAmount"
      :total-amount="totalAmount"
      class="mb-lg"
    />

    <div class="mt-lg flex justify-center">
      <n-button
        class="btn-gradient-primary min-w-[128px] font-semibold"
        :loading="loading"
        @click="handleDeposit"
      >
        {{
          t(
            'buy_coins_for',
            { a: depositAmount, b: totalAmount },
            'BUY {a} COINS FOR ${b}',
          )
        }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import DepositAmountInput from '~/components/deposit/DepositAmountInput.vue';
import DepositSummary from '~/components/deposit/DepositSummary.vue';
import { useAppStore } from '~/stores/modules/app';
import { useDepositCalculation } from '~/composables/useDepositCalculation';
import { useDepositAmount } from '~/composables/useDepositAmount';
import { useDepositPolling } from '~/composables/useDepositPolling';

interface Props {
  method: ApiBffInterfaceV1PaymentMethodsData;
  configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration;
  freeConfig: any;
}

const props = withDefaults(defineProps<Props>(), {});
const appStore = useAppStore();
const { depositApi } = useApi();
const { t } = useI18n();

const depositAmountInputRef = ref<any>(null);

const { exchangeRateMap } = storeToRefs(appStore);

const freeConfig = computed(() => {
  return props.freeConfig;
});

const exchangeRate = computed(() => {
  return exchangeRateMap.value.get('USD');
});

/// 限制的输入数字
const minAmount = ref(1);
const maxAmount = ref(999999);

const { baseAmount, feeAmount, totalAmount, updateTotalAmount } =
  useDepositCalculation({
    freeConfig,
    exchangeRate,
  });

const {
  depositAmount,
  depositQuickOptions,
  rules,
  onlyAllowNumber,
  handleAmountInput: handleAmountInputLogic,
  handleQuickOptionClick,
  setInitialValue,
} = useDepositAmount({
  initialAmount: '100',
  minAmount: minAmount.value,
  maxAmount: maxAmount.value,
  onAmountChange: updateTotalAmount,
});

const { loading, startDepositProcess, stopDepositProcess } =
  useDepositPolling();

const handleAmountInput = (value: string) => {
  handleAmountInputLogic(value);
};

// 充值
const handleDeposit = async () => {
  await depositAmountInputRef.value?.validate();
  loading.value = true;

  try {
    const res = await depositApi.createDeposit({
      token_amount: Number(depositAmount.value) + '',
      payment_method: props.method.key,
      recharge_fee_in_yuan: feeAmount.value,
      recharge_amount_in_yuan: baseAmount.value + '',
      provider: props.configuration.provider?.key || '',
    });

    if (res.data.value.code === 0) {
      openNewPage(res.data.value.data.redirect_url!);
      const rechargeNumber = res.data.value.data.recharge_no ?? '';
      startDepositProcess(rechargeNumber);
    } else {
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
  }
};

watch(
  () => props.freeConfig,
  () => {
    setInitialValue('100');
  },
  { immediate: true },
);

onBeforeUnmount(() => {
  stopDepositProcess();
});
</script>
