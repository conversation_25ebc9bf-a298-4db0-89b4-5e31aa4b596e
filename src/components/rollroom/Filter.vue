<template>
  <div>
    <FilterForm
      class="flex items-center flex-wrap gap-[16px]"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <div class="flex gap-[8px] flex-wrap">
        <n-button
          v-for="item in statusArr"
          :key="item.value"
          :color="item.value === formState.status ? '#151A29' : '#151A29'"
          :text-color="item.value === formState.status ? '#F8B838' : '#7D90CA'"
          class="border-1 border-solid border-[#151A29] rounded-[4px] min-w-fit uppercase"
          :class="{ '!border-theme-color': item.value === formState.status }"
          @click="handleStatusClick(item)"
        >
          {{ $t(item.label) }}
        </n-button>
      </div>
      <div class="flex flex-wrap grow justify-between gap-[8px] sm:gap-[16px]">
        <FilterComponetsFormItem
          v-for="item in formSchema"
          :key="item.key"
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          class="max-sm:w-full"
          @update:model-value="(value) => handleUpdate(item.key, value)"
        />
      </div>
    </FilterForm>
  </div>
</template>
<script lang="ts" setup>
import { createFormSchema } from '~/models/rollroomFilterModel';
import { useSettingStore } from '~/stores/modules/setting';
const { formSchema, formState } = createFormSchema();
const settingStore = useSettingStore();
const $emit = defineEmits(['updateFilter']);
const statusArr = [
  {
    label: 'all',
    value: 'All',
  },
  {
    label: 'current',
    value: 'Current',
  },
  {
    label: 'ended',
    value: 'Ended',
  },
  {
    label: 'joined',
    value: 'Joined',
    needLogin: true,
  },
];

const handleStatusClick = (item: (typeof statusArr)[number]) => {
  if (item.needLogin && !checkLogin(false)) {
    return settingStore.signin();
  }
  handleUpdate('status', item.value);
};

const handleUpdate = (key: string, value: any) => {
  formState[key] = value;
  $emit('updateFilter', {
    ...formState,
  });
};
</script>
<style lang="scss" scoped></style>
