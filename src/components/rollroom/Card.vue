<template>
  <div
    class="card-container"
    :style="{
      backgroundImage: `url(${info.cover})`,
    }"
  >
    <div class="card-content" @click="handleRollroom">
      <div v-if="ended" class="card-mask">
        <div
          class="text-white min-w-[122px] h-[29px] text-center leading-[29px] bg-white/10 rounded-[4px] -rotate-[20deg]"
        >
          {{ $t('ended') }}
        </div>
      </div>
      <div class="flex items-center justify-between px-[16px]">
        <n-ellipsis class="font-bold text-[16px] leading-[28px]">
          {{ info.title }}
        </n-ellipsis>
        <div
          v-if="joined && !ended"
          class="text-black px-[12px] h-[28px] flex items-center bg-black/20 rounded shrink-0"
        >
          {{ $t('have_joined') }}
        </div>
      </div>
      <div class="flex justify-between px-[16px] gap-x-3 -mt-[6px]">
        <div class="grow flex flex-col justify-between mt-[14px]">
          <ClientOnly>
            <div
              v-if="!ended"
              class="bg-black/20 rounded py-[4px] px-[8px] w-[140px] text-center font-bold"
            >
              <n-countdown
                :render="renderCountDown"
                :duration="duration"
                @finish="finish"
              />
            </div>
          </ClientOnly>
        </div>
        <img
          class="w-[150px] h-[116px] object-contain"
          :src="info.goods_icon"
          alt=""
        />
      </div>
      <div class="flex items-center gap-x-[8px] -mt-[40px]">
        <div class="flex items-center p-[16px]">
          <svgo-gold class="text-[20px] mr-2 inline mb-0" filled></svgo-gold>
          <span class="text-[#1E1F1C] text-[16px] leading-none">
            {{ info.award_amount }}
          </span>
        </div>
        <RollroomJoinedPeople>
          {{ info.player_number }}
        </RollroomJoinedPeople>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { RollStatus } from '~/types/rollroom';
import rollroomDialog from '~/components/rollroom/dialog/index.vue';
const props = withDefaults(
  defineProps<{
    info: ApiV1RollroomListGet200ResponseDataListInner;
  }>(),
  {},
);
const $emit = defineEmits(['updateRoom']);
const dialog = useAppDialog();
const toast = useAppToast();
const { rollroomApi } = useApi();
const { t } = useI18n();
// 倒计时总时长,距离开始时间/距离结束时间/0
const unStart = props.info.cstatus === RollStatus.UNSTART;
const endTime = unStart ? props.info.started_at : props.info.ended_at;
// 倒计时
const { renderCountDown, duration } = useNaiveUICountdown(endTime);
// 点击join按钮
const joinLoading = ref(false);
// 已加入
const joined = computed(() => props.info.is_joined);
// 以结束
const ended = computed(() => props.info.cstatus === RollStatus.FINISH);
// 详情
const detail = computed(() => props.info);

// 更新列表数据
const updateRoom = async (id: number) => {
  const { data: req } = await rollroomApi.getRollroomDetail(id);
  const data = req.value?.data;
  return data || {};
};

// 加入游戏
const joinGame = async (cb?: () => void) => {
  if (!checkLogin()) return;
  joinLoading.value = true;
  const { data: req } = await rollroomApi.joinRollroom(props.info.id);
  const data = req.value?.data;
  if (data) {
    toast.success({ content: t('join_successfully') });
    const info = await updateRoom(props.info.id);
    $emit('updateRoom', info);
    cb?.();
  }
  joinLoading.value = false;
};

// 倒计时结束
const finish = async () => {
  if (duration.value > 0) {
    const info = await updateRoom(props.info.id);
    $emit('updateRoom', info);
  }
};
// 点击卡片
const handleRollroom = () => {
  dialog.open(rollroomDialog, {
    class:
      'max-w-[95%] w-[880px] bg-[#151A29] rounded-[8px] pb-[32px] n-dialog__close-position max-sm:px-[12px]',
    titleClass: '',
    contentProps: {
      detail,
      joinLoading,
      onJoinGame: joinGame,
      onUpdateRoom: (data: ApiV1RollroomListGet200ResponseDataListInner) => {
        $emit('updateRoom', data);
      },
    },
  });
};
</script>
<style lang="scss" scoped>
.card-container {
  @apply bg-no-repeat bg-[length:100%_100%] h-[208px] rounded-b-[8px] transition-transform duration-200 sm:hover:scale-105 pt-[46px];
}
.card-content {
  @apply rounded-[8px] cursor-pointer bg-no-repeat text-white relative bg-right-top overflow-hidden h-full pt-[12px];
}
.card-mask {
  @apply absolute bottom-0 top-0 left-0 right-0 bg-[#05060A]/60 flex justify-center items-center;
}
</style>
