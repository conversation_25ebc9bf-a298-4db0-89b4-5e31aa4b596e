<template>
  <MainButton :disabled="!activeBth || disabled" @click.stop="join">
    {{ textBtn }}
  </MainButton>
</template>
<script lang="ts" setup>
import { RollStatus } from '~/types/rollroom';
const { t } = useI18n();
const props = withDefaults(
  defineProps<{
    joined: boolean;
    cstatus: number;
    disabled?: boolean;
  }>(),
  {
    joined: false,
    disabled: false,
  },
);
const $emit = defineEmits(['join']);
const activeBth = computed(
  () => props.cstatus === RollStatus.PLAYING && !props.joined,
);
const textBtn = computed(() => {
  if (props.cstatus === RollStatus.FINISH) {
    return t('ended');
  } else if (props.cstatus === RollStatus.UNSTART) {
    return t('unstart');
  } else if (props.joined) {
    return t('have_joined');
  } else {
    return t('join');
  }
});
const join = () => {
  $emit('join');
};
</script>
<style lang="scss" scoped></style>
