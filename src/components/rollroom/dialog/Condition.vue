<template>
  <!-- Conditions -->
  <div class="mb-4">
    <h3 class="text-[#7D90CA] text-[14px] font-bold">{{ $t('conditions') }}</h3>
    <div class="bg-[#20263B] rounded mt-4 text-white">
      <template v-if="conditions.length">
        <div
          v-for="(condition, index) in conditions"
          :key="condition.key"
          class="w-full py-3 px-4 flex justify-between items-center gap-2"
          :class="{ 'border-t-1 border-solid border-[#262B3D]': index !== 0 }"
        >
          <div class="flex items-center gap-2">
            <svgo-checked
              v-if="condition.finished"
              class="shrink-0 text-[22px]"
              filled
            ></svgo-checked>
            <svgo-uncheck
              v-else
              class="shrink-0 text-[#7D90CA] text-[22px]"
            ></svgo-uncheck>
            <p>{{ desc(condition) }}</p>
          </div>
          <span class="shrink-0">
            <span :class="{ 'text-theme-color': condition.completed_num >= 1 }">
              {{ Math.min(condition.completed_num, condition.total) }}
            </span>
            <span>/{{ condition.total }}</span>
          </span>
        </div>
      </template>
      <div v-else-if="loading" class="w-full py-2 text-center">
        <n-spin size="small" class="w-full" />
      </div>
      <div v-else class="w-full py-4 text-center">
        {{ $t('join_free_competition') }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    loading: boolean;
    conditions: ApiV1RollroomListGet200ResponseDataListInnerConditionsInner[];
  }>(),
  {},
);

const { t } = useI18n();

const desc = computed(() => {
  return (
    condition: ApiV1RollroomListGet200ResponseDataListInnerConditionsInner,
  ) => {
    try {
      return (
        t(condition.key, {
          a: condition.a,
          b: condition.b,
        }) || condition.description
      );
    } catch (e) {
      return condition.description;
    }
  };
});
</script>
<style lang="scss" scoped></style>
