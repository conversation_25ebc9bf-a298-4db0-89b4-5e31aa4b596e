<template>
  <div class="relative pb-[24px]">
    <div
      class="title-bg"
      :style="{
        backgroundImage: `url(${info.popover_bg})`,
      }"
    ></div>
    <div
      class="flex justify-between items-center max-sm:flex-wrap pb-[19px] gap-y-3"
    >
      <div class="flex items-center relative gap-x-10 max-sm:gap-x-3">
        <img
          class="w-[160px] h-[120px] object-contain max-sm:w-[120px] max-sm:h-[90px]"
          :src="info.goods_icon"
          alt=""
        />
        <div class="flex flex-col gap-y-[12px] max-sm:gap-y-[4px]">
          <h2
            class="text-white text-[20px] font-bold w-max leading-[24px] max-sm:text-[16px]"
          >
            {{ info.title }}
          </h2>
          <div class="bg-black/15 text-white py-1 px-3 rounded-[4px] w-max">
            {{ $t('giveaway_id') }}:
            <span class="text-white">{{ info.id }}</span>
          </div>
          <div class="flex items-center gap-x-[16px] pt-[2px]">
            <div class="flex items-center gap-x-1.5">
              <svgo-gold
                class="text-[28px] max-sm:text-[20px]"
                filled
              ></svgo-gold>
              <span class="text-black text-[16px] max-sm:text-[14px]">
                {{ info.award_amount }}
              </span>
            </div>
            <div
              class="flex items-center gap-x-1.5 text-white bg-white/10 rounded-full px-4 py-0.5"
            >
              <svgo-user class="mb-0"></svgo-user>
              <span class="">{{ info.player_number }}</span>
            </div>
          </div>
        </div>
      </div>
      <ClientOnly>
        <div class="hidden">
          <n-countdown
            :render="renderCountDown"
            :duration="duration"
            @finish="finish"
          />
        </div>
      </ClientOnly>
      <div class="relative">
        <div class="text-[12px] text-white mb-[9px] leading-none">
          {{ $t('end_time') }}: UTC+0 {{ info.ended_at }}
        </div>
        <div class="flex gap-x-[4px]">
          <div class="countdown-item">
            <b>{{ time.d }}</b>
            <span>{{ $t('day', 2) }}</span>
          </div>
          <div class="countdown-item">
            <b>{{ time.h }}</b>
            <span>{{ $t('hour', 2) }}</span>
          </div>
          <div class="countdown-item">
            <b>{{ time.m }}</b>
            <span>{{ $t('min') }}</span>
          </div>
          <div class="countdown-item">
            <b>{{ time.s }}</b>
            <span>{{ $t('sec') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { RollStatus } from '~/types/rollroom';
const props = withDefaults(
  defineProps<{
    info: ApiV1RollroomListGet200ResponseDataListInner;
  }>(),
  {},
);
const $emit = defineEmits(['finish']);
// 倒计时总时长,距离开始时间/距离结束时间/0
const unStart = props.info.cstatus === RollStatus.UNSTART;
const endTime = unStart ? props.info.started_at : props.info.ended_at;
const { renderCountDown, time, duration } = useNaiveUICountdown(endTime);

const finish = () => {
  if (duration.value > 0) {
    $emit('finish');
  }
};
</script>
<style lang="scss" scoped>
.title-bg {
  @apply absolute -left-[29.5px] -top-[24px] rounded-t-[8px] bg-no-repeat bg-right-top max-sm:!w-[calc(100%+27px)] max-sm:-left-[13.5px];
  width: calc(100% + 59px);
  height: 100%;
  background-size: cover;
}
.countdown-item {
  @apply flex flex-col text-[12px] items-center justify-center w-[54px] h-[75px] bg-black/30 rounded-lg text-white;
  b {
    @apply text-[20px] mb-[10px] leading-none;
  }
  span {
    @apply -mb-[8px];
  }
}
</style>
