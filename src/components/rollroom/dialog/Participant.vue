<template>
  <div
    class="flex items-center bg-[#20263B] p-2 sm:p-4 rounded-lg gap-x-3"
    :class="{ 'border border-theme-color bg-theme-color/10': uid === user.uid }"
  >
    <n-avatar :src="user.avatar" :size="30" class="flex-shrink-0" />
    <UserLevel class="h-[20px] !bg-[#C9CBD7]/10" :level="user.level" />
    <n-ellipsis class="max-w-[130px] flex-shrink-0">
      {{ user.steam_name }}
    </n-ellipsis>
  </div>
</template>
<script lang="ts" setup>
import { useSettingStore } from '~/stores/modules/setting';
withDefaults(
  defineProps<{
    user: ApiV1RollroomUsersGet200ResponseDataListInner;
  }>(),
  {},
);
const settingStore = useSettingStore();
const uid = computed(() => settingStore.userInfo?.uid);
</script>
<style lang="scss" scoped></style>
