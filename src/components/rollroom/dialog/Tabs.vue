<template>
  <!-- list -->
  <n-tabs
    animated
    :value="tab"
    tabClass="font-bold uppercase"
    @update:value="tabChange"
  >
    <n-tab-pane name="reward" :tab="$t('reward')">
      <n-scrollbar class="h-[200px] sm:h-[300px] text-white">
        <template v-if="rewardList.length">
          <div class="flex flex-col gap-y-[8px]">
            <RollroomDialogReward
              v-for="(item, index) in rewardList"
              :key="item.goods_id"
              :info="item"
              :index="index + 1"
              :finished="cstatus === RollStatus.FINISH"
            />
          </div>
        </template>
        <template v-else-if="rewardLoading">
          <n-spin size="large" class="w-full h-[200px] sm:h-[300px]" />
        </template>
        <template v-else>
          <Empty
            :msg="$t('no_data')"
            class="h-[200px] sm:h-[300px]"
            column
          ></Empty>
        </template>
      </n-scrollbar>
    </n-tab-pane>
    <n-tab-pane name="user" :tab="$t('participants')">
      <n-infinite-scroll
        v-if="userList.length"
        class="h-[200px] sm:h-[300px] text-white"
        :distance="50"
        @load="handleLoad"
      >
        <div class="participants-list">
          <RollroomDialogParticipant
            v-for="item in userList"
            :key="item.uid"
            :user="item"
          />
        </div>
        <div v-if="userLoading && userPage > 1" class="text-center">
          loading...
        </div>
      </n-infinite-scroll>
      <template v-else-if="userLoading">
        <n-spin size="large" class="w-full h-[200px] sm:h-[300px]" />
      </template>
      <template v-else>
        <Empty
          class="h-[200px] sm:h-[300px]"
          :msg="$t('no_data')"
          column
        ></Empty>
      </template>
    </n-tab-pane>
  </n-tabs>
</template>
<script lang="ts" setup>
import { RollStatus } from '~/types/rollroom';
const props = withDefaults(
  defineProps<{
    cstatus: number;
    id: number;
  }>(),
  {},
);
const { rollroomApi } = useApi();
const tab = ref('reward');
// 奖品信息
const rewardList = ref<ApiV1RollroomAwardsGet200ResponseDataListInner[]>([]);
const rewardLoading = ref(false);
// 用户
const userList = ref<ApiV1RollroomUsersGet200ResponseDataListInner[]>([]);
const userLoading = ref(false);
const userTotal = ref(0);
const userPage = ref(1);
const userNomore = computed(
  () => userTotal.value && userTotal.value <= userList.value.length,
);
// 获取奖励列表
const getAwards = async () => {
  rewardLoading.value = true;
  const { data: req } = await rollroomApi.getRollroomAwards(props.id);
  rewardLoading.value = false;
  const data = req.value?.data;
  if (data) {
    rewardList.value = data.list;
  }
};
// 获取参与用户列表
const getUsers = async (reset: boolean = false) => {
  if (reset) {
    userTotal.value = 0;
    userPage.value = 1;
  }
  if (userNomore.value) return;
  if (userLoading.value) return;
  userLoading.value = true;
  const { data: req } = await rollroomApi.getRollroomUsers({
    page: userPage.value,
    page_size: 60,
    room_id: props.id,
  });
  userLoading.value = false;
  const data = req.value?.data;
  if (data) {
    if (reset) userList.value = [];
    const list = data.list || [];
    userList.value.push(...list);
    userTotal.value = data.total;
    userPage.value += 1;
  }
};
// 用户分页加载更多
const handleLoad = () => {
  if (userPage.value > 1) getUsers();
};
// tab切换
const tabChange = (value: string) => {
  tab.value = value;
  if (value === 'user') {
    getUsers(true);
  }
};
getAwards();

defineExpose({
  getAwards,
  getUsers,
  tab,
});
</script>
<style lang="scss" scoped>
.participants-list {
  @apply grid gap-x-[9px] gap-y-[8px];
  grid-template-columns: repeat(auto-fill, minmax(266px, 1fr));
}
</style>
