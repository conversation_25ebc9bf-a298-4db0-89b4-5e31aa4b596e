<template>
  <div ref="rollroomDialog" class="text-primary-text">
    <!-- 房间信息 -->
    <RollroomDialogRoom :info="info" @finish="finish" />
    <!-- Public Hash -->
    <div class="mb-4">
      <h3 class="text-[#7D90CA] text-[14px] font-bold">
        {{
          info.cstatus === RollStatus.FINISH ? $t('public') : $t('public_hash')
        }}
      </h3>
      <div
        class="w-full py-3 px-4 flex justify-between items-center bg-[#20263B] rounded mt-4 cursor-pointer text-white"
        @click="legacyCopy"
      >
        <p>{{ info.public_seed_hash }}</p>
        <svgo-copy class="text-[#7D90CA] text-[24px] shrink-0"></svgo-copy>
      </div>
    </div>
    <!-- Conditions -->
    <RollroomDialogCondition
      :conditions="info.conditions"
      :loading="detailLoading"
    />
    <!-- tabs 奖品信息/参与用户 -->
    <RollroomDialogTabs :id="info.id" ref="tabs" :cstatus="info.cstatus" />

    <!-- button -->
    <div class="flex gap-x-5 mt-[12px] sm:mt-[24px] justify-end">
      <Fairness
        class="bg-[#2E3757] rounded justify-center px-4 text-white font-bold"
        size="small"
        upperCase
      />
      <RollroomBtns
        class="min-w-[128px]"
        size="large"
        :joined="info.is_joined"
        :cstatus="info.cstatus"
        :disabled="joinLoading"
        @join="joinGame"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { RollStatus } from '~/types/rollroom';
const props = withDefaults(
  defineProps<{
    detail: ApiV1RollroomListGet200ResponseDataListInner;
    history?: boolean;
    joinLoading?: boolean;
  }>(),
  {
    history: false,
    joinLoading: undefined,
  },
);
const $emit = defineEmits(['joinGame', 'updateRoom']);
const { copy } = useCopy();
const { t } = useI18n();
const { rollroomApi } = useApi();
const toast = useAppToast();
const rollroomDialog = ref();
const detailLoading = ref(true);
const info = computed(() => props.detail);
const tabs = ref();
// 更新参与用户
const updateUser = () => {
  if (tabs.value.tab === 'user') {
    tabs.value.getUsers(true);
  }
};
// 倒计时结束
const finish = () => {
  // 公平性倒计时结束需要更新列表数据
  // 卡片由卡片倒计时结束更新数据(防止卡片和弹窗倒计时结束调用两次详情)
  if (props.history) {
    getDetail();
  }
  // 倒计时接口,展示获奖人数
  setTimeout(tabs.value.getAwards, 1000);
  // 更新参与用户
  updateUser();
};
// 加入房间
const joinGame = () => {
  $emit('joinGame', () => {
    updateUser();
  });
};
// 复制文字
const legacyCopy = () => {
  copy(info.value.public_seed_hash, rollroomDialog.value);
  toast.success({ content: t('replicating_success') });
};

// 获取详情
const getDetail = async () => {
  const { data: req } = await rollroomApi.getRollroomDetail(info.value.id);
  detailLoading.value = false;
  const data = req.value?.data;
  if (data) {
    $emit('updateRoom', data);
  }
};
// 获取详情
getDetail();
</script>
<style lang="scss" scoped>
.participants-list {
  @apply grid my-4 gap-x-3 gap-y-2;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}
</style>
