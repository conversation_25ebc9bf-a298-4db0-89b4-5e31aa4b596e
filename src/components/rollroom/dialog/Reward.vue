<template>
  <div
    class="bg-[#20263B] px-4 py-[4px] rounded-lg flex items-center gap-2 overflow-auto"
  >
    <div class="flex grow items-center gap-x-2">
      <div class="flex-shrink-0 w-[30px] font-bold">#{{ index }}</div>
      <img
        class="w-[75px] h-[56px] object-contain flex-shrink-0"
        :src="info.goods_icon"
        alt=""
      />
      <n-ellipsis class="grow w-[350px]">
        {{ $td(info.market_name || '') }}
      </n-ellipsis>
    </div>
    <div class="flex flex-shrink-0 gap-x-2">
      <RollroomPrice class="w-[110px] font-bold">
        {{ info.goods_amount }}
      </RollroomPrice>
      <div v-if="finished" class="flex items-center w-[125px]">
        <template v-if="info.user_profile.uid">
          <n-avatar
            :src="info.user_profile.avatar"
            :size="30"
            class="mr-2 cursor-pointer"
          />
          <n-ellipsis class="max-w-[90px]">
            {{ info.user_profile.steam_name }}
          </n-ellipsis>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    info: ApiV1RollroomAwardsGet200ResponseDataListInner;
    index: number;
    finished: boolean;
  }>(),
  {},
);
</script>
<style lang="scss" scoped></style>
