<template>
  <div class="flex flex-col items-center justify-center" :class="[className]">
    <slot name="icon">
      <BaseIcon
        v-if="icon"
        :name="icon"
        :class="iconClass || 'w-[214px] h-[114px]'"
      />
      <img
        v-else-if="imageSrc"
        :src="imageSrc"
        :class="iconClass || 'w-[214px] h-[114px]'"
      />
    </slot>

    <!-- 主要文本 -->
    <span :class="messageClass || 'text-gray-3 mt-3'">
      {{ $t(message) || $t('no_data') }}
    </span>

    <!-- 描述文本  -->
    <span
      v-if="description"
      :class="descriptionClass || 'text-gray-2 text-sm mt-1'"
    >
      {{ $t(description) }}
    </span>

    <!-- 操作按钮插槽 -->
    <slot name="action"></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  // 显示的消息
  message: {
    type: String,
    default: '',
  },
  // 附加描述
  description: {
    type: String,
    default: '',
  },
  // 组件自定义类名
  className: {
    type: String,
    default: '',
  },
  // SVG 组件
  icon: {
    type: [String],
    default: null,
  },
  // 图片路径
  imageSrc: {
    type: String,
    default: '',
  },
  // 自定义类名
  iconClass: {
    type: String,
    default: '',
  },
  // 消息自定义类名
  messageClass: {
    type: String,
    default: '',
  },
  // 描述自定义类名
  descriptionClass: {
    type: String,
    default: '',
  },
});
</script>
