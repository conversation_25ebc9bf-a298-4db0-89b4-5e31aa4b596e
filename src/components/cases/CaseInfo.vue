<template>
  <div
    class="w-full flex flex-wrap justify-between items-center mb-[15px] relative gap-2 min-h-[57px]"
  >
    <div class="cursor-pointer text-[#7D90CA]" @click="backCases">
      <BaseIcon name="back" class="mr-2 !text-[10px] mb-0.5"></BaseIcon>
      <span>{{ $t('back') }}</span>
    </div>
    <div class="flex items-center justify-between">
      <Fairness class="!text-[#7D90CA]" />
      <SoundSwitch class="ml-5" />
    </div>
    <div class="lg:absolute w-full flex justify-center pointer-events-none">
      <div class="case-bg">
        <MediaDisplay
          :src="info.image_url"
          class="h-[37px] object-contain mr-3"
          alt=""
        />
        <h1 class="text-[16px] sm:text-[20px] font-bold">
          {{ info.case_name }}
        </h1>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
withDefaults(
  defineProps<{
    info: V1CasesItem;
  }>(),
  {
    info: undefined,
  },
);
const $router = useRouter();
// 返回上一页
const backCases = () => {
  if ($router.options.history.state.back) {
    $router.back();
  } else {
    $router.push('/cases');
  }
};
</script>
<style lang="scss" scoped>
.case-bg {
  @apply flex items-center text-white max-sm:order-last px-[10px] py-[5px] sm:px-[20px] sm:py-[10px] rounded-lg;
  background-image: linear-gradient(
      180deg,
      rgba(255, 188, 54, 0.1) 2%,
      rgba(248, 184, 56, 0.02) 100%
    ),
    radial-gradient(
      53% 38% at 50% 95%,
      rgba(248, 184, 56, 0.24) 2%,
      rgba(248, 184, 56, 0) 100%
    );
}
.case-position {
  @apply absolute top-1/2 -translate-y-1/2 max-md:top-0;
}
</style>
