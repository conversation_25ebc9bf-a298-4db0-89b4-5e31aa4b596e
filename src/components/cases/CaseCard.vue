<template>
  <div
    class="flex flex-col items-center p-[20px] rounded-lg text-white cursor-pointer case-card"
    @click="goCasePage"
  >
    <MediaDisplay
      :src="info.image_url"
      :class-name="'w-[118px] h-[118px] object-contain transition-transform scale-img'"
      alt=""
    />
    <n-ellipsis
      class="w-full text-center mb-[16px] mt-[12px] text-[16px] font-bold leading-[20px]"
    >
      {{ info.case_name }}
    </n-ellipsis>
    <div class="w-[128px] h-[42px] relative">
      <div class="case-amount">
        <BaseIcon name="gold" class="mr-2 !text-[20px]"></BaseIcon>
        <span>{{ info.total_price }}</span>
      </div>
      <div class="opacity-0 open-case">
        <MainButton>
          {{ $t('open') }}
        </MainButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// import { Sound } from '~/types/sound';
const props = withDefaults(
  defineProps<{
    info: V1CasesItem;
  }>(),
  {},
);

const $router = useRouter();
// const { play } = useSoundControl();
const goCasePage = () => {
  // play(Sound.CASE_CHOOSE);
  $router.push(`/cases/${props.info.slug}`);
};
</script>
<style lang="scss" scoped>
.case-amount {
  @apply bg-[#262D49] w-full h-full flex items-center justify-center rounded-[4px] text-[14px] absolute;
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(125, 144, 202, 0.1) 0%,
      rgba(125, 144, 202, 0) 49%
    )
    1;
}
@media (any-hover: hover) {
  .case-card:hover {
    .case-amount {
      transition: opacity 300ms ease-in-out;
      opacity: 0 !important;
    }
    .open-case {
      transition: opacity 300ms ease-in-out;
      opacity: 1 !important;
    }
    .scale-img {
      transition-timing-function: ease-in-out;
      transition-duration: 300ms;
      transform: scale3d(1.2, 1.2, 1.2);
    }
  }
}
</style>
