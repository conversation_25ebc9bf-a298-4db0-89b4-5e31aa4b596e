<template>
  <!-- open cases animation -->
  <div class="w-full h-full overflow-hidden flex justify-center items-center">
    <div
      ref="scrollBox"
      class="flex flex-col w-full will-change-transform"
      :class="{
        'transition-transform ease-[cubic-bezier(0,0.48,0.05,1)]': !skip,
        'duration-[2s]': fast && !skip,
        'duration-[5s]': !fast && !skip,
      }"
      :style="{
        transform: `translate3d(0, ${translate}%, 0)`,
      }"
    >
      <template v-for="(item, index) in spinnerInfo.spinnerList" :key="index">
        <div
          class="relative w-full"
          :class="{
            'res-hover': convertBtn(index),
            'z-10': index === endIndex,
          }"
        >
          <div
            class="relative w-full h-[210px] skin-box"
            :class="{
              'transition-opacity duration-100': index === endIndex,
              'opacity-30': index !== highlightIndex,
              'opacity-100': index === highlightIndex,
              '!opacity-60': converted && index === endIndex,
            }"
          >
            <div
              class="w-full h-full relative will-change-transform"
              :class="{
                'transition-transform': !skip,
                'duration-[800ms]': !skip,
                'translate-y-[100px]':
                  endIndex < index &&
                  animateStatus >= AnimateStatus.REBOUND_SATRT,
              }"
            >
              <CasesSkinCardBg
                :tags="item.tags"
                :imgUrl="item.icon_url"
                :gradientBorder="[[90, 90], 3, 20]"
                :imgHeight="126"
                :bgLazy="false"
                class="mx-[2px] !bg-[#05060A] !rounded-[4px] h-full"
                contentClass="h-full flex items-center"
              />
              <Transition :name="endIndexAni">
                <div
                  v-if="
                    animateStatus >= AnimateStatus.REBOUND_SATRT &&
                    index === endIndex
                  "
                  class="absolute w-full top-[175px]"
                >
                  <div class="text-white">
                    <div
                      class="flex justify-center text-white/40 text-[16px] leading-none gap-x-[8px]"
                    >
                      <AccessoryTag :info="item" />
                      <span>{{ item.category_name }}</span>
                    </div>
                    <p
                      class="text-center w-full overflow-hidden text-ellipsis whitespace-nowrap mt-[8px] mb-[16px] font-bold"
                    >
                      {{ $td(item.steam_item_name || '') }}
                    </p>
                  </div>
                  <div class="flex items-center text-white xl:justify-center">
                    <BaseIcon name="gold" class="!text-[20px] mr-2"></BaseIcon>
                    {{ item.pawn_price }}
                  </div>
                </div>
              </Transition>
            </div>
          </div>
          <div
            v-if="convertBtn(index)"
            class="absolute left-0 right-0 bottom-0 top-0 items-center justify-center flex opacity-0 transition-opacity duration-200 pt-[25%] skin-convert"
            :class="{ '!opacity-100': converted }"
          >
            <MainButton
              v-if="!converted"
              type="secondary"
              :disabled="btnDisabled"
              @click="convert"
            >
              {{ $t('convert') }}
            </MainButton>
            <div
              v-else
              class="text-white bg-white/10 leading-[30px] px-2 rounded-[4px] -rotate-[20deg] uppercase"
            >
              {{ $t('converted') }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { AnimateStatus, type SpinnerInfoType } from '~/types/cases';
const { spinnerInfo, fast, skip, hash } = defineProps<{
  spinnerInfo: SpinnerInfoType;
  fast: boolean;
  skip: boolean;
  hash: string;
  btnDisabled: boolean;
}>();
const $emit = defineEmits(['finish', 'convert']);
const converted = computed(() => spinnerInfo.converted);
const endIndexAni = computed(() => {
  let aniName = 'ani';
  if (skip) aniName = 'no-ani';
  // if (fast) aniName = 'fast-ani';
  return aniName;
});
// 滚动盒子
const scrollBox = ref();
const {
  animateStatus,
  highlightIndex,
  endIndex,
  translate,
  start: startAni,
  reset,
} = useOpenCase(scrollBox, hash, {
  isCol: true,
  finish: spinnerInfo.finished,
});
// 是否展示convert按钮
const convertBtn = computed(() => {
  return (index: number) =>
    animateStatus.value === AnimateStatus.FINISH &&
    index === endIndex &&
    !spinnerInfo.experiment;
});
// start传入参数
const start = () => {
  startAni({
    fast,
    skip,
  });
};
// 监听动画是否结束
watch(animateStatus, (newV) => {
  if (newV === AnimateStatus.FINISH) {
    $emit('finish');
  }
});
// 转换
const convert = () => {
  if (converted.value) return;
  $emit('convert');
};

defineExpose({
  start,
  reset,
  animateStatus,
  endIndex,
});
</script>
<style lang="scss" scoped>
.ani-enter-active,
.ani-leave-active {
  transform-origin: top center;
  transition: all 700ms ease;
}

.ani-enter-from,
.ani-leave-to {
  opacity: 0;
  transform: scale(0.5) translateY(-40px);
}

.res-hover:hover {
  .skin-box {
    opacity: 0.15 !important;
  }
  cursor: pointer;
  .skin-convert {
    opacity: 1 !important;
  }
}
</style>
