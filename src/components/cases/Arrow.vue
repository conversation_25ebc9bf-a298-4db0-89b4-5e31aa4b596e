<template>
  <div class="arrow-bg">
    <BaseIcon
      name="spinArrow"
      class="relative !text-[32px] -mb-[8px]"
    ></BaseIcon>
  </div>
</template>
<style lang="scss" scoped>
.arrow-bg {
  @apply w-[86px] h-[44px] flex justify-center items-end relative overflow-hidden;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      44% 80% at 50% 94%,
      rgba(248, 184, 56, 0.4) 0%,
      rgba(248, 184, 56, 0.14) 51%,
      rgba(254, 192, 86, 0.02) 100%
    );
    filter: blur(10px);
  }
}
</style>
