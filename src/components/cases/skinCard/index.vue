<!-- eslint-disable vue/no-v-html -->
<template>
  <CasesSkinCardBg
    :tags="info.tags"
    :imgUrl="info.icon_url"
    :gradientCircle="curSize.gradientCircle"
    :gradientTriangle="curSize.gradientTriangle"
    :gradientBorder="curSize.gradientBorder"
    :imgHeight="curSize.imgHeight"
    :style="{
      borderRadius: curSize.rounded,
    }"
  >
    <template #header="{ class: slotClass }">
      <slot name="header">
        <div
          :class="slotClass"
          :style="{
            padding: curSize.headerPadding,
            margin: curSize.headerMargin,
            minHeight: curSize.headerHeight,
          }"
        >
          <AccessoryTag
            :info="info"
            :style="{
              'font-size': curSize.textSize,
            }"
            :size="curSize.tagSize"
          />
          <slot name="header-right"></slot>
        </div>
      </slot>
    </template>
    <template #default>
      <slot>
        <span class="text-[10px] hidden">{{ info.record_id }}</span>
        <CasesSkinCardEllipsis
          :text="$td(info.market_hash_name || '')"
          :popoverText="$td(info.market_hash_name || '')"
          className="p-[4px_6px] sm:p-[12px] pb-[20px] leading-none font-bold max-sm:text-sm"
        />
      </slot>
    </template>
    <template v-if="mask" #mask="{ class: slotClass, style: slotStyle }">
      <slot name="mask">
        <component
          :is="`CasesSkinCardMask${mask}`"
          :info="info"
          :class="slotClass"
          :style="slotStyle"
        ></component>
      </slot>
    </template>
  </CasesSkinCardBg>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
type CardSize = 'sm' | 'md';
type ConfigItem = {
  gradientCircle?: (number | number[])[];
  gradientTriangle?: (number | number[])[];
  gradientBorder?: (number | number[])[];
  imgHeight?: number;
  rounded?: string;
  textSize?: string;
  headerPadding?: string;
  headerMargin?: string;
  headerHeight?: string;
  tagSize?: number;
};
const props = withDefaults(
  defineProps<{
    info: CaseSkinItemType;
    mask?: string;
    size?: CardSize;
    customConfig?: ConfigItem;
  }>(),
  {
    mask: undefined,
    size: 'md',
    customConfig: undefined,
  },
);
const sizeConfig = {
  sm: {
    gradientCircle: [[172, 128], 52],
    gradientTriangle: [[106, 103]],
    gradientBorder: [[54, 54], 2, 12],
    imgHeight: 76,
    rounded: '4px',
    textSize: '14px',
    headerPadding: '6px 8px 0 8px',
    headerMargin: '0 0 3px 0',
    headerHeight: '20px',
    tagSize: 14,
  },
  md: {
    gradientCircle: [[320, 236], 95],
    gradientTriangle: [[206, 202]],
    gradientBorder: [[80, 80], 3, 20],
    imgHeight: 112,
    rounded: '8px',
    textSize: '16px',
    headerPadding: '14px 16px 0 16px',
    headerMargin: '0 0 8px 0',
    headerHeight: '30px',
    tagSize: 17,
  },
};
const curSize = computed(() => {
  return props.customConfig || sizeConfig[props.size];
});
</script>
<style lang="scss" scoped></style>
