<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="relative bg-[#151A29] cursor-pointer overflow-hidden">
    <!-- 品质背景色 -->
    <div class="absolute top-0 bottom-0 right-0 left-0">
      <div
        v-if="gradientCircle"
        class="radial-bg rounded-full"
        :style="gradientCircleStyle"
      ></div>
      <div
        v-if="gradientTriangle"
        class="radial-bg"
        :style="gradientTriangleStyle"
      ></div>
    </div>
    <div class="relative z-10 h-full" :class="contentClass">
      <slot name="mask" :class="mask.class" :style="mask.style"></slot>
      <slot
        name="header"
        :class="'relative z-20 flex justify-between items-center'"
      ></slot>
      <div
        class="relative w-full flex justify-center items-center"
        :style="{ height: `${imgHeight}px` }"
      >
        <GradientBorder
          v-if="gradientBorder"
          :width="(gradientBorder[0] as number[])[0]"
          :height="(gradientBorder[0] as number[])[1]"
          :borderRadius="gradientBorder[2] as number"
          :borderWidth="gradientBorder[1] as number"
          :startColor="opacityColor()"
          class="gradient-border-position"
        />
        <MediaDisplay
          :src="imgUrl"
          :lazy="bgLazy"
          class="absolute w-full max-h-full object-contain"
        />
      </div>
      <div class="relative z-20">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// gradientCircle [[长,宽], 距离顶部距离]
// gradientTriangle [[长,宽], 距离顶部距离]
// gradientBorder [[长,宽], 边宽宽度, 圆角]
const props = withDefaults(
  defineProps<{
    imgUrl?: string;
    tags?: MarketGoodsInfotagList;
    gradientCircle?: (number | number[])[];
    gradientTriangle?: (number | number[])[];
    gradientBorder?: (number | number[])[];
    imgHeight?: number;
    showQuality?: boolean;
    contentClass?: string;
    bgLazy?: boolean;
  }>(),
  {
    imgUrl: '',
    tags: undefined,
    gradientCircle: undefined,
    gradientTriangle: undefined,
    gradientBorder: undefined,
    imgHeight: 112,
    showQuality: false,
    contentClass: '',
    bgLazy: true,
  },
);
const opacityColor = (opacity: number = 1) => {
  const color = props.tags?.rarity?.color;
  if (!color) return '';
  return hexToRgba(props.tags?.rarity?.color as string, opacity);
};
// 圆形背景
const gradientCircleStyle = computed(() => {
  const gradientCircle = props.gradientCircle;
  if (!gradientCircle) return {};
  const wh = gradientCircle[0] as number[];
  return {
    background: `radial-gradient(41% 41% at 50% 50%, ${opacityColor(0.4)} 0%, ${opacityColor(0.07)} 100%)`,
    width: `${wh[0]}px`,
    height: `${wh[1]}px`,
    top: `${gradientCircle[1]}px`,
  };
});
// 三角形背景
const gradientTriangleStyle = computed(() => {
  const gradientTriangle = props.gradientTriangle;
  if (!gradientTriangle) return {};
  const wh = gradientTriangle[0] as number[];
  return {
    background: `radial-gradient(52% 94% at 50% 94%, ${opacityColor(0.2)} 0%, ${opacityColor(0.02)} 100%)`,
    width: `${wh[0]}px`,
    height: `${wh[1]}px`,
    bottom: '0',
  };
});

const mask = {
  class:
    'flex flex-col items-center justify-center p-3 absolute top-0 bottom-0 right-0 left-0 z-10 opacity-0 hover:z-30 hover:opacity-100 transition-opacity duration-200',
  style: {
    background:
      'linear-gradient(180deg, rgba(21, 26, 41, 0.9) 0%, #151a29 48%, rgba(21, 26, 41, 0.9) 100%)',
  },
};
</script>
<style lang="scss" scoped>
.radial-bg {
  @apply absolute left-1/2 -translate-x-1/2;
  filter: blur(10px);
}
</style>
