<template>
  <div>
    <n-avatar :src="info.avatar" :size="60" class="mb-[8px]" />
    <n-ellipsis class="w-full text-white text-[16px] text-center">
      {{ info.nickname }}
    </n-ellipsis>
  </div>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
withDefaults(
  defineProps<{
    info: CaseSkinItemType;
  }>(),
  {},
);
</script>
<style lang="scss" scoped></style>
