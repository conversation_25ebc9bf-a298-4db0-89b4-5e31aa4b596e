<template>
  <div class="!justify-start text-center text-[16px]">
    <n-ellipsis class="w-full px-[12px] mt-[12px]">
      {{ $td(info.market_hash_name || '') }}
    </n-ellipsis>
    <div class="mt-[45px]">
      <p class="text-[#7D90CA] text-[14px]">{{ $t('chance') }}</p>
      <p class="text-white">{{ info.chance }}%</p>
    </div>
    <div class="mt-[16px]">
      <p class="text-[#7D90CA] text-[14px]">{{ $t('range') }}</p>
      <p class="text-white">
        {{ info.number_space_from }}-{{ info.number_space_to }}
      </p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
withDefaults(
  defineProps<{
    info: CaseSkinItemType;
  }>(),
  {},
);
</script>
<style lang="scss" scoped></style>
