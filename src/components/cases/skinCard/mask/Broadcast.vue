<template>
  <div @click="goCaseDetail">
    <MediaDisplay
      :src="info.case?.image_url"
      :class-name="'h-[80px] object-contain'"
      alt=""
    />
    <div class="flex items-center justify-center gap-x-2 mt-[8px]">
      <n-avatar :src="info.avatar" :size="26" class="shrink-0" />
      <n-ellipsis class="max-w-[70px]">
        {{ info.nickname }}
      </n-ellipsis>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
const props = withDefaults(
  defineProps<{
    info: CaseSkinItemType;
  }>(),
  {},
);
const $router = useRouter();
const goCaseDetail = () => {
  $router.push(`/cases/${props.info.slug}`);
};
</script>
<style lang="scss" scoped></style>
