<template>
  <Tooltip trigger="hover">
    <template #trigger>
      <p
        class="w-full text-center overflow-hidden text-ellipsis whitespace-nowrap"
        :class="className"
      >
        {{ text }}
      </p>
    </template>
    <p>{{ popoverText }}</p>
  </Tooltip>
</template>
<script lang="ts" setup>
defineProps({
  text: {
    type: String,
    default: '',
  },
  popoverText: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
});
</script>
