<template>
  <CasesSkinCard :info="info" mask="Lucky">
    <template #default>
      <div class="w-full text-center text-[16px] leading-[20px]">
        <p class="text-white/40 mt-[8px] mb-[6px]">
          {{ info.category_name }}
        </p>
        <CasesSkinCardEllipsis
          :text="$td(info.steam_item_name || '')"
          :popoverText="$td(info.market_hash_name || '')"
          className="px-[14px] font-bold"
        />
      </div>
    </template>
  </CasesSkinCard>
</template>
<script lang="ts" setup>
import type { CaseSkinItemType } from '~/types/cases';
withDefaults(
  defineProps<{
    info: CaseSkinItemType;
  }>(),
  {},
);
</script>
<style lang="scss" scoped></style>
