<template>
  <div
    class="w-full flex relative flex-col xl:flex-row xl:h-[300px] border-[3px] border-solid border-[#323A51] rounded-[8px] bg-[#05060A] overflow-hidden"
    :class="{ 'justify-center': isSpinnerDecrease }"
  >
    <template v-if="isCol">
      <CasesArrow
        class="rotate-90 -ml-[25px] absolute z-50 top-1/2 left-0 -translate-y-1/2"
      />
      <CasesArrow
        class="-rotate-90 -mr-[25px] absolute z-50 top-1/2 right-0 -translate-y-1/2"
      />
    </template>
    <template v-else>
      <CasesArrow
        class="rotate-180 -mt-[3px] absolute z-50 left-1/2 top-0 -translate-x-1/2"
      />
      <CasesArrow
        class="-mb-[3px] absolute z-50 left-1/2 bottom-0 -translate-x-1/2"
      />
    </template>
    <!-- 跳过动画 -->
    <div
      v-if="skip"
      class="absolute w-full h-full z-[20] transition-opacity duration-500 bg-[#05060A] flex max-xl:flex-col pointer-events-none"
      :class="`${btnDisabled ? 'opacity-1' : 'opacity-0'}`"
    >
      <div
        v-for="(_spinnerInfo, index) in spinnerInfos"
        :key="index + apngKey"
        class="flex-1 relative"
      >
        <!-- 成功动画 -->
        <img
          :key="index + apngKey"
          :src="
            skipResShow
              ? `/imgs/opencase/skip_win${winItemQualitys[index]}.png`
              : ''
          "
          alt=""
          class="skip-img-apng"
          :class="`${skipResShow ? 'opacity-1' : 'opacity-0'}`"
        />
        <!-- loading 动画 -->
        <img
          :key="index + apngKey"
          src="/imgs/opencase/skip_transition_ani.png"
          alt=""
          class="skip-img-apng !duration-[700ms]"
          :class="`${!skipResShow && btnDisabled ? 'opacity-1' : 'opacity-0'}`"
        />
      </div>
    </div>
    <template v-if="isCol">
      <transition-group>
        <div
          v-for="(item, index) in spinnerInfos"
          :key="index"
          class="h-full"
          :class="{
            'transition-all delay-[500ms]': isSpinnerDecrease,
            'v-move': index === 0,
          }"
          :style="{ width: `${100 / spinnerInfos.length}%` }"
        >
          <CasesOpenAniCol
            ref="casesOpenAniRefCol"
            :fast="fast"
            :skip="skip"
            :spinnerInfo="item"
            :hash="`${seed}-${index}`"
            :btnDisabled="convertLoading"
            @finish="finishAni(index)"
            @convert="convert(index)"
          />
        </div>
      </transition-group>
    </template>
    <template v-else>
      <CasesOpenAniRow
        v-for="(item, index) in spinnerInfos"
        :key="index"
        ref="casesOpenAniRefRow"
        :fast="fast"
        :skip="skip"
        :spinnerInfo="item"
        :hash="`${seed}-${index}`"
        :btnDisabled="convertLoading"
        :pageXl="activeBreakpoint === 'desktop'"
        @finish="finishAni(index)"
        @convert="convert(index)"
      />
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useCaseOpenStore } from '~/stores/modules/caseOpen';
import { AnimateStatus } from '~/types/cases';
import { Sound } from '~/types/sound';

interface OpenAniComponentInstance {
  start: () => void;
  reset: () => void;
  endIndex: number;
  animateStatus: Ref<AnimateStatus>;
}
const props = withDefaults(
  defineProps<{
    fast?: boolean;
    skip?: boolean;
    multiple: boolean;
    btnDisabled?: boolean;
    convertLoading?: boolean;
    levelName?: string;
  }>(),
  {
    fast: false,
    skip: false,
    btnDisabled: false,
    convertLoading: false,
    levelName: '',
  },
);

const $emit = defineEmits(['finish', 'convert']);
const $router = useRouter();
const caseOpenStore = useCaseOpenStore();
const { play } = useSoundControl();
const casesOpenAniRefCol = ref<OpenAniComponentInstance[]>([]);
const casesOpenAniRefRow = ref<OpenAniComponentInstance[]>([]);
// 开箱信息
const spinnerInfos = computed(() => caseOpenStore.spinnerInfos);
// 是否展示竖组件
const isCol = ref(false);
const seed = computed(() => $router.currentRoute.value.params.id);
const apngKey = ref(0);
const skipResShow = ref(false);

const breakpoints = useBreakpoints({
  laptop: 996,
  desktop: 1376,
});
const activeBreakpoint = breakpoints.active();
const winItemQualitys = computed(() => {
  const levels = spinnerInfos.value.map((item) =>
    getQualityLevel(item.selectItem?.tags),
  );
  return levels;
});

// 横竖组件区分
watchEffect(() => {
  const xl = activeBreakpoint.value === 'desktop';
  const newIsCol = xl && props.multiple;
  if (isCol.value !== newIsCol) {
    // 当前的el list
    const elList = isCol.value
      ? casesOpenAniRefCol.value
      : casesOpenAniRefRow.value;
    if (!elList.length) return;
    // 已经有结果的动画,第一个结束就全部结束
    const allFinished = spinnerInfos.value[0].finished;
    if (allFinished) {
      spinnerInfos.value.forEach((el) => (el.finished = !!el.selectItem));
    }
    // 第一个在动画中全部都在动画中
    const isAni =
      elList[0].animateStatus === AnimateStatus.START ||
      elList[0].animateStatus === AnimateStatus.REBOUND_SATRT;
    // 横竖状态修改
    isCol.value = newIsCol;

    if (!isAni) return;
    // 有进行中动画 给新组件赋值动画
    nextTick(() => {
      const nextElList = isCol.value
        ? casesOpenAniRefCol.value
        : casesOpenAniRefRow.value;
      nextElList.forEach((el) => {
        setTimeout(() => {
          el.start();
        });
      });
    });
  }
});

// 打开箱子
const start = async (experiment: boolean, totalPrice?: string) => {
  if (props.skip) {
    apngKey.value += 1;
  }
  play(Sound.CASE_OPEN);
  // 获取结果,等级宝箱
  const isLevelCase = !!props.levelName;
  const res = await caseOpenStore.getCaseRes(
    experiment,
    isLevelCase,
    props.fast,
    props.skip,
    totalPrice,
  );
  if (res.length) {
    const elList = isCol.value
      ? casesOpenAniRefCol.value
      : casesOpenAniRefRow.value;
    if (!props.skip) {
      reset();
      await delay(50);
    }
    for (let i = 0; i < elList.length; i++) {
      const el = elList[i];
      const select = spinnerInfos.value[i].selectItem;
      const spinnerList = spinnerInfos.value[i].spinnerList;
      if (select) {
        spinnerList[el.endIndex] = select;
        el.start();
        if (!props.skip) {
          await delay(props.fast ? 200 : 300);
        }
      }
    }
  } else {
    $emit('finish');
  }
};
// 重置箱子
const reset = () => {
  const elList = isCol.value
    ? casesOpenAniRefCol.value
    : casesOpenAniRefRow.value;
  elList.forEach((el) => {
    el.reset();
  });
};
// 动画结束
const finishAni = async (index: number) => {
  caseOpenStore.spinnerInfos[index].finished = true;
  const allFinished = spinnerInfos.value.every((el) => el.finished);
  if (allFinished) {
    const level = Math.max(
      ...spinnerInfos.value.map((item) =>
        getQualityLevel(item.selectItem?.tags),
      ),
    );
    const winSound = `${props.fast ? 'FAST_' : ''}WIN${level}`;
    const soundKey = winSound as keyof typeof Sound;
    play(Sound[soundKey]);
    if (props.skip) {
      skipResShow.value = true;
      await delay(700);
      skipResShow.value = false;
    }
    $emit('finish');
  }
};
// 转换宝箱物品
const convert = (index: number) => {
  const convertItem = caseOpenStore.spinnerInfos[index];
  const price = Number(convertItem.selectItem?.pawn_price || '0');
  $emit('convert', [convertItem], price);
};

defineExpose({
  start,
});

const isSpinnerDecrease = ref(false);
watch(
  () => spinnerInfos.value.length,
  (to, from) => {
    isSpinnerDecrease.value = to < from;
  },
);
</script>
<style lang="scss" scoped>
.v-move {
  transition: all 0.5s ease;
}
.v-enter-active {
  transform: translateY(-210px);
  animation: spinnerAdd 400ms cubic-bezier(0.55, 0, 0.1, 1) 400ms forwards;
}
.v-enter-from {
  transform: translateY(-210px);
}

.v-leave-active {
  transition: all 400ms ease;
  transition-delay: 0ms !important;
}
.v-leave-to {
  width: 0 !important;
  opacity: 0;
}
@keyframes spinnerAdd {
  0% {
    transform: translateY(-210px);
  }
  60% {
    transform: translateY(10px);
  }
  80% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.skip-open-success-enter-from,
.skip-open-success-leave-to {
  opacity: 0;
}
img:not([src]),
img[src=''] {
  visibility: hidden;
}
.skip-img-apng {
  @apply w-full h-full object-contain absolute transition-opacity duration-[400ms];
}
</style>
