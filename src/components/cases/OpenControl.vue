<template>
  <!-- 跳过/打开 -->
  <div
    class="flex justify-between items-center mt-[24px] flex-wrap gap-2"
    :class="{ '!justify-end': isLevelCase }"
  >
    <!-- 试玩 -->
    <MainButton
      v-if="!isLevelCase"
      class="max-md:hidden"
      type="linear"
      :disabled="disabled"
      @click="openCases(true)"
    >
      {{ $t('experiment') }}
    </MainButton>
    <div class="flex gap-x-[24px] flex-wrap gap-y-2 max-md:w-full">
      <div class="convert-mobile">
        <!-- 试玩 -->
        <MainButton
          v-if="!isLevelCase"
          class="md:hidden"
          type="linear"
          :disabled="disabled"
          @click="openCases(true)"
        >
          {{ $t('experiment') }}
        </MainButton>
        <!-- 转换 -->
        <div v-if="convertBtnShow" class="flex">
          <div v-if="!allConverted" class="amount-bg">
            <BaseIcon
              name="gold"
              class="text-theme-color mr-1.5 !text-[22px]"
            ></BaseIcon>
            {{ convertPrice }}
          </div>
          <MainButton
            type="secondary"
            :border-radius="allConverted ? '4px' : '0 4px 4px 0'"
            :disabled="disabled || allConverted"
            class="uppercase max-md:w-full"
            @click="convertAll"
          >
            {{ allConverted ? $t('converted') : $t('convert') }}
          </MainButton>
        </div>
      </div>
      <!-- 动画速度 -->
      <div class="flex gap-x-[1px] max-md:grow max-md:grid max-md:grid-cols-5">
        <n-popover
          v-for="(item, index) in openSpeedList"
          :key="item"
          trigger="hover"
        >
          <template #trigger>
            <MainButton
              type="info"
              minWidth="42px"
              padding="0"
              :disabled="disabled"
              :selected="openSpeed === item"
              :borderRadius="
                index > 0 && index < 2
                  ? '0'
                  : index === 0
                    ? '4px 0 0 4px'
                    : '0 4px 4px 0'
              "
              @click="handleSpeed(item)"
              @mouseenter="hoverTimes"
            >
              <BaseIcon :name="item" class="!text-[20px]"></BaseIcon>
            </MainButton>
          </template>
          <span>{{ $t(`open_case_speed_${item}`) }}</span>
        </n-popover>
      </div>
      <!-- 开箱个数,正常开箱有,等级宝箱没有 -->
      <template v-if="!isLevelCase">
        <div
          class="flex gap-x-[1px] max-md:grow max-md:grid max-md:grid-cols-5"
        >
          <MainButton
            v-for="index in 5"
            :key="index"
            type="info"
            minWidth="42px"
            :disabled="disabled"
            :selected="index === openNum"
            :borderRadius="
              index > 1 && index < 5
                ? '0'
                : index === 1
                  ? '4px 0 0 4px'
                  : '0 4px 4px 0'
            "
            @click="openCasesNum(index)"
            @mouseenter="hoverTimes"
          >
            {{ index }}
          </MainButton>
        </div>
      </template>
      <!-- 开箱 -->
      <div class="flex max-md:w-full">
        <div class="amount-bg text-theme-color">
          <template v-if="isLevelCase">
            <img :src="keyIcon" alt="" class="w-[20px]" />
            x1
          </template>
          <template v-else>
            <BaseIcon
              name="gold"
              class="text-theme-color mr-1.5 !text-[22px]"
            ></BaseIcon>
            {{ openPrice }}
          </template>
        </div>
        <MainButton
          borderRadius="0 4px 4px 0"
          :disabled="disabled"
          @click="openCases(false)"
        >
          {{ $t('open') }}
        </MainButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { debounce } from 'lodash-es';
import { Sound } from '~/types/sound';
import { useCaseOpenStore } from '~/stores/modules/caseOpen';
import { useSettingStore } from '~/stores/modules/setting';
const props = defineProps({
  disabled: Boolean,
  openNum: {
    type: Number,
    default: 1,
  },
  fast: Boolean,
  skip: Boolean,
  openSpeed: {
    type: String,
    default: 'ordinary',
  },
  levelName: {
    type: String,
    default: '',
  },
  keyQty: {
    type: Number,
    default: 0,
  },
  keyIcon: {
    type: String,
    default: '',
  },
});
const $emit = defineEmits([
  'openCases',
  'update:openNum',
  'update:openSpeed',
  'updateNum',
  'convert',
]);
const caseOpenStore = useCaseOpenStore();
const settingStore = useSettingStore();
const toast = useAppToast();
const { t } = useI18n();
const { openConfirm } = useDialogPromptsConfirm('caseOpenPrompts');
const openSpeedList = ['ordinary', 'fast', 'skip'];
// 余额
const balance = computed(() => settingStore.userInfo?.balance.balance || 0);
// 是否是等级宝箱
const isLevelCase = computed(() => {
  return !!props.levelName;
});
// 未转换列表
const unConvert = computed(() =>
  caseOpenStore.spinnerInfos.filter((item) => !item.converted && item.finished),
);
// 全部已转换
const allConverted = computed(() => unConvert.value.length === 0);
// 所有箱子都结束,控制展示convert按钮
const convertBtnShow = computed(() => {
  const unStartList = caseOpenStore.spinnerInfos.filter(
    (item) => !item.finished,
  );
  const experiment = caseOpenStore.spinnerInfos[0].experiment;
  return (
    unStartList.length !== caseOpenStore.spinnerInfos.length && !experiment
  );
});
// covert价格
const convertPrice = computed(() => {
  return caseOpenStore.spinnerInfos.reduce((pre: number, cur) => {
    if (!cur.converted) {
      const addPrice = BigNumberCalc.add(
        pre,
        cur.selectItem?.pawn_price || '0',
      );
      pre = Number(addPrice);
    }
    return pre;
  }, 0);
});
// 开箱价格
const openPrice = computed(() => {
  const casePrice = caseOpenStore.caseInfo.total_price || '0';
  return BigNumberCalc.multiply(casePrice, props.openNum);
});

// 转换全部
const convertAll = () => {
  $emit('convert', unConvert.value, convertPrice.value);
};

const { play } = useSoundControl();
const clickSoundPlay = debounce(
  () =>
    play(Sound.CLICK_BTN, {
      volume: 1,
    }),
  200,
);
const openCasesNum = (num: number) => {
  $emit('update:openNum', num);
  $emit('updateNum', num);
  clickSoundPlay();
};
const skipAniPreFetch = ref(false);
const handleSpeed = (type: string) => {
  $emit('update:openSpeed', type);
  clickSoundPlay();
  if (type === 'skip' && !skipAniPreFetch.value) {
    skipAniPreFetch.value = true;
    for (let i = 0; i <= 6; i++) {
      const newImg = new Image();
      newImg.src = `/imgs/opencase/skip_win${i}.png`;
    }
  }
};
const hoverTimes = () => {
  play(Sound.HOVER_MOUSE, {
    volume: 1,
  });
};

// 打开宝箱
const openCases = (isExperiment: boolean = false) => {
  if (!isExperiment && !checkLogin()) return;
  // 用钥匙开,没钥匙
  if (isLevelCase.value && props.keyQty < 1) {
    toast.error({ content: t('403019') });
    return;
  }
  // 余额不够
  if (Number(openPrice.value) > Number(balance.value)) {
    useBalanceNotEnoughDialog();
    return;
  }
  if (isExperiment) {
    $emit('openCases', isExperiment);
    return;
  }
  openConfirm({
    title: t('confirm_activation'),
    content: t(
      isLevelCase.value
        ? 'open_case_with_key_confirmation'
        : 'open_case_confirmation',
      { a: openPrice.value, b: props.levelName },
    ),
    onConfirm: () =>
      $emit(
        'openCases',
        isExperiment,
        isLevelCase.value ? undefined : openPrice.value,
      ),
  });
};

const dialog = useAppDialog();
const onKeyDown = (e: KeyboardEvent) => {
  e.preventDefault();
  if (e.key === 'Enter' && !props.disabled && !dialog.isAnyDialogOpen()) {
    openCases();
  }
};
onMounted(() => {
  window.addEventListener('keydown', onKeyDown);
});
onBeforeUnmount(() => {
  window.removeEventListener('keydown', onKeyDown);
});
</script>
<style lang="scss" scoped>
.amount-bg {
  @apply grow px-[12px] bg-white/10 rounded-l-[4px] flex items-center justify-center max-md:grow min-w-[125px];
  background: #262d49;
}
.convert-mobile {
  @apply max-md:w-full max-md:flex max-md:justify-between;
}
</style>
