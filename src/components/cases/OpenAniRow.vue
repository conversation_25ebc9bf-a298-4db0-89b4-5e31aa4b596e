<template>
  <div class="w-full h-full overflow-hidden flex justify-center items-center">
    <div
      ref="scrollBox"
      class="flex will-change-transform"
      :class="{
        'transition-transform ease-[cubic-bezier(0,0.48,0.05,1)]': !skip,
        'duration-[2s]': fast && !skip,
        'duration-[5s]': !fast && !skip,
      }"
      :style="{
        transform: `translate3d(${translate}%, 0, 0)`,
      }"
    >
      <template v-for="(item, index) in spinnerInfo.spinnerList" :key="index">
        <div class="relative" :class="{ 'res-hover': convertBtn(index) }">
          <div
            class="relative w-[112px] h-[130px] xl:w-[223px] xl:h-[260px] skin-box"
            :class="{
              'transition-opacity duration-100': index === endIndex,
              'opacity-30': index !== highlightIndex,
              'opacity-100': index === highlightIndex,
              '!opacity-60': converted && index === endIndex,
            }"
          >
            <div
              class="w-full h-full relative max-xl:flex will-change-transform"
              :class="{
                'transition-transform': !skip,
                'duration-[800ms]': !skip,
                'xl:-translate-y-[43px] max-xl:-translate-x-1/3':
                  endIndex === index &&
                  animateStatus >= AnimateStatus.REBOUND_SATRT,
                'max-xl:-translate-x-[80px]':
                  endIndex > index &&
                  animateStatus >= AnimateStatus.REBOUND_SATRT,
                'max-xl:translate-x-[100px]':
                  endIndex < index &&
                  animateStatus >= AnimateStatus.REBOUND_SATRT,
              }"
            >
              <ClientOnly>
                <CasesSkinCardBg
                  :tags="item.tags"
                  :imgUrl="item.icon_url"
                  :gradientBorder="
                    pageXl ? [[90, 90], 3, 20] : [[60, 60], 2, 10]
                  "
                  :bgLazy="false"
                  :imgHeight="pageXl ? 126 : 63"
                  class="mx-[2px] !bg-[#05060A] !rounded-[4px] w-full h-full shrink-0"
                  contentClass="h-full flex items-center"
                />
              </ClientOnly>
              <Transition :name="endIndexAni">
                <div
                  v-if="
                    animateStatus >= AnimateStatus.REBOUND_SATRT &&
                    index === endIndex
                  "
                  class="w-full flex flex-col justify-center xl:absolute xl:-bottom-[25px] shrink-0"
                >
                  <div class="text-white">
                    <div
                      class="flex xl:justify-center text-white/40 text-[16px] leading-none gap-x-[8px] max-xl:!text-[11px]"
                    >
                      <AccessoryTag
                        :info="item"
                        :size="11"
                        class="max-xl:!text-[11px]"
                      />
                      <span>{{ item.category_name }}</span>
                    </div>
                    <p
                      class="xl:text-center w-full overflow-hidden text-ellipsis whitespace-nowrap mt-[4px] mb-[8px] xl:mt-[8px] xl:mb-[16px] font-bold max-xl:text-[12px]"
                    >
                      {{ $td(item.steam_item_name || '') }}
                    </p>
                  </div>
                  <div class="flex items-center text-white xl:justify-center">
                    <BaseIcon name="gold" class="!text-[20px] mr-2"></BaseIcon>
                    {{ item.pawn_price }}
                  </div>
                </div>
              </Transition>
            </div>
          </div>
          <div
            v-if="convertBtn(index)"
            class="absolute left-0 right-0 bottom-0 top-0 items-center justify-center flex opacity-0 transition-opacity duration-200 skin-convert"
            :class="{ '!opacity-100': converted }"
          >
            <MainButton
              v-if="!converted"
              type="secondary"
              :disabled="btnDisabled"
              @click="convert"
            >
              {{ $t('convert') }}
            </MainButton>
            <div
              v-else
              class="text-white bg-white/10 leading-[30px] px-2 rounded-[4px] -rotate-[20deg] uppercase"
            >
              {{ $t('converted') }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { AnimateStatus, type SpinnerInfoType } from '~/types/cases';
const props = withDefaults(
  defineProps<{
    spinnerInfo: SpinnerInfoType;
    fast: boolean;
    skip: boolean;
    hash: string;
    btnDisabled: boolean;
    pageXl: boolean;
  }>(),
  {},
);
const $emit = defineEmits(['finish', 'convert']);
const converted = computed(() => props.spinnerInfo.converted);
const endIndexAni = computed(() => {
  let aniName = 'ani';
  if (props.skip) aniName = 'no-ani';
  // if (fast) aniName = 'fast-ani';
  return aniName;
});
// 滚动盒子
const scrollBox = ref();
const {
  animateStatus,
  highlightIndex,
  endIndex,
  translate,
  start: startAni,
  reset,
} = useOpenCase(scrollBox, props.hash, {
  finish: props.spinnerInfo.finished,
});

// 是否展示convert按钮
const convertBtn = computed(() => {
  return (index: number) =>
    animateStatus.value === AnimateStatus.FINISH &&
    index === endIndex &&
    !props.spinnerInfo.experiment;
});
// start传入参数
const start = () => {
  startAni({
    skip: props.skip,
    fast: props.fast,
  });
};
// 监听动画状态
watch(animateStatus, (newV) => {
  if (newV === AnimateStatus.FINISH) {
    $emit('finish');
  }
});
// 转换
const convert = () => {
  if (converted.value) return;
  $emit('convert');
};

defineExpose({
  start,
  reset,
  animateStatus,
  endIndex,
});
</script>
<style lang="scss" scoped>
.ani-enter-active,
.ani-leave-active {
  transform-origin: top center;
  transition: all 700ms ease;
  @apply max-xl:origin-left;
}

.ani-enter-from,
.ani-leave-to {
  @apply opacity-0 transform scale-50 max-xl:-translate-x-[40px] xl:-translate-y-[40px];
}

.res-hover:hover {
  .skin-box {
    opacity: 0.15 !important;
  }
  cursor: pointer;
  .skin-convert {
    opacity: 1 !important;
  }
}
</style>
