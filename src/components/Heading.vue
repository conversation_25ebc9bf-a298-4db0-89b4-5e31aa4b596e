<template>
  <component :is="headingTag">
    <slot></slot>
  </component>
</template>

<script setup lang="ts">
interface Props {
  size?: 'xl' | 'lg' | 'md' | 'sm' | 'xs' | 'xxs';
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
});

const headingTag = computed(() => {
  const sizeToLevel = {
    xl: 1,
    lg: 2,
    md: 3,
    sm: 4,
    xs: 5,
    xxs: 6,
  };

  return `h${sizeToLevel[props.size]}`;
});
</script>
