<template>
  <div class="gradient-border-container" :style="containerStyle">
    <div class="gradient-border" :style="borderStyle"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
interface Props {
  width?: number;
  height?: number;
  borderRadius?: number;
  borderWidth?: number;
  startColor: string;
  endColor?: string;
  rotate?: number;
  gradientAngle?: number;
}

const props = withDefaults(defineProps<Props>(), {
  width: 80,
  height: 80,
  borderRadius: 20,
  borderWidth: 3,
  endColor: 'rgba(255, 59, 209, 0.02)',
  rotate: 45,
  gradientAngle: 315,
});
const gradient = computed(
  () =>
    `linear-gradient(${props.gradientAngle}deg, ${props.startColor} 9%, ${props.endColor} 90%)`,
);

const containerStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
  transform: `rotate(${props.rotate}deg)`,
}));

const borderStyle = computed(() => ({
  '--border-radius': `${props.borderRadius}px`,
  '--border-width': `${props.borderWidth}px`,
  '--gradient': gradient.value,
}));
</script>

<style scoped lang="scss">
.gradient-border-container {
  display: inline-block;
  cursor: pointer;
  position: relative;
}

.gradient-border {
  width: 100%;
  height: 100%;
  position: relative;
}

.gradient-border::before {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
  border: var(--border-width) solid transparent;
  background: var(--gradient) border-box;
  mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  mask-composite: xor;
  mask-composite: exclude;
}
</style>
