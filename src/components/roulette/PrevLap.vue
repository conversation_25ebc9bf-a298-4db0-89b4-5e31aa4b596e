<template>
  <div class="text-xs flex items-center justify-center mt-8 flex-wrap">
    <span>上一圈</span>
    <div class="lg:mx-4 my-3 w-[276px] overflow-hidden">
      <div class="flex">
        <transition-group name="pre-res">
          <img
            v-for="item in preRes"
            :key="item.id"
            :src="icons[item.result]"
            alt=""
            class="w-[24px] h-[24px] mr-[4px]"
          />
        </transition-group>
      </div>
    </div>
    <div class="flex items-center">
      <span class="mr-4">最后100</span>
      <div class="flex">
        <div class="flex items-center mr-2">
          <img src="/imgs/coin_gray.webp" alt="" class="w-[20px] h-[20px]" />
          <span class="text-white ml-1">{{ lastRes[1] }}</span>
        </div>
        <div class="flex items-center mr-2">
          <img src="/imgs/coin_orange.webp" alt="" class="w-[20px] h-[20px]" />
          <span class="text-white ml-1">{{ lastRes[2] }}</span>
        </div>
        <div class="flex items-center mr-2">
          <img
            src="/imgs/roulette_center.webp"
            alt=""
            class="w-[20px] h-[20px]"
          />
          <span class="text-white ml-1">{{ lastRes[3] }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
type IconType = {
  [key: number]: string; // 添加数字索引签名
};
type LastRes = {
  [key: number]: number;
};
type PreResItem = {
  result: number;
  id: string;
};
defineProps<{
  preRes: Array<PreResItem>;
  lastRes: LastRes;
}>();
const icons: IconType = {
  1: '/imgs/coin_gray.webp',
  2: '/imgs/coin_orange.webp',
  3: '/imgs/roulette_center.webp',
};
</script>
<style lang="scss" scoped>
// 上一圈结果动画
.pre-res-move, /* 对移动中的元素应用的过渡 */
.pre-res-enter-active,
.pre-res-leave-active {
  transition: all 1s ease;
}
.pre-res-leave-to {
  opacity: 0;
  transform: translateX(-34px);
}
.pre-res-enter-from {
  opacity: 0;
  transform: translateX(34px);
}
/* 确保将离开的元素从布局流中删除
  以便能够正确地计算移动的动画。 */
.pre-res-leave-active {
  position: absolute;
}
</style>
