<!-- eslint-disable vue/multi-word-component-names -->
<script setup lang="ts">
import type { DataTableProps } from 'naive-ui';
import { merge } from 'lodash-es';
type DataTableThemeOverrides = NonNullable<DataTableProps['themeOverrides']>;
const primaryColor = '#FFE100';
const border: string = '1px solid rgba(255, 255, 255, 0.2)';
const themeOverridesDefault: DataTableThemeOverrides = {
  thColor: '#151A29',
  thColorHover: 'rgba(0, 0, 0, 0.0)',
  thPaddingMedium: '8px 16px',
  tdPaddingMedium: '8px 16px',
  thTextColor: '#7D90CA',
  tdColor: 'rgba(0, 0, 0, 0.0)',
  tdColorHover: 'rgba(0, 0, 0, 0.1)',
  tdTextColor: '#fff',
  tdColorStriped: 'rgba(0, 0, 0, 0.0)',
  peers: {
    Pagination: {
      buttonColor: 'rgba(255, 255, 255, 0)',
      buttonBorder: border,
      buttonIconColor: '#fff',
      buttonIconColorHover: '#fff',
      buttonBorderHover: border,
      itemColor: 'rgba(255, 255, 255, 0)',
      itemColorDisabled: 'rgba(255, 255, 255, 0.1)',
      itemColorHover: primaryColor,
      itemBorder: border,
      itemBorderDisabled: border,
      itemTextColor: 'red',
      itemTextColorHover: '#000',
      itemTextColorActive: '#000',
      itemColorActive: primaryColor,
      itemBorderActive: primaryColor,
      itemColorActiveHover: primaryColor,
      inputWidthMedium: '41px',
      peers: {
        Input: {
          color: 'rgba(255, 255, 255, 0.1)',
          textColor: '#fff',
          caretColor: primaryColor,
          borderFocus: border,
          borderHover: border,
          colorFocus: border,
          border,
          boxShadowFocus: border,
        },
      },
    },
  },
};
const slots = useSlots();
const attrs = useAttrs();
const themeOverrides = merge(
  themeOverridesDefault,
  attrs['theme-overrides-cover'],
);
defineProps({
  emptytext: {
    type: String,
    default: 'no_data',
  },
});
</script>

<template>
  <n-data-table v-bind="attrs" :theme-overrides="themeOverrides">
    <template #empty>
      <ClientOnly>
        <BaseTableEmpty :emptytext="emptytext">
          <slot name="emptytext"></slot>
        </BaseTableEmpty>
      </ClientOnly>
    </template>
    <template v-for="(_item, key, i) in slots" :key="i" #[key]>
      <slot :name="key"></slot>
    </template>
  </n-data-table>
</template>
