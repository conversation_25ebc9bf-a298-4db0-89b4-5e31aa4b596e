<template>
  <div
    class="relative flex flex-shrink-0 items-center justify-center"
    :class="{
      'm-[3px]': player?.is_bot !== BOT_STATUS.REAL_USER && showBotIndicator,
    }"
  >
    <img
      class="self-center rounded-[4px]"
      :style="avatarStyle"
      :src="player?.avatar"
    />

    <!-- 机器人状态 -->
    <!-- <div
      v-if="player.is_bot !== BOT_STATUS.REAL_USER && showBotIndicator"
      :class="[
        'absolute stroke-current',
        player?.is_bot === BOT_STATUS.GOOD_BOT ? 'text-yellow-1' : 'text-red-2',
      ]"
    >
      <svg
        :height="indicatorSize"
        :width="indicatorSize"
        :viewBox="`0 0 ${indicatorSize} ${indicatorSize}`"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          :cx="indicatorSize / 2"
          :cy="indicatorSize / 2"
          :r="indicatorSize / 2 - 0.5"
          stroke-width="1"
          stroke-linecap="butt"
          stroke-dasharray="200 200"
        />
      </svg>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { BOT_STATUS, type Player } from '@/types/battles';

// const INDICATOR_PADDING = 6;

interface Props {
  player: Player;
  size?: number;
  showBotIndicator?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 30,
  showBotIndicator: true,
});

const avatarStyle = computed(() => ({
  height: `${props.size}px`,
  width: `${props.size}px`,
}));

// const indicatorSize = computed(() => props.size + INDICATOR_PADDING);
</script>
