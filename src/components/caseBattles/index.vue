<template>
  <div
    class="pt-[18px] pb-[12px] w-full flex flex-col items-center cursor-pointer gap-x-lg rounded-lg text-white bg-dark-3"
    :data-id="battle.arena_id"
    @mousedown="handleViewBattle"
  >
    <div class="px-[6px] sm:px-[16px] flex w-full items-center justify-between">
      <div class="flex items-center gap-md">
        <Tooltip
          :trigger="
            battle?.game_mode === GameModesType.UNO_REVERSE ? 'hover' : 'manual'
          "
        >
          <template #trigger>
            <svgo-case-battles-filled
              :filled="battle?.game_mode !== GameModesType.UNO_REVERSE"
              :class="[
                'focus:outline-none',
                battle?.game_mode !== GameModesType.UNO_REVERSE
                  ? ''
                  : 'text-purple-2',
                'size-[28px]',
              ]"
            />
          </template>
          <span>{{ $t('uno_reverse_mode') }}</span>
        </Tooltip>
        <div
          class="flex flex-col md:flex-row items-start md:items-center md:gap-[24px]"
        >
          <div class="flex items-center gap-md">
            <!-- 私人战斗 tooltip-->
            <Tooltip
              v-if="battle.is_private === 1"
              :trigger="battle?.is_private === 1 ? 'hover' : 'manual'"
            >
              <template #trigger>
                <base-icon
                  name="lock"
                  class="text-red-1 w-[13px] h-[16px] focus:outline-none"
                />
              </template>
              <span>{{ $t('private_battle') }}</span>
            </Tooltip>
            <div
              :class="{
                'font-bold uppercase whitespace-nowrap max-sm:text-sm sm:text-base': true,
                'text-purple-2':
                  battle?.game_mode === GameModesType.UNO_REVERSE,
              }"
            >
              {{ $t('standard_battle') }}
            </div>
          </div>
          <div class="text-purple-1 font-bold">
            {{ totalRounds }} {{ $t('round') }}
          </div>
        </div>
      </div>
      <div class="flex items-center">
        <div
          v-for="(_, index) in battle?.player_number"
          :key="index"
          class="flex items-center"
        >
          <Tooltip
            :trigger="battle.players[index]?.nickname ? 'hover' : 'manual'"
          >
            <template #trigger>
              <div
                :class="{
                  'rounded-md size-[32px] box-content border-dark-3 border-1': true,
                  'bg-[#262D49] flex justify-center items-center':
                    !battle.players[index],
                  'border-primary-400':
                    battle.players[index]?.uid === String(userId),
                }"
                :style="{
                  background: battle.players[index]
                    ? `url(${battle.players[index].avatar ?? ''}) no-repeat center / 100%`
                    : '',
                }"
              >
                <span v-if="!battle.players[index]"></span>
              </div>
            </template>
            <span class="text-white">
              {{ battle.players[index]?.nickname }}
            </span>
          </Tooltip>
          <svgo-battle
            v-if="index !== battle?.players?.length - 1"
            class="max-sm:mx-[4px] mx-[6px] mb-0 size-[20px]"
            filled
          />
        </div>
      </div>
    </div>
    <div
      class="mt-[14px] flex-1 w-full cursor-default bg-[#20263B] overflow-x-hidden whitespace-nowrap scrollbar-hidden"
      @mousedown.stop
    >
      <CaseBattlesBattleCaseList
        :cases="battle?.cases ?? []"
        :current-round="battle?.active_round!"
        :status="battle?.status ?? 0"
        :total-rounds="totalRounds"
      />
    </div>
    <div
      class="px-[6px] sm:px-[16px] mt-[12px] w-full flex items-center justify-between"
    >
      <div class="flex items-center gap-x-[7px]">
        <p class="text-[16px] max-sm:text-sm text-white/40">
          {{ $t('battle_value') }}
        </p>
        <p class="flex items-center gap-x-2">
          <Currency
            :amount="battleAmount"
            class="text-large max-sm:text-sm font-bold"
          />
        </p>
      </div>
      <n-button
        v-if="showButton"
        :class="[
          'rounded-md sm:min-w-[128px] h-[42px] max-sm:h-[36px]  max-sm:text-sm font-medium uppercase',
          actionTextColor,
          actionBg,
        ]"
        :color="actionColor"
        :ghost="actionGhost"
        @mousedown.stop="handleButtonClick()"
      >
        <span>{{ buttonText }}</span>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import {
  BattleStatusType,
  GameModesType,
  type BattleItem,
  type BattleCase,
} from '../../types/battles';
import { useSettingStore } from '../../stores/modules/setting';
import JoinChoose from '@/components/dialog/battles/JoinChoose.vue';

type Battle = BattleItem & {
  cases: BattleCase[];
};

interface Props {
  battle: Battle;
  showDismissButton?: boolean;
  showButton?: boolean;
  hideTimeoutBattle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showDismissButton: false,
  showButton: true,
  hideTimeoutBattle: true,
});

const emit = defineEmits([
  'battle:dismiss',
  'hide:battle',
  'battle:join',
  'battle:cancel',
  'battle:view',
  'battle:leave',
]);

const router = useRouter();
const userStore = useSettingStore();
const { battleApi } = useApi();
const { t } = useI18n();
const toast = useAppToast();
const dialog = useAppDialog();
const { openConfirm } = useDialogPromptsConfirm('caseBattlesPrompts');

const userId = computed(() => Number(userStore.userInfo?.uid ?? ''));

const totalRounds = computed(() =>
  props.battle?.cases?.reduce((acc, c) => acc + c.qty!, 0),
);

const BATTLE_CONSTANTS = {
  HIDE_BATTLE_TIMEOUT: 10, // 战斗隐藏超时时间(秒)
  DEFAULT_INTERVAL: 1000, // 默认定时器间隔
} as const;

const battleState = computed(() => ({
  isCreator: props.battle?.creator_uid === String(userId.value),
  isParticipant: props.battle.players.some(
    (p) => p?.uid === String(userId.value),
  ),
  isFull:
    props.battle?.player_number === props.battle?.players.length &&
    props.battle?.players.every((p) => p),
  isFinished: props.battle?.status === BattleStatusType.FINISHED,
  isWaiting: props.battle?.status === BattleStatusType.WAITING,
  isCancelled: props.battle?.status === BattleStatusType.CANCELLED,
}));

const canJoin = computed(
  () =>
    battleState.value.isWaiting &&
    !battleState.value.isCreator &&
    !battleState.value.isParticipant &&
    !battleState.value.isFull,
);

// 未开始且是参与者
const isWaitingParticipant = computed(
  () =>
    battleState.value.isParticipant &&
    battleState.value.isWaiting &&
    !battleState.value.isCreator,
);

const buttonState = computed(() => {
  const state = {
    text: t('view'),
    color: '',
    textColor: '',
    background: '',
    ghost: true,
  };

  type StateKey =
    | 'dismissFinished'
    | 'joinable'
    | 'waitingParticipant'
    | 'waitingCreator';

  const stateMap: Record<StateKey, Partial<typeof state>> = {
    // 已完成
    dismissFinished: {
      text: '',
      ghost: false,
    },
    // 加入
    joinable: {
      text: t('join_battle'),
      textColor: isWaitingParticipant.value ? 'text-white' : '',
      background: isWaitingParticipant.value ? '' : 'btn-gradient-primary',
      ghost: false,
    },
    // 等待中
    waitingParticipant: {
      text: t('exit'),
      textColor: 'text-white',
      background: 'btn-gradient-gray',
      ghost: false,
    },
    // 创建者等待中状态
    waitingCreator: {
      text: t('cancel_the_battle'),
      textColor: 'text-white',
      background: 'btn-gradient-gray',
      ghost: false,
    },
  };

  // 确定当前状态
  let currentState: StateKey | null = null;

  if (battleState.value.isFinished && props.showDismissButton) {
    currentState = 'dismissFinished';
  } else if (canJoin.value) {
    currentState = 'joinable';
  } else if (isWaitingParticipant.value) {
    currentState = 'waitingParticipant';
  } else if (
    !canJoin.value &&
    battleState.value.isCreator &&
    battleState.value.isWaiting
  ) {
    currentState = 'waitingCreator';
  }

  return currentState ? { ...state, ...stateMap[currentState] } : state;
});

const battleAmount = computed(() => {
  return props.battle?.battle_cost ?? 0;
});

const buttonText = computed(() => buttonState.value.text);
const actionColor = computed(() => buttonState.value.color);
const actionBg = computed(() => buttonState.value.background);
const actionTextColor = computed(() => buttonState.value.textColor);
const actionGhost = computed(() => buttonState.value.ghost);
const handleViewBattle = () => {
  navigateTo(`/case-battles/game/${props.battle.arena_id}`);
};

const handleBattleJoin = async (position: number) => {
  const res = await battleApi.joinBattle({
    arena_id: props.battle.arena_id,
    position: position + 1,
    total_price: props.battle.battle_cost,
  });
  if (res.data.value.code === 0) {
    emit('battle:join', {
      arena_id: props.battle.arena_id,
      data: res.data.value.data,
    });
    toast.success({
      content: t('join_successfully'),
    });
    dialog.hideByKey();
    router.push(`/case-battles/game/${res.data.value.data.arena_id}`);
  }
};

const cancelBattle = async () => {
  const res = await battleApi.cancelBattle({
    arena_id: props.battle.arena_id,
  });
  if (res.data.value.code === 0) {
    emit('battle:cancel', props.battle.arena_id);
    toast.success({
      content: t('battle_cancel_success'),
    });
  }
};

const leaveBattle = async () => {
  const res = await battleApi.cancelBattle({
    arena_id: props.battle.arena_id,
  });
  if (res.data.value.code === 0) {
    emit('battle:leave', props.battle.arena_id);
    toast.success({
      content: t('exit_successful'),
    });
  }
};

const checkJoin = async () => {
  const res = await battleApi.checkJoin({
    arena_id: props.battle.arena_id,
    total_price: props.battle.battle_cost,
  });
  if (res.data.value.code === 0) {
    return true;
  }
  return false;
};

const handleBattleJoinDialog = async () => {
  const canJoin = await checkJoin();
  if (!canJoin) {
    return;
  }
  dialog.open(JoinChoose, {
    style: {
      width: '568px',
    },
    contentProps: {
      battle: computed(() => props.battle),
    },
    title: t('choose_your_spot'),
    onConfirm: (data: { position: number }) => {
      if (data.position !== -1) {
        handleBattleJoin(data.position);
      }
    },
  });
};

const cancelBattleDialog = () => {
  openConfirm({
    promptKey: 'cancelBattle',
    title: t('confirm_cancel'),
    content: t('cancel_battle_confirm'),
    confirmText: t('confirm'),
    cancelText: t('do_not_cancel'),
    onConfirm: () => {
      cancelBattle();
    },
  });
};

const handleBattleExitDialog = () => {
  openConfirm({
    promptKey: 'leaveBattle',
    title: t('confirm_exit'),
    content: t('exit_battle_confirm'),
    confirmText: t('confirm'),
    onConfirm: () => {
      leaveBattle();
    },
  });
};

const handleButtonClick = () => {
  if (!checkLogin()) return;
  if (battleState.value.isFinished && props.showDismissButton) {
    emit('battle:dismiss', props.battle.arena_id);
    return;
  }

  if (canJoin.value) {
    handleBattleJoinDialog();
    return;
  }

  if (isWaitingParticipant.value) {
    handleBattleExitDialog();
    return;
  }

  if (
    !canJoin.value &&
    battleState.value.isCreator &&
    battleState.value.isWaiting
  ) {
    cancelBattleDialog();
    return;
  }

  handleViewBattle();
};

const useAutoHideBattle = () => {
  let timer: any = null;

  const startTimer = () => {
    if (timer) {
      clearInterval(timer);
    }

    if (props.battle?.ended_at && props.hideTimeoutBattle) {
      timer = setInterval(() => {
        const diff = dayjs(getLocalTimeAsUTCTimestamp()).diff(
          dayjs(props.battle!.ended_at),
          'seconds',
        );
        if (diff > BATTLE_CONSTANTS.HIDE_BATTLE_TIMEOUT) {
          emit('hide:battle', props.battle.arena_id);
          clearInterval(timer!);
          timer = null;
        }
      }, BATTLE_CONSTANTS.DEFAULT_INTERVAL);
    }
  };

  onUnmounted(() => {
    if (timer) {
      clearInterval(timer);
    }
  });

  return { startTimer };
};

const { startTimer } = useAutoHideBattle();

watch(
  () => [
    props.battle,
    battleState.value.isFinished,
    battleState.value.isCancelled,
  ],
  ([newBattle, isFinished, isCancelled]) => {
    if (newBattle && (isFinished || isCancelled)) {
      startTimer();
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="scss"></style>
