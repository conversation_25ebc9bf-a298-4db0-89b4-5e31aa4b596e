<template>
  <div class="flex justify-between flex-wrap my-2 items-center">
    <div class="bg-[#1f2028] rounded-full h-fit">
      <n-button
        class="text-sm"
        color="#fff"
        text-color="white"
        round
        @click="goPage('/case-battles')"
      >
        进行中
      </n-button>
      <n-button
        class="text-sm"
        color="transparent"
        text-color="#7D90CA"
        round
        @click="goPage('/case-battles/history')"
      >
        历史
      </n-button>
    </div>
    <div class="flex items-center justify-between max-md:w-full max-md:mt-2">
      <div>
        对战
        <b class="text-white">{{ battleNum }}</b>
      </div>
      <n-button
        class="ml-4 !font-bold text-sm rounded-[4px]"
        color="#edc254"
        text-color="#000"
        @click="goPage('/case-battles/create')"
      >
        创建对战
      </n-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  battleNum: {
    type: Number,
    default: 0,
  },
});
const $router = useRouter();
const goPage = (path: string) => {
  $router.push(path);
};
</script>
<style lang="scss" scoped></style>
