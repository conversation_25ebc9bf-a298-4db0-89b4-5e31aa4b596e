<template>
  <div
    class="relative grid h-full w-full auto-cols-fr grid-flow-col gap-xxs overflow-hidden"
  >
    <template v-for="(player, index) in players" :key="`player-${index}`">
      <div
        v-show="mobile && !calculating ? !isLoser[index] : true"
        :class="[
          'relative flex flex-col items-center justify-center max-sm:pb-[10%] pb-[7%]',
        ]"
      >
        <!-- 获胜者显示 -->
        <div
          v-if="isWinner[index]"
          class="absolute top-0 left-[50%] -translate-x-[50%] w-[236px] h-[48px] max-sm:h-[32px] flex items-center justify-center"
        >
          <span class="text-[18px] max-sm:text-lg font-bold">
            {{ $t('winning_value') }}
          </span>
          <BaseIcon
            name="winning"
            class="z-[-1] absolute top-0 left-[50%] -translate-x-[50%] w-full h-full"
          />
        </div>
        <Transition
          enter-active-class="transition-opacity duration-700"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="transition-opacity duration-700"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
          mode="out-in"
        >
          <div
            v-if="isWinner[index]"
            class="z-10 flex w-full flex-col items-center justify-center"
          >
            <CaseBattlesBattlePlayerAvatar
              :player="player"
              :size="60"
              :show-name="false"
            />
          </div>
        </Transition>

        <!-- 玩家结果显示 -->
        <div class="mt-[12px] flex w-full flex-col items-center justify-center">
          <Currency
            :class="[
              'relative z-10 font-bold transition-colors duration-1000 md:text-[22px]',
              {
                'text-white':
                  calculating && !isWinner[index] && !isLoser[index],
                'text-green-1': isWinner[index],
                'text-red-1': isLoser[index],
              },
            ]"
            isAnimated
            :animation-duration="1500"
            :amount="playerAmounts[index]"
            :size="mobile ? '18px' : '26px'"
          />
        </div>
        <!-- 转换全部 -->
        <Transition
          enter-active-class="transition-opacity duration-700"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="transition-opacity duration-700"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
          mode="out-in"
        >
          <div
            v-if="isWinner[index] && isWinnerSelf && enableConvert"
            class="mt-[21px] max-sm:mt-[5px] z-10 flex justify-center whitespace-nowrap p-0 items-center cursor-pointer"
          >
            <AmountButton
              class="max-sm:flex max-sm:flex-col max-sm:items-center"
              :currency-class="'max-sm:w-full max-sm:py-2 max-sm:rounded-t-md max-sm:bg-transparent'"
              :action-class="'max-sm:w-max max-sm:min-w-min max-sm:text-xs/none max-sm:h-[32px] max-sm:rounded-t-none max-sm:!rounded-sm'"
              :amount="convertPrice"
              :text="$t('convert_all')"
              theme="purple"
              isAnimated
              :loading="loading"
              @click="convertAll"
            />
          </div>
        </Transition>
      </div>
    </template>

    <!-- 重建/修改按钮 -->
    <Transition
      enter-active-class="transition-opacity duration-700"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-700"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
      mode="out-in"
    >
      <div
        v-if="
          (status === BattleStatusType.FINISHED ||
            status === BattleStatusType.TIEBREAKER) &&
          !calculating
        "
        class="absolute bottom-0 right-[50%] z-50 max-sm:w-max mb-[10px] flex max-sm:flex-wrap justify-center translate-x-[50%] gap-sm md:gap-[24px] whitespace-nowrap xl:mb-[48px]"
      >
        <n-button
          ghost
          class="max-sm:flex-1 max-sm:p-2 max-sm:min-w-min min-w-[273px] min-h-[42px] uppercase font-bold max-sm:text-sm/none"
          @click="emit('modifyBattle')"
        >
          {{ $t('modify_battle') }}
        </n-button>
        <AmountButton
          :amount="battleCost"
          :text="$t('recreate_battle')"
          currencyClass="!h-[42px] max-sm:min-w-min min-w-[104px]"
          actionClass="max-sm:flex-1 max-sm:min-w-min min-w-[169px] max-sm:text-sm/none"
          @click="emit('recreateBattle')"
        />
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import {
  // BattleModesType,
  BattleStatusType,
  type Player,
} from '~/types/battles';

interface Props {
  battleCost: number | string;
  isUnoReverse: boolean;
  mode: string | number;
  players: Player[];
  showInsufficientBalance: boolean;
  status: string | number;
  winnerPositions: number[];
  disableConverts: number[];
}

const props = withDefaults(defineProps<Props>(), {
  battleCost: 0,
  isUnoReverse: false,
  showInsufficientBalance: false,
});

const emit = defineEmits<{
  (e: 'modifyBattle'): void;
  (e: 'needsTieBreaker'): void;
  (e: 'recreateBattle'): void;
  (e: 'recreateBattleBlur'): void;
  (
    e: 'convertAll',
    item: any,
    price: number,
    callback: (loading?: boolean) => void,
  ): void;
}>();

const mobile = useIsMobile();
const settingStore = useSettingStore();
const userInfo = computed(() => settingStore.userInfo);
const calculating = ref(true);
const needsTieBreaker = ref(false);
const enableConvert = ref(true);
const loading = ref(false);

// 玩家金额
const playerAmounts = ref<number[]>(
  Array.from({ length: props.players.length }, () => 0),
);
const totalAmount = ref(0);

// 获胜者和失败者状态
const isWinner = ref<boolean[]>(Array(props.players.length).fill(false));
const isLoser = ref<boolean[]>(isWinner.value);

// 计算获胜者和金额分配
function calculateWinners() {
  totalAmount.value = playerAmounts.value.reduce(
    (sum, amount) => sum + amount,
    0,
  );
  // if (props.mode === BattleModesType.SHARED) {
  //   isWinner.value.fill(true);
  //   playerAmounts.value.fill(totalAmount.value / props.players.length);
  //   return;
  // }
  // 常规模式
  if (!props.isUnoReverse) {
    const maxIndex = playerAmounts.value.indexOf(
      Math.max(...playerAmounts.value),
    );
    if (
      playerAmounts.value.filter(
        (amount) => amount === playerAmounts.value[maxIndex],
      ).length > 1
    ) {
      needsTieBreaker.value = true;
      return;
    }
    playerAmounts.value.fill(0);
    isWinner.value[maxIndex] = true;
    playerAmounts.value[maxIndex] = totalAmount.value;
  } else {
    const minIndex = playerAmounts.value.indexOf(
      Math.min(...playerAmounts.value),
    );
    if (
      playerAmounts.value.filter(
        (amount) => amount === playerAmounts.value[minIndex],
      ).length > 1
    ) {
      needsTieBreaker.value = true;
      return;
    }
    playerAmounts.value.fill(0);
    isWinner.value[minIndex] = true;
    playerAmounts.value[minIndex] = totalAmount.value;
  }
}

// 转换全部
const convertAll = () => {
  emit(
    'convertAll',
    props.players
      .map((player) => player?.itemsData)
      .flat()
      .filter((e) => e?.is_claimed === 2),
    convertPrice.value,
    (_loading: boolean = false) => {
      loading.value = _loading;
    },
  );
};

// 是否是胜利者本人
const isWinnerSelf = computed(() => {
  return (
    isWinner.value.findIndex((won) => won) ===
    props.players.findIndex((player) => player?.uid === userInfo.value?.uid)
  );
});

// 转换金额
const convertPrice = computed(() => {
  const records =
    props.players.map((player) => player?.itemsData).flat() ??
    ([] as V1BattleRecord);
  return records.reduce((pre: any, cur: any) => {
    if (
      cur.is_claimed === 2 &&
      !props.disableConverts.includes(cur.backpack_id)
    ) {
      pre = Number(BigNumberCalc.add(pre, Number(cur.win_amount)));
    }
    return pre;
  }, 0);
});

watch(
  convertPrice,
  (newVal) => {
    if (newVal > 0) {
      enableConvert.value = true;
    } else {
      enableConvert.value = false;
    }
  },
  { immediate: true },
);

// 监听获胜者位置变化
watch(
  () => props.winnerPositions,
  () => {
    playerAmounts.value.fill(0);
    props.winnerPositions.forEach((position) => {
      isWinner.value[position] = true;
      playerAmounts.value[position] =
        totalAmount.value / props.winnerPositions.length;
    });
    calculating.value = false;
    isLoser.value = isWinner.value.map((won) => !won);
  },
);
// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    const amounts = props.players.map(
      (player) =>
        player?.itemsData?.reduce(
          (sum: number, item: any) =>
            Number(BigNumberCalc.add(sum, Number(item.pawn_price))),
          0,
        ) || 0,
    );
    playerAmounts.value = amounts;

    setTimeout(() => {
      calculateWinners();
      if (needsTieBreaker.value) {
        emit('needsTieBreaker');
        return;
      }
      calculating.value = false;
      isLoser.value = isWinner.value.map((won) => !won);
    }, 3000);
  });
});
</script>
