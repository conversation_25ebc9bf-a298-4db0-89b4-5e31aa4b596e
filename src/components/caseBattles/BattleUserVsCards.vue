<template>
  <div
    :class="[
      'grid w-full auto-cols-fr grid-flow-col gap-md overflow-hidden  xl:h-[64px]',
    ]"
  >
    <div
      v-for="(_, index) in totalNumberOfPlayers"
      :key="`player-${index}`"
      :class="[
        'relative flex flex-1 items-center justify-center bg-dark-3',
        isStarted ? 'rounded-t-[8px]' : 'rounded-[8px]',
      ]"
    >
      <div
        v-if="players[index]"
        :class="[
          'flex h-full w-full flex-1 flex-col justify-center overflow-hidden py-[7px] xl:flex-row xl:items-center xl:gap-md max-sm:px-0 px-xl',
          { 'border border-primary-400': players[index].uid === userInfo?.uid },
          isStarted ? 'rounded-t-[8px]' : 'rounded-[8px]',
        ]"
      >
        <BattlePlayerAvatar :player="players[index]" />

        <div
          class="mt-xxs truncate px-lg xl:order-last xl:mt-0 xl:px-0 text-center max-w-full"
          :user-id="players[index].uid"
        >
          <n-ellipsis
            class="block truncate text-center w-full font-bold text-lg max-sm:text-sm"
          >
            {{ players[index].nickname || '' }}
          </n-ellipsis>
        </div>

        <CaseBattlesUserRankBadge
          v-if="players[index]?.user_type !== 3"
          class="ml-[6px] mr-[8px] mt-[7px] self-center xl:mt-0"
          :lvl="players[index]?.level ?? 0"
        />
        <CaseBattlesTag
          v-else
          class="sm:mt-[7px] sm:mr-[8px] self-center !p-xs font-bold xl:mt-0"
          :color="
            players[index].is_bot === BOT_STATUS.GOOD_BOT ? 'yellow' : 'red'
          "
        >
          <CaseBattlesHeading
            size="sm"
            class="flex items-center justify-center font-bold h-[24px]"
          >
            <span>{{ t('bot') }}</span>
          </CaseBattlesHeading>
        </CaseBattlesTag>
      </div>

      <Text
        v-else
        class="px-[12px] max-sm:text-xs/none max-sm:py-2 text-center text-purple-1"
        :size="breakpoints ? 'small' : 'medium'"
        bold
      >
        {{ t('waiting_for_a_player') }}
      </Text>

      <svgo-battles-vs
        v-if="index > 0"
        class="absolute left-0 translate-x-[-58%] text-purple-1 w-[54px] h-[54px] max-sm:w-[32px] max-sm:h-[32px] rounded-full"
        filled
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import BattlePlayerAvatar from './BattlePlayerAvatar.vue';
import { BOT_STATUS, BattleStatusType, type Player } from '@/types/battles';
import { useSettingStore } from '~/stores/modules/setting';

interface Props {
  mode: number;
  players: Record<number, Player>;
  totalNumberOfPlayers: number;
  status: number;
}

const props = withDefaults(defineProps<Props>(), {});
const breakpoints = useIsMobile();
const { t } = useI18n();
const settingStore = useSettingStore();

const userInfo = computed(() => settingStore.userInfo);

const isStarted = computed(() => {
  return [
    BattleStatusType.WAITING_FOR_EOS,
    BattleStatusType.RUNNING,
    BattleStatusType.TIEBREAKER,
    BattleStatusType.FINISHED,
  ].includes(props.status);
});
</script>
