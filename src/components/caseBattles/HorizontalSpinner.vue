<script setup>
const props = defineProps({
  autoCenter: <PERSON>olean,
  bgGlow: {
    type: String,
    default: '',
  },
  minOffsetPercentage: {
    type: Number,
    default: 0.9,
  },
  spinnerConfig: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['playEndSound', 'playTickSound', 'spinEnded']);

const wrapperRef = ref(null);
const wheelRef = ref(null);
const wheelStyle = shallowRef(null);
const cardSize = shallowRef(120);
const middleIndex = ref(0);
const startTime = shallowRef(0);
const elapsedTime = shallowRef(0);
const measurementComplete = ref(false);

// 计算属性
const spinnerItems = computed(() => props.spinnerConfig.spinnerItems);
const selectedPosition = computed(
  () => props.spinnerConfig.selectedItemPosition,
);
const spinEnded = computed(() => props.spinnerConfig.spinEnded);
const animation = computed(() => props.spinnerConfig.animation);
const isSpinning = computed(() => props.spinnerConfig.isSpinning);
const remainingTime = computed(() =>
  animation.value ? Math.max(animation.value.time - elapsedTime.value, 0) : 0,
);

function measureCardSize() {
  if (!wheelRef.value) return;

  const wheelItems = wheelRef.value.querySelectorAll('.wheel-items');
  if (wheelItems.length === 0) return;

  let totalWidth = 0;
  wheelItems.forEach((item) => {
    totalWidth += item.getBoundingClientRect().width;
  });

  const averageWidth = totalWidth / wheelItems.length;
  cardSize.value = averageWidth;
  measurementComplete.value = true;
}

// 监听大小变化
useResizeObserver(wheelRef, (_entries) => {
  if (startTime.value) {
    elapsedTime.value = new Date().getTime() - startTime.value;
  }
  measureCardSize();
  updateMiddlePosition();
  handleSpinEnd();
});

const { pause, resume, isActive } = useRafFn(updateMiddlePosition, {
  immediate: false,
});

// 更新中间位置
function updateMiddlePosition() {
  if (!wrapperRef.value || !wheelRef.value) return;

  const center =
    wrapperRef.value.getBoundingClientRect().left +
    wrapperRef.value.clientWidth / 2;
  const offset = Math.abs(wheelRef.value.getBoundingClientRect().left - center);
  const newMiddleIndex = Math.floor(offset / cardSize.value);

  if (newMiddleIndex !== middleIndex.value) {
    middleIndex.value = newMiddleIndex;
  }

  !props.spinnerConfig.isSpinning && pause();
  !isActive.value && resume();
}

// 监听中间索引变化
watch(middleIndex, () => {
  if (!props.spinnerConfig.isMuted) {
    emit('playTickSound', middleIndex.value);
  }
});

// 处理转换
const { start: startTransition } = useTimeoutFn(
  handleTransitionUpdate,
  remainingTime,
  {
    immediate: false,
  },
);

function handleTransitionUpdate(position, done) {
  wheelStyle.value = {
    'transition-duration': '1500ms',
    transform: `translate3d(${-position}px, 0, 0)`,
  };

  done();
}

function handleTransitionEnd(position) {
  startTransition(position, () => {
    if (!props.spinnerConfig.isMuted) {
      emit('playEndSound');
    }
    emit('spinEnded');
  });
  updateMiddlePosition();
}

// 使用spinner动画
const { getSpinnerAnimationPositions } = useSpinnerConfig();

// 获取动画位置
function getAnimationPositions() {
  return getSpinnerAnimationPositions({
    cardSize: cardSize.value,
    minOffsetPercentage: props.minOffsetPercentage,
    selectedItemPosition: selectedPosition.value,
    hash: props.spinnerConfig.hash,
  });
}

// 设置初始位置
async function setInitialPosition(startingPosition) {
  await nextTick(() => {
    wheelStyle.value = {
      'transition-timing-function': '',
      'transition-duration': '',
      transform: `translate3d(${startingPosition}px, 0, 0)`,
    };
  });
}

// 设置最终位置
async function setFinalPosition(landingPosition, landingPositionWithOffset) {
  await nextTick(() => {
    wheelStyle.value = {
      'transition-timing-function': `cubic-bezier(0, ${animation.value.bezierFirstSpeed}, ${animation.value.bezierSecondSpeed}, 1)`,
      'transition-duration': `${remainingTime.value}ms`,
      transform: `translate3d(${-landingPositionWithOffset}px, 0, 0)`,
    };
    handleTransitionEnd(
      props.autoCenter ? landingPosition : landingPositionWithOffset,
    );
  });
}

// 开始旋转
async function startSpin() {
  if (!measurementComplete.value) {
    measureCardSize();
  }
  const { landingPosition, landingPositionWithOffset, startingPosition } =
    getAnimationPositions();
  if (!startTime.value) {
    startTime.value = new Date().getTime();
  }
  await setInitialPosition(startingPosition);
  await setFinalPosition(landingPosition, landingPositionWithOffset);
}

// 监听旋转状态
watch(isSpinning, () => {
  if (isSpinning.value) {
    if (!measurementComplete.value) {
      measureCardSize();
    }

    startSpin();
    return;
  }
  startTime.value = 0;
  elapsedTime.value = 0;
});

// 处理旋转结束
function handleSpinEnd() {
  if (spinEnded.value) {
    const { landingPosition, landingPositionWithOffset } =
      getAnimationPositions();

    wheelStyle.value = {
      transform: `translate3d(-${props.autoCenter ? landingPosition : landingPositionWithOffset}px, 0, 0)`,
    };
    return;
  }

  if (isSpinning.value) {
    startSpin();
  }
}
</script>

<template>
  <div
    ref="wrapperRef"
    :class="[
      'relative mx-auto my-0 flex h-full w-full items-center justify-center overflow-hidden',
      bgGlow,
    ]"
  >
    <div
      ref="wheelRef"
      :style="wheelStyle"
      class="flex h-full transform-gpu will-change-transform"
    >
      <div
        v-for="(item, index) in spinnerItems"
        :key="index"
        class="flex h-full wheel-items"
      >
        <slot
          :item="item"
          :index="index"
          :selectedItemPosition="selectedPosition"
          :spinEnded="spinEnded"
          :isMiddleCard="middleIndex === index"
          :isSelectedItem="selectedPosition === index"
        />
      </div>
    </div>
  </div>
</template>
