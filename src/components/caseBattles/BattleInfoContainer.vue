<template>
  <div
    class="relative flex w-full flex-wrap items-center justify-between gap-0 overflow-hidden rounded-lg bg-dark-3 max-sm:gap-lg py-[20px] max-sm:py-[50px] px-xl max-sm:px-[10px]"
  >
    <!-- 标题 -->
    <div
      class="absolute left-1/2 top-0 -translate-x-1/2 min-w-[230px] h-[36px] flex items-center justify-center"
    >
      <svgo-rectangles
        :class="[
          'absolute left-0 top-0 size-full z-[-1]',
          !isUnoReverse ? 'text-purple-1' : 'text-purple-2 cursor-pointer',
        ]"
      />
      <div class="flex items-center gap-md text-[#0A0D14]">
        <Tooltip :trigger="isUnoReverse ? 'hover' : 'manual'">
          <template #trigger>
            <svgo-case-battles-filled
              :class="['size-[26px] focus:outline-none']"
            />
          </template>
          {{ $t('uno_reverse_mode') }}
        </Tooltip>
        <div class="font-semibold">{{ $t('standard_battle') }}</div>
      </div>
    </div>
    <!-- 左侧信息 -->
    <div
      class="flex flex-row items-center gap-sm sm:flex-1 sm:flex-col sm:items-start sm:gap-md mb-5 xl:mb-0"
    >
      <div class="sm:mb-[4px] mb-0 max-sm:text-md font-bold uppercase">
        {{ $t('battle_value') }}
      </div>
      <Currency
        class="max-sm:text-lg text-[24px] font-bold text-primary-400 gap-[6px]"
        :amount="battleCost"
        :size="'26px'"
      />
    </div>

    <!-- 中间箱子列表 -->
    <CaseBattlesBattleCaseDisplay
      class="order-last w-full xl:order-none xl:max-w-[726px]"
      :case-list="caseList"
      :selected-index="selectedIndex"
    />

    <!-- 右侧信息 -->
    <div
      class="sm:flex-col gap:sm sm:gap-0 sm:items-end flex-1 flex-row uppercase flex items-center justify-end gap-md font-bold mb-5 xl:mb-0"
    >
      <!-- 战斗状态 -->
      <template v-if="finished">
        <Text bold>
          {{ $t('finished') }}
        </Text>
      </template>
      <template v-else-if="selectedIndex < 0">
        <span class="whitespace-nowrap">
          {{ $t('round') }}
        </span>
        <span
          class="sm:mt-sm text-primary-400 text-[24px] max-sm:text-lg font-bold"
        >
          {{ caseList.length }}
        </span>
      </template>
      <template v-else>
        <span class="whitespace-nowrap">
          {{ $t('round') }}
        </span>
        <Text
          class="whitespace-nowrap sm:mt-sm text-[24px] max-sm:text-md text-primary-400"
          bold
        >
          <i18n-t keypath="a_of_b" scope="global">
            <template #a>
              <span>{{ selectedIndex + 1 }}</span>
            </template>
            <template #b>
              <span>{{ caseList.length }}</span>
            </template>
          </i18n-t>
        </Text>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  battleCost: number | string;
  finished: boolean;
  private: boolean;
  shared: boolean;
  isUnoReverse: boolean;
  caseList: any[];
  selectedIndex: number;
}

withDefaults(defineProps<Props>(), {
  battleCost: 0,
  finished: false,
  private: false,
  shared: false,
  sponsored: 0,
  isUnoReverse: false,
  caseList: () => [],
  selectedIndex: -1,
});
</script>
