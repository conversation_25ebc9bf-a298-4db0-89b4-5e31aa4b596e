<template>
  <div
    ref="containerRef"
    v-dragscroll
    class="flex pt-[22px] pb-[17px] items-center gap-[24px] overflow-hidden rounded-b bg-dark-4 px-[13px] xl:rounded"
    @dragscrollstart="onDragStart"
    @dragscrollmove="disableAutoScroll"
    @dragscrollend="onDragEnd"
  >
    <template v-for="(caseItem, index) in caseList" :key="`case_${index}`">
      <div
        ref="casesRef"
        class="flex shrink-0 cursor-pointer items-center gap-3"
        :class="{
          'mx-lg': selectedIndex === index,
        }"
        @click.stop="goDetail(caseItem)"
      >
        <!-- 箱子图片 -->
        <div class="flex items-center justify-center">
          <Tooltip trigger="hover">
            <template #trigger>
              <div class="relative">
                <MediaDisplay
                  class="pointer-events-none select-none object-cover"
                  :class="[
                    'pointer-events-none select-none w-[72px] h-[61px]',
                    selectedIndex === index || selectedIndex < 0
                      ? 'opacity-100'
                      : 'opacity-30',
                  ]"
                  :src="`${caseItem.image_url}`"
                />
              </div>
            </template>
            <span class="text-white">
              {{ caseItem.case_name }}
            </span>
          </Tooltip>
        </div>

        <!-- 选中箱子的信息 -->
        <div
          v-if="selectedIndex > -1 && selectedIndex === index"
          class="flex flex-col gap-md"
        >
          <Text bold>
            {{ caseItem.case_name }}
          </Text>
          <Currency
            class="font-bold text-[16px] text-primary-400"
            :amount="caseItem.total_price"
            :size="'18px'"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { BattleCase } from '~/types/battles';

interface Props {
  caseList: BattleCase[];
  selectedIndex: number;
}

const props = defineProps<Props>();

const CARD_MARGIN = 96;
const SELECTED_CARD_MARGIN = 24;

const casesRef = ref<HTMLElement[]>([]);
const containerRef = ref<HTMLElement>();

// 自动滚动控制
const { isAutoScrollEnabled, isDragging, disableAutoScroll, enableAutoScroll } =
  useCasesAutoScrollControl();

// 选中的箱子元素
const selectedCase = computed(() => {
  if (casesRef.value) {
    const index =
      props.selectedIndex > -1
        ? props.selectedIndex
        : casesRef.value?.length + props.selectedIndex;
    return casesRef.value[index];
  }
  return undefined;
});

// 容器宽度
const { width: containerWidth } = useElementSize(containerRef);
const { width: selectedWidth } = useElementSize(selectedCase);

// 计算滚动位置
const startOffset = computed(() =>
  props.selectedIndex > -1 ? containerWidth.value / 2 : 0,
);
const endOffset = computed(() =>
  props.selectedIndex > -1
    ? selectedWidth.value / 2 + SELECTED_CARD_MARGIN
    : CARD_MARGIN,
);
const scrollPosition = computed(
  () => CARD_MARGIN * props.selectedIndex + endOffset.value - startOffset.value,
);

// 监听滚动位置变化
watch(
  scrollPosition,
  (position) => {
    if (position != null && isAutoScrollEnabled.value) {
      containerRef.value?.scrollTo({
        left: position,
        behavior: 'smooth',
      });
    }
  },
  { immediate: true },
);

// 拖动控制
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = () => {
  enableAutoScroll();
  setTimeout(() => {
    isDragging.value = false;
  }, 0);
};
const goDetail = (caseItem: BattleCase) => {
  window.open(`/cases/${caseItem.slug}`, '_blank');
};
</script>
