<template>
  <div
    class="case-card group relative flex min-h-[216px] cursor-pointer flex-col items-center justify-between rounded-[8px] p-md"
    :class="[selected ? selectedClass : unselectedBg]"
    @click="onSelect"
  >
    <!-- 查看宝箱 -->
    <div
      class="invisible group-hover:visible z-100 absolute top-4 left-4 justify-center items-center"
      @click.stop="goDetail"
    >
      <svgo-info-outlined
        class="text-[20px] text-purple-1"
      ></svgo-info-outlined>
    </div>

    <!-- 删除宝箱事件 -->
    <div
      v-if="showClose && quantity"
      class="invisible group-hover:visible z-100 absolute top-[12px] right-[12px] justify-center items-center"
      @click.stop="updateQuantity(0)"
    >
      <svgo-close class="text-[20px] text-purple-1"></svgo-close>
    </div>
    <div class="mb-[8px] sm:mb-[15px] w-full flex flex-col items-center">
      <MediaDisplay
        :src="`${caseItem.image_url}`"
        class="object-contain size-[111px] sm:size-[131px] p-[10px] sm:p-[20px]"
        :alt="caseItem.case_name"
      />
      <Text
        class="mt-[-2px] w-full text-center"
        ellipsis
        size="large"
        bold
        :tooltip-text="caseItem.case_name"
      >
        {{ caseItem.case_name }}
      </Text>
    </div>

    <!-- 价格与数量-->
    <div class="w-full text-center">
      <Currency
        :amount="caseItem?.total_price"
        class="mb-[16px] sm:mb-[24px] !items-center"
      />

      <div
        v-if="!selected"
        class="m-auto sm:mb-[24px] sm:w-[174px] h-[42px] flex items-center justify-center rounded bg-[#272F4A]"
      >
        <Text bold class="text-white">
          {{ t('add') }}
        </Text>
      </div>
      <!-- 输入框 -->
      <div class="sm:px-[12px]">
        <n-input-number
          v-if="selected"
          :value="quantity"
          button-placement="both"
          class="sm:mb-[24px]"
          size="large"
          :show-button="false"
          :step="0"
          :input-props="{ readonly: true }"
          :update-value-on-input="false"
          @click.stop
          @update:value="updateQuantity"
        >
          <template #prefix>
            <div
              class="bg-[#2E3757] group-hover:bg-[#5064A0] w-[42px] h-[42px] rounded-[4px] flex items-center justify-center cursor-pointer"
              @click="emit('decrement', props.caseItem)"
            >
              <svgo-subtract class="text-[#D8D8D8]"></svgo-subtract>
            </div>
          </template>
          <template #suffix>
            <div
              class="bg-[#2E3757] group-hover:bg-[#5064A0] w-[42px] h-[42px] rounded-[4px] flex items-center justify-center cursor-pointer"
              @click="emit('increment', props.caseItem)"
            >
              <svgo-add class="text-[#D8D8D8]"></svgo-add>
            </div>
          </template>
        </n-input-number>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
type V1CasesItemWithQuantity = V1CasesItem & {
  quantity: number;
};

interface Props {
  caseItem: V1CasesItemWithQuantity;
  unselectedBg?: string;
  selected?: boolean;
  selectedClass?: string;
  quantity: number;
  showClose?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  quantity: 0,
  showClose: false,
  selectedClass: 'bg-dark-3',
  unselectedBg: 'bg-dark-2',
});
const emit = defineEmits<{
  (e: 'select', value: V1CasesItemWithQuantity): void;
  (e: 'update:quantity', item: V1CasesItemWithQuantity, quantity: number): any;
  (e: 'go-detail', id: string): void;
  (e: 'decrement', item: V1CasesItemWithQuantity): void;
  (e: 'increment', item: V1CasesItemWithQuantity): void;
}>();

const { t } = useI18n();

function goDetail() {
  if (!props.caseItem.slug) return;
  emit('go-detail', props.caseItem.slug);
}

function onSelect() {
  emit('select', props.caseItem);
}

function updateQuantity(value: number | null) {
  emit('update:quantity', props.caseItem, value || 0);
}
</script>

<style lang="scss" scoped>
.case-card {
  background: rgba(32, 38, 59, 1);
  &:hover {
    background: rgba(80, 100, 160, 0.4) !important;
  }
}
</style>
