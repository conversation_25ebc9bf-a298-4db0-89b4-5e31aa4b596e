<template>
  <div
    class="flex items-center justify-between bg-dark-3 p-3 sm:p-6 rounded-lg flex-wrap gap-2"
    :class="customClass"
  >
    <div class="flex items-center gap-[8px]">
      <svgo-case-battles-filled class="size-[28px]" filled />
      <span>
        {{ $t(title) }}
      </span>
    </div>
    <div class="flex gap-[8px]">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="[
          'flex-1 text-center py-2 px-4 cursor-pointer text-white  box-border bg-dark-2 border-1 border-dark-2  rounded-[4px]',
          { '!border-primary-400 !text-primary-400': currentTab === tab },
        ]"
        @click="$emit('change-tab', tab)"
      >
        {{ tab }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true,
    },
    tabs: {
      type: Array,
      required: true,
    },
    currentTab: {
      type: String,
      required: true,
    },
    customClass: {
      type: String,
      default: '',
    },
  },
  emits: ['change-tab'],
};
</script>
