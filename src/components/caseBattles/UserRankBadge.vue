<template>
  <div
    v-if="rankName"
    :class="[
      'relative flex h-[24px] shrink-0 items-center gap-sm rounded px-[6px] py-[2px] leading-tight',
      `text-rank-${rankColor}`,
      `bg-rank-${rankColor}/10`,
      { 'flex-row-reverse': imagePosition === 'RIGHT' },
    ]"
  >
    <img
      v-if="config?.level_url"
      :src="config?.level_url"
      class="size-[20px]"
      alt="等级图标"
    />
    <Text
      v-if="lvl"
      :class="['pointer-events-none', `text-rank-${rankColor}`]"
      bold
    >
      {{ lvl }}
    </Text>
  </div>
  <div v-else class="flex items-center">
    <Text size="small" class="pointer-events-none text-light-grey-2" bold>
      0
    </Text>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/modules/app';

// 等级名称
const RANK_NAMES = {
  IRON: 'iron',
  BRONZE: 'bronze',
  SILVER: 'silver',
  GOLD: 'gold',
  PLATINUM: 'platinum',
  DIAMOND: 'diamond',
  MASTER: 'master',
  EXTRAORDINARY: 'extraordinary',
} as const;

// 等级阈值
const LEVEL_THRESHOLDS = {
  [RANK_NAMES.IRON]: 0,
  [RANK_NAMES.BRONZE]: 20,
  [RANK_NAMES.SILVER]: 40,
  [RANK_NAMES.GOLD]: 60,
  [RANK_NAMES.PLATINUM]: 80,
  [RANK_NAMES.DIAMOND]: 100,
  [RANK_NAMES.MASTER]: 120,
  [RANK_NAMES.EXTRAORDINARY]: 140,
} as const;

interface Props {
  imagePosition?: 'LEFT' | 'RIGHT';
  lvl?: number;
}

const props = withDefaults(defineProps<Props>(), {
  imagePosition: 'LEFT',
  lvl: 0,
});

const appStore = useAppStore();
const levelConfig = computed(() => appStore.levelConfig);

const config = computed(() => {
  if (!props.lvl || !levelConfig.value?.length) return null;

  return (
    levelConfig.value.find(
      (cfg: V1LevelConfigItem) => props.lvl === cfg.level_id,
    ) ?? null
  );
});

const rankName = computed(() => {
  return (
    config.value?.level_name?.toLowerCase() ?? calculateRankName(props.lvl)
  );
});

const rankColor = computed(() => rankName.value || '');

function calculateRankName(level?: number): string {
  if (!level) return '';
  const thresholds = Object.entries(LEVEL_THRESHOLDS).sort(
    (a, b) => b[1] - a[1],
  );
  for (const [rank, threshold] of thresholds) {
    if (level > threshold) return rank;
  }

  return RANK_NAMES.IRON;
}
</script>

<style lang="scss">
:root {
  --rank-iron: #818792;
  --rank-bronze: #a47963;
  --rank-silver: #c7cfe5;
  --rank-gold: #dca85a;
  --rank-platinum: #9066b9;
  --rank-diamond: #39b0c8;
  --rank-master: #ffa1ae;
  --rank-extraordinary: #ff86eb;
}

.text-rank {
  &-iron {
    color: var(--rank-iron);
  }
  &-bronze {
    color: var(--rank-bronze);
  }
  &-silver {
    color: var(--rank-silver);
  }
  &-gold {
    color: var(--rank-gold);
  }
  &-platinum {
    color: var(--rank-platinum);
  }
  &-diamond {
    color: var(--rank-diamond);
  }
  &-master {
    color: var(--rank-master);
  }
  &-extraordinary {
    color: var(--rank-extraordinary);
  }
}

.bg-rank {
  &-iron\/10 {
    background-color: color-mix(in srgb, var(--rank-iron) 10%, transparent);
  }
  &-bronze\/10 {
    background-color: color-mix(in srgb, var(--rank-bronze) 10%, transparent);
  }
  &-silver\/10 {
    background-color: color-mix(in srgb, var(--rank-silver) 10%, transparent);
  }
  &-gold\/10 {
    background-color: color-mix(in srgb, var(--rank-gold) 10%, transparent);
  }
  &-platinum\/10 {
    background-color: color-mix(in srgb, var(--rank-platinum) 10%, transparent);
  }
  &-diamond\/10 {
    background-color: color-mix(in srgb, var(--rank-diamond) 10%, transparent);
  }
  &-master\/10 {
    background-color: color-mix(in srgb, var(--rank-master) 10%, transparent);
  }
  &-extraordinary\/10 {
    background-color: color-mix(
      in srgb,
      var(--rank-extraordinary) 10%,
      transparent
    );
  }
}
</style>
