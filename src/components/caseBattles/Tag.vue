<template>
  <div
    :class="[
      'flex items-center px-sm',
      tagVariantClass,
      tagColorClass,
      roundedClass,
      { ellipsis: useEllipsis },
    ]"
  >
    <slot name="before"></slot>
    <Text
      :size="size"
      :ellipsis="useEllipsis"
      color="warning"
      :class="['px-xs', { 'font-bold': variant === 'secondary' }]"
    >
      <slot></slot>
    </Text>
    <slot name="after" class="ml-md"></slot>
  </div>
</template>

<script setup lang="ts">
import { TagColor, TagVariant, TagSize } from '@/types/battles';

interface Props {
  color?: 'yellow' | 'red' | 'green' | 'grey';
  rounded?: boolean;
  size?: TagSize;
  useEllipsis?: boolean;
  variant?: TagVariant;
}

const props = withDefaults(defineProps<Props>(), {
  color: TagColor.GREY,
  rounded: false,
  size: TagSize.MEDIUM,
  useEllipsis: false,
  variant: TagVariant.PRIMARY,
});

const tagVariantClass = computed(() => `tag-${props.variant}`);
const roundedClass = computed(() =>
  props.rounded ? 'rounded-full' : 'rounded',
);
const tagColorClass = computed(() => `${tagVariantClass.value}-${props.color}`);
</script>

<style scoped lang="scss">
.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
