<template>
  <div class="sm:px-[6px]">
    <div class="flex w-full justify-between mb-5 flex-wrap items-center">
      <FilterForm
        ref="form"
        class="flex justify-between items-center"
        :form-schema="formSearchSchema"
        :form-state="formSearchState"
      >
        <FilterComponetsFormItem
          v-for="item in formSearchSchema"
          :key="item.key"
          :field="item"
          :field-name="item.key"
          :model-value="formSearchState[item.key]"
          @update:model-value="(value) => handleSearhcUpdate(item.key, value)"
        />
      </FilterForm>
      <FilterForm
        ref="form"
        class="max-sm:mt-[10px] flex justify-between items-center gap-2"
        :form-schema="formSchema"
        :form-state="formState"
      >
        <FilterComponetsFormItem
          v-for="item in formSchema"
          :key="item.key"
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          @update:model-value="(value) => handleUpdate(item.key, value)"
        />
      </FilterForm>
    </div>
    <n-spin :show="loading">
      <n-infinite-scroll
        :distance="300"
        content-class="min-h-[500px] sm:min-h-[587px] max-h-[50vh] grid"
        @load="handleLoad"
      >
        <div
          class="cases-grid overflow-y-auto h-max grid grid-cols-[repeat(auto-fill,minmax(140px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))] gap-[6px]"
        >
          <CaseBattlesCaseCard
            v-for="item in caseItems"
            :key="item.slug"
            :caseItem="item"
            :showClose="true"
            :quantity="
              caseSelections.find((i) => i.slug === item.slug)?.quantity ?? 0
            "
            :selected="!!caseSelections.find((i) => i.slug === item.slug)"
            :selectedClass="'bg-dark-2'"
            @select="increment(item)"
            @decrement="decrement(item)"
            @increment="increment(item)"
            @update:quantity="
              (item, quantity) => updateQuantity(item, quantity)
            "
            @go-detail="goDetail"
          />
          <template v-if="isPageLoading">
            <SkeletonCase v-for="item in pagination.page_size" :key="item" />
          </template>
        </div>
        <Empty
          v-if="!caseItems?.length && !loading"
          :msg="$t('no_corresponding_case_found')"
          class="mb-auto pb-10"
          column
        ></Empty>
      </n-infinite-scroll>
    </n-spin>
    <div class="mt-7 flex flex-wrap items-center justify-between">
      <div class="mr-lg flex">
        <div class="text-purple-1">
          {{ $t('cases_selected') }}
          <span class="text-white">{{ totalSelectedCases }}</span>
        </div>
      </div>
      <div class="flex items-center text-lg flex-wrap gap-md">
        <span>{{ $t('total_cost') }}</span>
        <Currency class="mr-md" :amount="totalValue" :size="'22px'" />
        <n-button
          type="primary"
          class="btn-gradient-primary min-w-[149px] min-h-[42px] sm:flex items-center justify-center rounded font-bold text-dark-5"
          @click.stop="submitCases"
        >
          {{ $t('add_cases') }}
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { createFormSchema } from '~/models/casesFilterModel';
import type { PaginatorType } from '~/types/base';

const dialogRef = inject('dialogRef') as UseDialogOptions;

type V1CasesItemWithQuantity = V1CasesItem & {
  quantity: number;
};

interface Props {
  selectedCases: V1CasesItemWithQuantity[];
}

const props = withDefaults(defineProps<Props>(), {
  selectedCases: () => [],
});

const { formSearchSchema, formSchema, formState, formSearchState } =
  createFormSchema();
const { casesApi } = useApi();
const {
  caseSelections,
  totalValue,
  totalSelectedCases,
  updateQuantity,
  increment,
  decrement,
  reset,
} = useBattleSelectedCases();
caseSelections.value = props.selectedCases;

const pagination = reactive<PaginatorType>({
  page_size: 20,
  page: 1,
  total: 0,
});

// 接口参数
const params = computed(() => {
  const priceRange = formState.price_range.split(',');
  return {
    page: pagination.page,
    page_size: pagination.page_size,
    price_min: Number(priceRange[0]),
    price_max: Number(priceRange[1]),
    ...formSearchState,
    order_by: formState.sort,
    type: 1,
  };
});

// 获取列表
const getLists = async () => {
  noMore.value = true;
  try {
    loading.value = true;
    const { data } = await casesApi.getCasesList(params.value);
    if (data.value?.code === 0) {
      const dataValue = data.value?.data;
      caseItems.value = [
        ...(caseItems.value ?? []),
        ...(dataValue?.list ?? []),
      ];
      pagination.total = dataValue?.total || 0;
      noMore.value = caseItems.value.length >= pagination.total;
    }
  } finally {
    loading.value = false;
    isPageLoading.value = false;
  }
};
const loading = ref(false);
const isPageLoading = ref(true);
const noMore = ref(false);

const caseItems = ref<any[]>();

// 筛选条件
const handleUpdate = (key: string, value: any) => {
  formState[key] = value;
  resetReload();
};

// 搜索
const handleSearhcUpdate = (key: string, value: any) => {
  formSearchState[key] = value;
  resetReload();
};

const resetReload = () => {
  pagination.page = 1;
  caseItems.value = [];
  reset();
  getLists();
};

const handleLoad = () => {
  if (noMore.value) return;
  pagination.page++;
  getLists();
};

const submitCases = () => {
  dialogRef?.onConfirm(caseSelections.value);
};

function goDetail(id: string) {
  dialogRef?.destroy();
  window.open(`/cases/${id}`, '_blank');
}

onMounted(() => {
  getLists();
});
</script>
