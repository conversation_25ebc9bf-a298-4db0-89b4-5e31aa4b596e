<template>
  <p
    :class="[
      sizeClass,
      'inline-block',
      {
        'font-bold': props.bold,
        underline: props.underline,
        truncate: props.ellipsis,
      },
    ]"
    :style="maxWidth ? { maxWidth } : {}"
  >
    <slot></slot>
  </p>
</template>

<script setup lang="ts">
interface Props {
  size?: 'xl' | 'large' | 'medium' | 'small' | 'xs';
  bold?: boolean;
  underline?: boolean;
  ellipsis?: boolean;
  maxWidth?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  bold: false,
  underline: false,
  ellipsis: false,
  width: '',
  maxWidth: '',
});

const sizeClass = computed(() => {
  const sizes = {
    xl: 'text-xl', // 20px
    large: 'text-lg', // 18px
    medium: 'text-base', // 16px
    small: 'text-sm', // 14px
    xs: 'text-xs', // 12px
  };
  return sizes[props.size];
});
</script>
