<template>
  <div class="grid w-full auto-cols-fr grid-flow-col gap-[8px] xl:h-[64px]">
    <div
      v-for="(_, index) in totalNumberOfPlayers"
      :key="`player-${index}`"
      class="relative flex flex-1 items-center justify-center"
    >
      <div
        :class="[
          'h-full flex flex-1 flex-col items-center justify-center gap-[6px] py-[21px] xl:flex-row xl:px-lg bg-dark-3 rounded-b-[8px]',
        ]"
      >
        <Text
          :size="mobile ? 'small' : 'large'"
          class="text-white/40"
          align="center"
        >
          {{ $t('total_unboxed', 'Total Unboxed') }}
        </Text>
        <Currency
          class="sm:ml-[4px] text-[16px] max-sm:text-sm font-bold gap-[6px]"
          isAnimated
          :animation-duration="1000"
          :amount="getTotalUnboxed(index)"
          :size="mobile ? '16px' : '22px'"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCaseBattleStore } from '@/stores/modules/caseBattle';

interface Props {
  mode: number;
  players: any[];
  totalNumberOfPlayers: number;
}

withDefaults(defineProps<Props>(), {});

const { getPlayerItems } = useCaseBattleStore();
const mobile = useIsMobile();

function getTotalUnboxed(index: number) {
  return getPlayerItems(index).reduce(
    (total: number, item: any) =>
      Number(BigNumberCalc.add(total, item.pawn_price)),
    0,
  );
}
</script>
