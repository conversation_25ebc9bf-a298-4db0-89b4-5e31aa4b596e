<template>
  <div
    :style="{
      minHeight: myBattleTotalCount ? '150px' : '90px',
    }"
  >
    <n-spin
      :show="loading && !isPageLoading"
      class="flex-layout"
      content-class="flex-layout"
    >
      <n-scrollbar class="md:max-h-[520px]">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-lg overflow-y-auto">
          <template v-if="!isPageLoading">
            <CaseBattles
              v-for="battle in myBattles"
              :key="battle.arena_id"
              :battle="battle"
              :show-button="
                ![BattleStatusType.CANCELLED].includes(battle.status)
              "
              :show-dismiss-button="false"
              @hide:battle="hideBattle"
              @battle:cancel="cancelBattle"
              @battle:leave="leaveBattle"
              @battle:dismiss="dismissBattle"
            />
          </template>
          <template v-else>
            <SkeletonCaseBattle
              v-for="index in 2"
              :key="index"
              class="w-full"
            ></SkeletonCaseBattle>
          </template>
        </div>
      </n-scrollbar>
      <ListPaginator
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :page="pagination.page"
        @update:page="handlePagination"
      />
      <EmptyState
        v-if="!myBattles?.length && !loading"
        class="pt-[12px] pb-[70px] text-lg"
        icon="EmptyMybattles"
        message="empty_battles_description"
      />
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { useSettingStore } from '~/stores/modules/setting';
import type { BattleType } from '@/types/battles';
import { BattleStatusType } from '@/types/battles';
import { useSocketListener } from '@/composables/useSocketListener';
import { useBattleStore } from '~/stores/modules/battle';

const userStore = useSettingStore();
const battleStore = useBattleStore();
const {
  myBattleCurrentData,
  loading,
  activeBattleList: outerBattleList,
} = storeToRefs(battleStore);
const { myBattleTotalCount, getMyBattles, setMyBattlesCount } = battleStore;
const {
  allBattlesData,
  dismissedBattles,
  hiddenBattles,
  setBattleInitialList,
  joinBattle,
  createBattle,
  cancelBattle,
  startBattle,
  startNewRound,
  leaveBattle,
  finishBattle,
  dismissBattle,
  hideBattle,
} = useBattleList();

const pagination = reactive<any>({
  pageSize: 100,
  page: 1,
  total: 0,
});
const battleListSort = ref('started_at-desc');
const isPageLoading = ref(true);

watchEffect(() => {
  if (Object.keys(myBattleCurrentData.value).length > 0) {
    allBattlesData.value =
      setBattleInitialList(myBattleCurrentData.value) ?? ([] as any);
    pagination.total = myBattleCurrentData.value.total;
    pagination.page = myBattleCurrentData.value.page;
  }
});

const userId = computed(() => userStore.userInfo?.uid ?? '0');

const handlePagination = ({ page }: { page: number }) => {
  pagination.page = page;
  getMyBattles(pagination.page, pagination.pageSize);
};

// 处理战斗数据
const battles = computed(() => {
  const battleData = {
    battles: [
      ...(allBattlesData.value?.battles || []),
      ...(outerBattleList.value?.battles || []),
    ],
    cases: { ...allBattlesData.value?.cases, ...outerBattleList.value?.cases },
  };

  return battleData?.battles?.map((battle: any) => ({
    ...battle,
    cases: mapBattleCases(battle.case_slug, battleData?.cases),
  }));
});

const myBattles = computed(() => {
  if (!battles.value?.length) return [];

  const currentUserId = userId.value;
  const dismissedIds = dismissedBattles.value;
  const hiddenIds = hiddenBattles.value;
  const pageLimit = pagination.pageSize;

  const [sortKey, sortDir] = battleListSort.value.split('-');
  const sortKeyTyped = sortKey as keyof BattleType;

  const filteredBattles = battles.value.filter((battle) => {
    if (
      dismissedIds.includes(battle.arena_id) ||
      hiddenIds.includes(battle.arena_id)
    ) {
      return false;
    }
    if (!battle.players.some((player: any) => player?.uid === currentUserId)) {
      return false;
    }

    // 等待中、正在进行中、已完成的战斗
    return (
      battle.status === BattleStatusType.WAITING ||
      (battle.status !== BattleStatusType.WAITING &&
        ![BattleStatusType.CANCELLED].includes(battle.status)) ||
      battle.status === BattleStatusType.FINISHED
    );
  });

  const data = filteredBattles
    .sort((a, b) => sortBattles(a, b, sortKeyTyped, sortDir))
    .slice(0, pageLimit);
  setMyBattlesCount(data.length);
  return data;
});

function sortBattles(
  a: BattleType,
  b: BattleType,
  key: keyof BattleType,
  direction: string,
) {
  if (key === 'started_at') {
    const timeA = new Date(a[key] as string).getTime();
    const timeB = new Date(b[key] as string).getTime();
    return direction === 'asc' ? timeA - timeB : timeB - timeA;
  }

  const aValue = a[key];
  const bValue = b[key];
  if (direction === 'asc') {
    if (aValue === undefined || bValue === undefined) return 0;
    return Number(aValue) > Number(bValue) ? 1 : -1;
  }
  if (aValue === undefined || bValue === undefined) return 0;
  return Number(aValue) < Number(bValue) ? 1 : -1;
}

// 过滤函数
function mapBattleCases(cases: any[], casesData: any) {
  return cases.map((c) => ({
    ...c,
    case_image: casesData?.[c.slug]?.image_url || '',
    case_name: casesData?.[c.slug]?.case_name || '',
  }));
}

const getMyBattlesData = async (page: string, pageSize: string) => {
  isPageLoading.value = true;
  await getMyBattles(page, pageSize);
  isPageLoading.value = false;
};

const listenerBattleCreate = (data: any) => {
  if (data.battle_info.creator_uid !== userId.value) return;
  pagination.total++;
  createBattle(data);
};

const listenerBattleJoin = (data: any) => {
  joinBattle(data.arena_id, {
    ...data.players,
    ...data.user,
  });
};

const listenerBattleCancel = (data: any) => {
  cancelBattle(data.arena_id);
};

const listenerBattleRunning = (data: any) => {
  startBattle(data.arena_id);
};

const listenerBattleNewRound = (data: any) => {
  startNewRound(data.arena_id, data.round);
};

const listenerBattleLeave = (data: any) => {
  leaveBattle(data.arena_id, data.players.position);
};

const listenerBattleFinish = (data: any) => {
  finishBattle(
    data.arena_id,
    data.winners,
    dayjs(getLocalTimeAsUTCTimestamp()).format('YYYY-MM-DD HH:mm:ss'),
  );
};

useSocketListener('casebattle', {
  onCreate: listenerBattleCreate,
  onJoin: listenerBattleJoin,
  onCancel: listenerBattleCancel,
  onRunning: listenerBattleRunning,
  onNewRound: listenerBattleNewRound,
  onLeave: listenerBattleLeave,
  onFinish: listenerBattleFinish,
});

onMounted(() => {
  getMyBattlesData(pagination.page, pagination.pageSize);
});

defineExpose({
  isPageLoading,
});
</script>

<style scoped lang="scss">
.tab-pane-transition {
  overflow: hidden;
}

.tab-content {
  transition: height 0.3s ease-in-out;
}
</style>
