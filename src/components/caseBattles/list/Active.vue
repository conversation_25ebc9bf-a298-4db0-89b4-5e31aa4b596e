<template>
  <div :class="['relative flex-layout']">
    <n-spin
      :show="loading && !isPageLoading"
      :class="['min-h-[200px] w-full', !battleList?.length ? 'm-auto' : '']"
    >
      <div
        :class="[
          'w-full',
          isHorizontal
            ? 'flex overflow-x-auto flex-wrap md:flex-nowrap gap-[16px] hide-scrollbar'
            : 'grid grid-cols-1 lg:grid-cols-2 gap-lg',
        ]"
      >
        <template v-if="!isPageLoading">
          <CaseBattles
            v-for="battle in battleList"
            :key="battle.arena_id"
            :battle="battle"
            :show-dismiss-button="false"
            :hide-timeout-battle="!isHomePage"
            :class="[
              isHorizontal
                ? 'w-[calc(100%)] md:max-w-[calc(50%-8px)] flex-shrink-0'
                : '',
            ]"
            :loading="loading"
            @battle:dismiss="dismissBattle"
            @hide:battle="hideBattle"
            @battle:cancel="cancelBattle"
          />
        </template>
        <template v-else>
          <SkeletonCaseBattle
            v-for="index in pageSize"
            :key="index"
            class="w-full"
          ></SkeletonCaseBattle>
        </template>
      </div>
      <EmptyState
        v-if="!battleList?.length && !loading"
        class="pt-[12px]"
        icon="empty-active-battles"
        message="no_record_found"
      />
    </n-spin>
    <div v-if="!noMore" ref="target" class="absolute bottom-1/4 h-[10px]"></div>
  </div>
</template>
<script lang="ts" setup>
import { isEqual } from 'lodash-es';
import dayjs from 'dayjs';
import { useSettingStore } from '~/stores/modules/setting';
import type { BattleType } from '@/types/battles';
import { BattleModesType, BattleStatusType } from '@/types/battles';
import { useSocketListener } from '@/composables/useSocketListener';
import { useBattleStore } from '~/stores/modules/battle';

interface Props {
  params?: {
    order_key?: string;
    order_by?: string;
    is_can_join?: number;
    is_active?: number;
    status?: number[];
    order?: number;
  };
  page?: number;
  pageSize?: number;
  battleListSort?: string;
  isHorizontal?: boolean;
  canMore?: boolean;
  limit?: number;
  isHomePage?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    is_active: 1,
    // status: [1, 2, 3, 4],
  }),
  page: 1,
  pageSize: 30,
  battleListSort: 'battle_cost-desc',
  isHorizontal: false,
  canMore: true,
  limit: undefined,
  enableStatusSort: false,
});

const battleStore = useBattleStore();
const userStore = useSettingStore();
const { battleApi } = useApi();
const { activeBattleList, myBattleCurrentData } = storeToRefs(battleStore);
const { setActiveBattlesCount } = battleStore;
const {
  allBattlesData,
  dismissedBattles,
  hiddenBattles,
  setBattleInitialList,
  clearBattle,
  dismissBattle,
  hideBattle,
  createBattle,
  joinBattle,
  cancelBattle,
  startBattle,
  startNewRound,
  leaveBattle,
  finishBattle,
} = useBattleList();

setActiveBattlesCount(0);
const isPageLoading = ref(true);
const loading = ref(false);
const noMore = ref(true);
const pageNum = ref(1);
const activeBattlesInLimit = ref<string[]>([]);
const finishEventQueue = ref<{ arena_id: string; timestamp: number }[]>([]);
const isProcessingQueue = ref(false);

const userId = computed(() => userStore.userInfo?.uid ?? '0');

const params = computed(() => {
  return {
    page_size: props.pageSize,
    is_active: props.params.status?.length ? 0 : 1,
    is_can_join: 1,
    ...props.params,
    status: props.params.status?.length ? props.params.status : undefined,
    page: pageNum.value,
  };
});

async function processFinishEventQueue() {
  if (isProcessingQueue.value || finishEventQueue.value.length === 0) return;
  isProcessingQueue.value = true;
  try {
    while (finishEventQueue.value.length > 0) {
      const event = finishEventQueue.value.shift()!;
      updateWaitingBattlesInLimit();
      await new Promise((resolve) => setTimeout(resolve, 1000));
      manageBattleInLimitTracking(event.arena_id, false);
    }
  } finally {
    isProcessingQueue.value = false;
  }
}

// 处理战斗数据
const battles = computed(() => {
  return allBattlesData.value?.battles?.map((battle: any) => ({
    ...battle,
    cases: mapBattleCases(battle.case_slug, allBattlesData.value?.cases),
  }));
});

const empty = computed(() => !battleList.value?.length && !loading.value);

// 计算战斗列表
const activeBattles = computed(() => {
  if (props.isHomePage || !battles.value?.length) {
    return [];
  }
  const currentUserId = userId.value;
  const dismissedIds = dismissedBattles.value;
  const hiddenIds = hiddenBattles.value;
  const isCanJoin = props.params.is_can_join;
  const battleLimit = props.limit;

  const [sortKey, sortDir] = props.battleListSort.split('-');
  const sortKeyTyped = sortKey as keyof BattleType;

  const filteredBattles = filterVisibleBattles(
    battles.value,
    currentUserId,
    dismissedIds,
    hiddenIds,
    isCanJoin,
  );

  const data = sortBattlesByStartTime(filteredBattles, true)
    .sort((a, b) => sortBattles(a, b, sortKeyTyped, sortDir))
    .slice(0, battleLimit);

  setActiveBattlesCount(data.length);
  return data;
});

function filterVisibleBattles(
  battleList: any[],
  currentUserId: string,
  dismissedIds: string[],
  hiddenIds: string[],
  isCanJoin?: number,
): any[] {
  return battleList.filter((battle) => {
    if (
      dismissedIds.includes(battle.arena_id) ||
      hiddenIds.includes(battle.arena_id)
    ) {
      return false;
    }

    if (
      battle.players.some((player: any) => player?.uid === currentUserId) ||
      [BattleModesType.SHARED].includes(battle.battle_mode)
    ) {
      return false;
    }

    if (isCanJoin) {
      return battle.status === BattleStatusType.WAITING;
    } else {
      // 正在进行中、已完成和已取消的战斗
      const isActive = ![
        BattleStatusType.CANCELLED,
        BattleStatusType.FINISHED,
      ].includes(battle.status);
      const isFinished = battle.status === BattleStatusType.FINISHED;
      const isCancelled = battle.status === BattleStatusType.CANCELLED;

      return isActive || isFinished || isCancelled;
    }
  });
}

function getBattlesByStatus(battleList: any[]) {
  return {
    waiting: battleList.filter((b) => b.status === BattleStatusType.WAITING),
    running: battleList.filter((b) => b.status === BattleStatusType.RUNNING),
    finished: battleList.filter((b) =>
      [
        BattleStatusType.FINISHED,
        BattleStatusType.CANCELLED,
        BattleStatusType.ENDED,
      ].includes(b.status),
    ),
  };
}

function sortBattlesByStartTime(battles: any[], ascending = false): any[] {
  return [...battles].sort((a, b) => {
    const timeA = dayjs(a.started_at as string);
    const timeB = dayjs(b.started_at as string);
    return ascending
      ? timeA.isAfter(timeB)
        ? 1
        : -1
      : timeB.isAfter(timeA)
        ? 1
        : -1;
  });
}

function updateActiveBattlesInLimitTracking(currentBattles: any[]): void {
  activeBattlesInLimit.value = activeBattlesInLimit.value.filter((id) =>
    currentBattles.some((battle) => battle.arena_id === id),
  );
}

function manageBattleInLimitTracking(battleId: string, shouldAdd = true): void {
  if (shouldAdd) {
    if (!activeBattlesInLimit.value.includes(battleId)) {
      const newArray = [...activeBattlesInLimit.value];
      newArray.push(battleId);
      activeBattlesInLimit.value = newArray;
    }
  } else {
    activeBattlesInLimit.value = activeBattlesInLimit.value.filter(
      (id) => id !== battleId,
    );
  }
}

const battleList = computed(() => {
  if (!battles.value?.length) {
    return [];
  }

  if (!props.isHomePage) {
    return activeBattles.value;
  }

  const currentUserId = userId.value;
  const dismissedIds = dismissedBattles.value;
  const hiddenIds = hiddenBattles.value;
  const battleLimit = props.limit || Number.MAX_SAFE_INTEGER;

  const filteredBattles = filterVisibleBattles(
    battles.value,
    currentUserId,
    dismissedIds,
    hiddenIds,
  );

  const battlesByStatus = getBattlesByStatus(filteredBattles);

  const waitingSorted = sortBattlesByStartTime(battlesByStatus.waiting);

  // 对战中和已结束的对战排序
  const runningSorted = sortBattlesByStartTime(battlesByStatus.running);
  const finishedSorted = sortBattlesByStartTime(battlesByStatus.finished);

  // 已结束对战
  const finishedInLimit = finishedSorted.filter((battle) =>
    activeBattlesInLimit.value.includes(battle.arena_id),
  );
  const finishedOutLimit = finishedSorted.filter(
    (battle) => !activeBattlesInLimit.value.includes(battle.arena_id),
  );
  let result: BattleType[] = [];

  const stickyBattles = [...runningSorted, ...finishedInLimit].filter(
    (battle) => activeBattlesInLimit.value.includes(battle.arena_id),
  );
  result = [...stickyBattles];

  // 等待中对战
  if (result.length < battleLimit) {
    const remainingWaiting = waitingSorted.filter(
      (battle) => !result.some((b) => b.arena_id === battle.arena_id),
    );
    result = [
      ...result,
      ...remainingWaiting.slice(0, battleLimit - result.length),
    ];
  }
  const originalOrder = filteredBattles.map((battle) => battle.arena_id);
  result.sort(
    (a, b) =>
      originalOrder.indexOf(a.arena_id) - originalOrder.indexOf(b.arena_id),
  );
  // 剩余进行中对战
  if (result.length < battleLimit) {
    const remainingRunning = runningSorted.filter(
      (battle) => !result.some((b) => b.arena_id === battle.arena_id),
    );
    result = [
      ...result,
      ...remainingRunning.slice(0, battleLimit - result.length),
    ];
  }

  // 剩余已结束对战
  if (result.length < battleLimit) {
    result = [
      ...result,
      ...finishedOutLimit.slice(0, battleLimit - result.length),
    ];
  }
  updateActiveBattlesInLimitTracking(result);
  setActiveBattlesCount(result.length);
  return result.slice(0, battleLimit);
});

watchEffect(() => {
  activeBattleList.value = allBattlesData.value;
});

function mapBattleCases(cases: any[], casesData: any) {
  return cases.map((c) => ({
    ...c,
    case_image: casesData?.[c.slug]?.image_url || '',
    case_name: casesData?.[c.slug]?.case_name || '',
  }));
}

function sortBattles(
  a: BattleType,
  b: BattleType,
  key: keyof BattleType,
  direction: string,
) {
  if (key === 'started_at') {
    const timeA = new Date(a[key] as string).getTime();
    const timeB = new Date(b[key] as string).getTime();
    return direction === 'asc' ? timeA - timeB : timeB - timeA;
  }

  const aValue = a[key];
  const bValue = b[key];
  if (direction === 'asc') {
    if (aValue === undefined || bValue === undefined) return 0;
    return Number(aValue) > Number(bValue) ? 1 : -1;
  }
  if (aValue === undefined || bValue === undefined) return 0;
  return Number(aValue) < Number(bValue) ? 1 : -1;
}

function updateWaitingBattlesInLimit() {
  if (!props.isHomePage || !props.limit || !battles.value?.length) return;
  const currentWaitingInLimit = battleList.value
    .filter((b) => b.status === BattleStatusType.WAITING)
    .map((b) => b.arena_id);

  // 所有等待中对战
  const allWaitingBattles = sortBattlesByStartTime(
    battles.value.filter((b) => b.status === BattleStatusType.WAITING),
  );
  const needCount = props.limit - currentWaitingInLimit.length;

  if (needCount > 0) {
    const newWaitingBattles = allWaitingBattles
      .filter((battle) => !currentWaitingInLimit.includes(battle.arena_id))
      .slice(0, needCount);
    for (const battle of newWaitingBattles) {
      manageBattleInLimitTracking(battle.arena_id, true);
    }
  }

  const hasRunningBattle = battleList.value.some(
    (b) => b.status === BattleStatusType.RUNNING,
  );
  if (allWaitingBattles.length < props.limit && !hasRunningBattle) {
    getBattles(false);
  }
}

const handleBattleCreate = (data: any) => {
  if (data.battle_info.creator_uid !== userId.value) {
    createBattle(data);
  }
};

const handleBattleJoin = (data: any) => {
  joinBattle(data.arena_id, {
    ...data.players,
    ...data.user,
  });
};

const handleBattleCancel = (data: any) => {
  cancelBattle(data.arena_id);
  manageBattleInLimitTracking(data.arena_id, false);
  updateWaitingBattlesInLimit();
};

const handleBattleRunning = (data: any) => {
  if (props.isHomePage && props.limit && battles.value?.length) {
    const battle = battles.value.find((b) => b.arena_id === data.arena_id);
    if (battle && battle.status === BattleStatusType.WAITING) {
      const waitingBattles = battles.value.filter(
        (b) => b.status === BattleStatusType.WAITING,
      );
      const waitingIndex = waitingBattles.findIndex(
        (b) => b.arena_id === data.arena_id,
      );
      if (waitingIndex > -1 && waitingIndex < props.limit) {
        manageBattleInLimitTracking(data.arena_id, true);
      }
    }
  }
  startBattle(data.arena_id);
};

const handleBattleNewRound = (data: any) => {
  startNewRound(data.arena_id, data.round);
};

const handleBattleLeave = (data: any) => {
  // 检查当前我的战斗数据
  const myBattleCurrent = setBattleInitialList(myBattleCurrentData.value);
  if (!myBattleCurrent?.battles?.length) {
    leaveBattle(data.arena_id, data.position);
    return;
  }
  const index = myBattleCurrent.battles.findIndex(
    (battle: any) => battle.arena_id === data.arena_id,
  );

  if (index !== -1) {
    const battleToAdd = myBattleCurrent.battles[index];
    const casesToAdd = myBattleCurrent.cases || {};

    allBattlesData.value = {
      battles: [...(allBattlesData.value.battles || []), battleToAdd],
      cases: {
        ...(allBattlesData.value.cases || {}),
        ...casesToAdd,
      },
    };
  }
  leaveBattle(data.arena_id, data.position);
};

const handleBattleFinish = (data: any) => {
  finishBattle(
    data.arena_id,
    data.winners,
    dayjs(getLocalTimeAsUTCTimestamp()).format('YYYY-MM-DD HH:mm:ss'),
  );
  finishEventQueue.value.push({
    arena_id: data.arena_id,
    timestamp: Date.now(),
  });
  setTimeout(() => {
    processFinishEventQueue();
  }, 1000);
};

useSocketListener('casebattle', {
  // onInit: handleBattleInit,
  onCreate: handleBattleCreate,
  onJoin: handleBattleJoin,
  onCancel: handleBattleCancel,
  onRunning: handleBattleRunning,
  onNewRound: handleBattleNewRound,
  onLeave: handleBattleLeave,
  onFinish: handleBattleFinish,
});

const { target } = useObserver(() => {
  handleLoad();
});

const handleLoad = () => {
  if (noMore.value) return;
  pageNum.value++;
  getBattles();
};

const getBattles = async (enableLoading = true) => {
  try {
    if (enableLoading) {
      loading.value = true;
    }
    const res = await battleApi.getBattleList(params.value as any);
    if (res.data.value.code !== 0) return;
    const { data } = res.data.value;
    const activeData = setBattleInitialList(data) ?? { battles: [], cases: {} };
    const isFirstPage = params.value.page === 1;
    allBattlesData.value = {
      battles: isFirstPage
        ? [...activeData.battles!]
        : [...(allBattlesData.value?.battles ?? []), ...activeData.battles!],
      cases: {
        ...(isFirstPage ? {} : (allBattlesData.value?.cases ?? {})),
        ...activeData.cases!,
      },
    };
    if (props.canMore) {
      noMore.value = allBattlesData.value.battles!.length >= data.total!;
    }
  } catch (error) {
  } finally {
    loading.value = false;
    isPageLoading.value = false;
  }
};

watch(
  () => props.params,
  (newVal, oldVal) => {
    if (!isEqual(newVal, oldVal)) {
      pageNum.value = 1;
      getBattles();
    }
  },
);
onMounted(() => {
  getBattles();
});
onUnmounted(() => {
  clearBattle();
});

defineExpose({
  empty,
  isPageLoading,
});
</script>

<style scoped lang="scss">
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera*/
  }
}
</style>
