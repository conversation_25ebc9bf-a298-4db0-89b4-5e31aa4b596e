<template>
  <div
    :style="{
      minHeight: battles?.length ? '190px' : '90px',
    }"
  >
    <EmptyState
      v-if="!battles?.length && !loading"
      class="pt-[12px] pb-[122px] text-lg"
      icon="EmptyMybattles"
      message="empty_battles_description"
    />
    <n-scrollbar class="md:max-h-[500px]">
      <div
        class="mt-6 mb-md grid grid-cols-1 lg:grid-cols-2 gap-lg overflow-y-auto"
      >
        <template v-if="!loading">
          <CaseBattles
            v-for="battle in battles"
            :key="battle.arena_id"
            :battle="battle"
            :show-button="false"
          />
        </template>
        <template v-else>
          <SkeletonCaseBattle
            v-for="index in 4"
            :key="index"
            class="w-full"
          ></SkeletonCaseBattle>
        </template>
      </div>
    </n-scrollbar>
    <ListPaginator
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :page="pagination.page"
      @update:page="handlePagination"
    />
  </div>
</template>

<script setup lang="ts">
import { useBattleStore } from '~/stores/modules/battle';
import { useSettingStore } from '~/stores/modules/setting';

const battleStore = useBattleStore();
const { myBattleHistoryData, loading } = storeToRefs(battleStore);
const { getMyBattleHistory } = battleStore;
const { allBattlesData, setBattleInitialList } = useBattleList();
const settingStore = useSettingStore();

const userId = computed(() => settingStore.userInfo?.uid);

const pagination = reactive<any>({
  pageSize: 12,
  page: 1,
  total: 0,
});

// 处理战斗数据
const battles = computed(() => {
  return allBattlesData.value?.battles?.map((battle: any) => ({
    ...battle,
    cases: mapBattleCases(battle.case_slug, allBattlesData.value?.cases),
  }));
});

// 过滤函数
function mapBattleCases(cases: any[], casesData: any) {
  return cases.map((c) => ({
    ...c,
    case_image: casesData?.[c.slug]?.image_url || '',
    case_name: casesData?.[c.slug]?.case_name || '',
  }));
}

const handlePagination = ({ page }: { page: number }) => {
  pagination.page = page;
  getMyBattleHistory(pagination.page, pagination.pageSize);
};

watchEffect(() => {
  if (Object.keys(myBattleHistoryData.value).length > 0) {
    myBattleHistoryData.value.list = myBattleHistoryData.value.list.map(
      (battle: any) => {
        // 如果对战没有玩家信息，添加当前用户的玩家信息
        if (!battle.players?.length && battle.all_players?.length) {
          const currentPlayer = battle.all_players.find(
            (player: any) => player.uid === userId.value,
          );
          if (currentPlayer) {
            battle.players = [currentPlayer];
          }
        }
        return battle;
      },
    );

    // 初始化对战数据
    allBattlesData.value =
      setBattleInitialList(myBattleHistoryData.value) || {};
    pagination.total = myBattleHistoryData.value.total;
    pagination.page = myBattleHistoryData.value.page;
  }
});

onMounted(() => {
  getMyBattleHistory(pagination.page, pagination.pageSize);
});
</script>

<style scoped lang="scss">
.tab-pane-transition {
  overflow: hidden;
}

.tab-content {
  transition: height 0.3s ease-in-out;
}
</style>
