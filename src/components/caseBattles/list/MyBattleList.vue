<template>
  <div>
    <div class="my-[30px]">
      <div class="relative flex justify-between">
        <span
          class="h-[43px] absolute left-0 top-0 flex items-center text-[20px] sm:text-[24px] font-bold"
        >
          <span class="uppercase">{{ $t('my_battles') }}</span>
        </span>
        <n-tabs
          :key="currentLang"
          animated
          :justify-content="isMobile ? 'start' : 'end'"
          size="small"
          class="ml-auto max-md:mt-[40px]"
          tab-class="font-bold"
          :value="activeTab"
          @update:value="handleSelectTab"
        >
          <n-tab-pane :tab="$t('current')" name="current" class="mt-9 pt-0">
            <CurrentBattle />
          </n-tab-pane>
          <n-tab-pane :tab="$t('history')" name="history">
            <HistoryBattle />
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CurrentBattle from './Current.vue';
import HistoryBattle from './History.vue';
import { useAppStore } from '~/stores/modules/app';

const activeTab = ref('current');

const appStore = useAppStore();

const isMobile = useIsMobile();

const currentLang = computed(() => appStore.currentLang);

const handleSelectTab = (tab: any) => {
  if (activeTab.value === tab.value) return;
  activeTab.value = tab.value;
};
</script>
