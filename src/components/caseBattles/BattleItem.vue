<script setup lang="ts">
const isMobile = useIsMobile();

const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  isMiddleCard: {
    type: Boolean,
    default: false,
  },
  isSelectedItem: {
    type: Boolean,
    default: false,
  },
  index: {
    type: Number,
    required: true,
  },
  selectedItemPosition: {
    type: Number,
    required: true,
  },
  spinEnded: {
    type: Boolean,
    default: false,
  },
});
const opacityColor = (opacity: number = 1) => {
  return hexToRgba(props.item.tags?.rarity?.color || '', opacity);
};
</script>

<template>
  <div
    :class="[
      'relative flex h-[80px] w-full flex-col items-center justify-center xl:h-[120px] xl:w-full',
    ]"
  >
    <div
      :class="[
        'relative h-full w-[80px] xl:w-[120px]  transition-transform duration-1000',
        {
          'scale-75 opacity-[0.3]':
            (!isMiddleCard && !spinEnded) || (!isSelectedItem && spinEnded),
          'translate-y-[-52px]': isSelectedItem && spinEnded,
          'translate-y-[40px] !opacity-0':
            index > selectedItemPosition && spinEnded,
          'translate-y-[-30px] !opacity-0':
            index < selectedItemPosition && spinEnded,
        },
      ]"
    >
      <img
        :key="item.icon_url"
        :src="item.icon_url"
        class="item-shadow absolute top-0 z-10 h-full w-full object-contain"
      />
      <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <GradientBorder
          :startColor="opacityColor()"
          :width="isMobile ? 58 : 80"
          :height="isMobile ? 58 : 80"
          :borderRadius="isMobile ? 15 : 20"
        />
      </div>
      <div
        class="absolute left-0 top-0 z-0 h-full w-full opacity-20"
        :style="{
          background: `radial-gradient(50% 50% at 50% 50%, #${item?.tags?.rarity?.color}  0%, rgba(74, 34, 34, 0) 100%) `,
        }"
      ></div>
    </div>
    <Transition
      v-if="isSelectedItem"
      enter-active-class="transition duration-1000"
      enter-from-class="translate-y-1.5 opacity-0"
      enter-to-class="translate-y-0 opacity-100"
      leave-active-class="transition duration-1000"
      leave-from-class="translate-y-0 opacity-100"
      leave-to-class="translate-y-1.5 opacity-0"
      mode="out-in"
    >
      <div
        v-if="isSelectedItem && spinEnded"
        class="absolute translate-y-[52px] flex w-full flex-col items-center justify-center"
      >
        <div class="flex w-full mb-md items-center justify-center gap-md">
          <!-- 款式 -->
          <AccessoryTag :info="props.item" class="max-sm:text-base" />
          <Text
            line-height="tight"
            ellipsis
            class="w-max text-center text-white/40"
            :size="isMobile ? 'medium' : 'large'"
          >
            {{ item.category_name }}
          </Text>
        </div>
        <Text
          class="mb-[14px] w-full truncate text-center"
          :size="isMobile ? 'medium' : 'large'"
          bold
          ellipsis
          :tooltipText="$td(item.market_hash_name)"
        >
          {{ $td(item.steam_item_name) }}
        </Text>
        <Currency
          class="w-full truncate text-center !justify-center text-lg font-normal"
          size="22px"
          :amount="item.pawn_price"
          align="center"
        />
      </div>
    </Transition>
  </div>
</template>
