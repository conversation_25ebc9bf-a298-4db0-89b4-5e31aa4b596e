<template>
  <CaseBattlesSpinner
    spinner-height="h-[124px]"
    :is-loading="isPageLoading || !users"
    :spinner-config-array="spinnerConfig"
    @spin-ended="onSpinEnded"
  >
    <template #default="slotProps">
      <CaseBattlesUser v-bind="{ ...slotProps, battleMode }" />
    </template>
  </CaseBattlesSpinner>
</template>

<script setup lang="ts">
import { SPINNER_DURATION } from '~/constants/timing';

interface Props {
  battleArenaId: string;
  users: any[];
  battleMode: number;
  selectedIndex: number;
  isPageLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isPageLoading: false,
});

const emit = defineEmits<{
  (e: 'spinEnded'): void;
}>();

const { spinnerConfig, fillSpinnerConfig } = useSpinnerConfig();
onMounted(() => {
  fillSpinnerConfig({
    caseItems: props.users.map((item) => ({
      ...item,
      chance: parseFloat((100 / props.users.length).toFixed(2)),
    })),
    length: 1,
    isFastMode: true,
    selectedItem: props.users?.at(props.selectedIndex),
    customTime: SPINNER_DURATION,
    hash: props.battleArenaId,
    initialArray: [],
  });

  setTimeout(() => {
    spinnerConfig.value[0] = {
      ...spinnerConfig.value[0],
      isSpinning: true,
      spinEnded: false,
      isMuted: false,
    };
  }, 1500);
});

const onSpinEnded = () => {
  spinnerConfig.value[0] = {
    ...spinnerConfig.value[0],
    isSpinning: false,
    spinEnded: true,
    isMuted: true,
  };
  emit('spinEnded');
};
</script>
