<template>
  <div
    class="grid h-full w-full auto-cols-fr grid-flow-col gap-xxs max-sm:gap-[10px] rounded-lg overflow-hidden"
  >
    <div
      v-for="index in totalPlayers"
      :key="`player-${index}`"
      class="relative flex flex-1 self-center"
    >
      <!-- 已加入玩家的显示 -->
      <div v-if="players[index - 1]" class="w-full">
        <div class="flex flex-col items-center justify-between">
          <div class="relative flex flex-col items-center w-full">
            <BaseIcon
              name="CheckCircleIcon"
              class="text-green-1 h-[24px] w-[24px] xl:h-[40px] xl:w-[40px]"
            />
            <Text
              class="mt-sm md:mt-[12px]"
              :size="isMobile ? 'small' : 'medium'"
              align="center"
              bold
            >
              {{ t('ready') }}
            </Text>
            <template v-if="!disableControls">
              <n-button
                v-if="currentPlayerIndex === index - 1"
                class="text-white font-medium uppercase mt-[21px] min-w-[128px] md:h-[42px] btn-gradient-gray max-md:mt-[6px] max-md:h-[38px] max-md:p-1 max-md:min-w-[90%] max-md:!rounded-sm max-md:text-xs/none"
                @click="
                  isCreator
                    ? emit('cancelBattle')
                    : emit('leaveBattle', index - 1)
                "
              >
                {{ isCreator ? cancelText : leaveText }}
              </n-button>
            </template>
          </div>
        </div>
      </div>

      <!-- 等待加入玩家的显示 -->

      <div
        v-else
        class="flex w-full h-full flex-col items-center justify-between"
      >
        <CaseBattlesClockSpinner />
        <Text
          class="flex items-end text-center mt-sm md:mt-[12px] xl:h-[unset]"
          :size="isMobile ? 'small' : 'medium'"
          bold
        >
          {{ t('awaiting_player') }}
        </Text>
        <template
          v-if="!disableControls && (currentPlayerIndex < 0 || isCreator)"
        >
          <AmountButton
            class="w-full mt-[21px] justify-center max-md:h-[unset] max-md:mt-[6px] max-md:flex max-md:flex-col max-md:items-center"
            :currency-class="'max-md:w-full max-md:py-2 max-md:rounded-t-md max-md:bg-transparent'"
            :action-class="'break-all max-w-full max-md:w-max max-md:min-w-[90%]  max-md:text-xs/none max-md:h-[38px] max-md:!p-1 max-md:rounded-t-none max-md:!rounded-sm'"
            :amount="battleCost"
            :text="isCreator ? $t('join_bot') : joinText"
            :size="isMobile ? 'small' : 'medium'"
            @click="
              isCreator ? emit('callBot', index) : emit('joinBattle', index)
            "
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import type { Player } from '~/types/battles';

const props = withDefaults(
  defineProps<{
    battleCost: number | string;
    battleCreatorId: number | string;
    disableControls?: boolean;
    isUnoReverse?: boolean;
    players?: Player[];
    totalNumberOfPlayers: number;
  }>(),
  {
    disableControls: false,
    isUnoReverse: false,
    players: () => [],
    totalNumberOfPlayers: 0,
  },
);

const emit = defineEmits([
  'callBot',
  'cancelBattle',
  'joinBattle',
  'leaveBattle',
]);

const { t } = useI18n();
const isMobile = useIsMobile();
const settingStore = useSettingStore();

const totalPlayers = computed(() => props.totalNumberOfPlayers);
const userId = computed(() => settingStore.userInfo?.uid);
const currentPlayerIndex = computed(() => {
  return props.players.findIndex((player) => player?.uid === userId.value);
});
const isCreator = computed(() => props.battleCreatorId === userId.value);

const joinText = t('join_battle');
const cancelText = t('cancel_the_battle');
const leaveText = t('exit');
</script>
