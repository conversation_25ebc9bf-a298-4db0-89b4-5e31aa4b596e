<template>
  <svg
    data-v-88460558=""
    class="loader-spinner text-primary-400 fill-current h-[24px] w-[24px] align-middle text-yellow-2 xl:h-[30px] xl:w-[30px]"
    viewBox="0 0 32 32"
  >
    <circle
      data-v-88460558=""
      class="loader-spinner__path stroke-current"
      cx="16"
      cy="16"
      r="14"
      fill="none"
      stroke-width="2"
    ></circle>
  </svg>
</template>

<style scoped lang="scss">
.loader-spinner {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.loader-spinner__path {
  stroke-linecap: butt;
  animation: animateSpinnerPath 1.5s ease-in-out infinite;
}
@keyframes animateSpinnerPath {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40;
  }

  to {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
</style>
