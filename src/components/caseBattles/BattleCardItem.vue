<template>
  <SkinCard
    :item="item"
    :isAvailable="isClaimed || !isWinner"
    :disabled="disabled"
    :actionShow="actionShow"
    :colorCode="item.tags?.rarity?.color || ''"
    :iconUrl="item.icon_url"
    :categoryName="item.category_name || ''"
    :title="$td(item.steam_item_name)"
    :titleTooltip="$td(item.market_hash_name || '')"
    :price="item.pawn_price"
    :aspectRatio="aspectRatio"
    :actionText="$t(!isClaimed ? 'converted' : 'convert')"
    :disabledText="$t('converted')"
    :tagWrapperClass="tagWrapperClass"
    btnType="purple"
    @action-click="emit('battleItemConvert', $event)"
  >
    <template #tag>
      <AccessoryTag
        :info="item"
        :size="gemSize"
        :gap="playerNumber === 2 ? 4 : gemSize === 12 ? 2 : 4"
        :fontSize="playerNumber === 2 ? 16 : gemSize === 12 ? 12 : 16"
      />
    </template>
  </SkinCard>
</template>

<script setup lang="ts">
import { BattleStatusType, type BattleRecord } from '~/types/battles';

interface Props {
  item: BattleRecord;
  mode: number;
  mask?: boolean;
  disabled?: boolean;
  status: number;
  isWinner: boolean;
  aspectRatio: string;
  playerNumber: number;
}

const props = withDefaults(defineProps<Props>(), {
  mask: true,
  disabled: false,
  status: 0,
  aspectRatio: '222/269',
  playerNumber: 2,
});
const emit = defineEmits<{
  (e: 'battleItemConvert', data: BattleRecord): void;
}>();

const isClaimed = computed(() => {
  return props.item.is_claimed === 2;
});

const isMobile = useIsMobile();

const actionShow = computed(() => {
  return (
    props.mask &&
    (props.status === BattleStatusType.FINISHED ||
      props.status === BattleStatusType.TIEBREAKER) &&
    props.isWinner
  );
});

const tagWrapperClass = computed(() => {
  return props.playerNumber === 2 ? '' : 'max-sm:pl-[0.8em] max-sm:pt-[0.7em]';
});

const gemSize = computed(() => {
  if (props.playerNumber === 2) {
    return 17;
  }
  return isMobile.value ? 12 : 17;
});
</script>
