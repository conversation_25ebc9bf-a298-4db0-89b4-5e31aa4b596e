<template>
  <div class="relative">
    <!-- 渲染旋转器内容 -->
    <!-- <div
      v-if="isLoading || !spinnerConfigArray.length"
      :class="['mt-md rounded', spinnerHeight]"
    ></div> -->

    <!-- 渲染旋转器刻度 -->
    <template v-if="showHorizontalSpinner && !isLoading && showTick">
      <CaseBattlesSpinnerTickSmall
        :class="[
          'absolute inset-x-0 top-[-34px] z-10 mx-auto rotate-90 transition-colors duration-1000',
          tickColorClass,
        ]"
      />
      <CaseBattlesSpinnerTickSmall
        :class="[
          'absolute inset-x-0 bottom-[-34px] z-10 mx-auto -rotate-90 transition-colors duration-1000',
          tickColorClass,
        ]"
      />
    </template>
    <template v-else-if="!showHorizontalSpinner && !isLoading && showTick">
      <CaseBattlesSpinnerTickSmall
        :class="[
          'absolute inset-y-0 left-[31px] z-10 my-auto translate-x-[-50%]  transition-colors duration-1000',
          tickColorClass,
        ]"
      />
      <CaseBattlesSpinnerTickSmall
        :class="[
          'absolute inset-y-0 right-[20px] z-10 my-auto translate-x-[50%] rotate-180 transition-colors duration-1000',
          tickColorClass,
        ]"
      />
    </template>
    <div
      :class="[
        'mt-md flex overflow-hidden rounded-lg',
        {
          'flex-col': showHorizontalSpinner,
          'flex-row': !showHorizontalSpinner,
          [spinnerGap]: true,
          [spinnerHeight]: true,
        },
      ]"
    >
      <template v-for="(config, index) in spinnerConfigArray" :key="index">
        <CaseBattlesHorizontalSpinner
          v-if="showHorizontalSpinner"
          :autoCenter="autoCenter"
          :bgGlow="bgGlow"
          :minOffsetPercentage="minOffsetPercentage"
          :spinnerConfig="config || {}"
          @play-start-sound="handleStartSound"
          @play-tick-sound="handleTickSound"
          @spin-ended="() => handleSpinEnded(index)"
        >
          <template #default="slotProps">
            <slot v-bind="{ ...slotProps, isHorizontalSpinner: true }" />
          </template>
        </CaseBattlesHorizontalSpinner>
        <CaseBattlesVerticalSpinner
          v-else
          :autoCenter="autoCenter"
          :bgGlow="bgGlow"
          :minOffsetPercentage="minOffsetPercentage"
          :spinnerConfig="config || {}"
          @play-start-sound="handleStartSound"
          @play-tick-sound="handleTickSound"
          @spin-ended="() => handleSpinEnded(index)"
        >
          <template #default="Props">
            <slot v-bind="{ ...Props, isHorizontalSpinner: false }" />
          </template>
        </CaseBattlesVerticalSpinner>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { throttle } from 'lodash-es';
import { useSoundStore } from '~/stores/modules/sound';
import { Sound } from '~/types/sound';
const props = defineProps({
  autoCenter: {
    type: Boolean,
    default: true,
  },
  bgGlow: {
    type: String,
    default: '',
  },
  isDemoSpin: Boolean,
  isLoading: {
    type: Boolean,
    default: false,
  },
  minOffsetPercentage: {
    type: Number,
    default: 0.9,
  },
  showHorizontalSpinner: {
    type: Boolean,
    default: true,
  },
  spinnerConfigArray: {
    type: Array,
    default: () => [],
  },
  spinnerGap: {
    type: String,
    default: 'gap-xs',
  },
  spinnerHeight: {
    type: String,
    default: 'h-[310px] xl:h-[180px]',
  },
  isUnoReverse: {
    type: Boolean,
    default: false,
  },
  showTick: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['spinEnded']);
const tickColorClass = computed(() =>
  props.isDemoSpin ? 'text-light-3' : 'text-theme-color',
);

const soundStore = useSoundStore();

const soundEnabled = computed(() => soundStore.soundEnabled);

const { play } = useSoundControl({ mute: !soundEnabled.value });

function handleStartSound() {}

function handleEndSound() {
  const level = Math.max(
    ...props.spinnerConfigArray.map((item: any) =>
      getQualityLevel(item.spinnerItems[item.selectedItemPosition].tags),
    ),
  );
  const winSound = `FAST_WIN${level}`;
  const soundKey = winSound as keyof typeof Sound;
  play(Sound[soundKey]);
}

const throttledPlayTickSound = throttle(() => {
  play(Sound.CASE_OPEN_ROLL);
}, 50);

function handleTickSound(_index: number) {
  throttledPlayTickSound();
}
function handleSpinEnded(index: number) {
  const spinnerConfig = props.spinnerConfigArray.map((config: any, i) => {
    if (i === index) {
      return { ...config, spinEnded: true };
    }
    return config;
  });
  const allFinished = spinnerConfig.every(
    (config: { spinEnded: boolean }) => config.spinEnded,
  );
  if (allFinished) {
    handleEndSound();
  }
  emit('spinEnded', index);
}
</script>
