<template>
  <div class="px-lg py-[4px] flex-1 cursor-default relative">
    <!-- 回合状态显示 -->
    <div
      class="absolute left-[16px] top-[6px] z-30 flex h-[37px] items-center justify-center rounded bg-[#4B63B2]/40 px-[10px] text-white"
    >
      <svgo-cases
        v-if="!isFinishedOrCancelled"
        class="mr-md mb-1 size-[15px]"
        :fontControlled="false"
      />
      <Text bold>{{ roundStatus }}</Text>
    </div>
    <div class="relative">
      <!-- <div
        ref="containerRef"
        v-dragscroll
        class="relative w-full overflow-hidden rounded-lg"
        @dragscrollstart="onDragStart"
        @dragscrollmove="disableAutoScroll"
        @dragscrollend="onDragEnd"
      > -->
      <div
        ref="containerRef"
        class="relative w-full overflow-hidden rounded-lg"
      >
        <!-- 箱子列表容器 -->
        <div class="z-10 flex gap-[15px]">
          <!-- tooltip -->
          <template
            v-for="(caseItem, index) in flattenedCases"
            :key="`battle_list_case_${index}`"
          >
            <div ref="casesRef" class="shrink-0">
              <Tooltip trigger="hover">
                <template #trigger>
                  <div class="relative">
                    <MediaDisplay
                      :src="`${caseItem.case_image}`"
                      alt="battle_list_case_image"
                      :class="[
                        'w-[90px] h-[90px] select-none pointer-events-none',
                        {
                          'opacity-30':
                            (status !== BattleStatusType.WAITING &&
                              (currentRound || 0) - 1 !== index) ||
                            status === BattleStatusType.FINISHED,
                        },
                      ]"
                    />
                  </div>
                </template>
                <span class="text-white">{{ caseItem.case_name }}</span>
              </Tooltip>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BattleStatusType, type BattleCase } from '@/types/battles';

const CARD_MARGIN = 90 + 15;

interface Props {
  cases: BattleCase[];
  currentRound: number;
  status: BattleStatusType;
  totalRounds: number;
}

const props = defineProps<Props>();

const { isAutoScrollEnabled } = useCasesAutoScrollControl();

// 容器引用
const containerRef = ref<HTMLElement | null>(null);
const casesRef = ref<HTMLElement[]>([]);

const isDragging = ref(false);

// const onDragStart = () => {
//   isDragging.value = true;
// };

// const onDragEnd = () => {
//   enableAutoScroll();
//   setTimeout(() => {
//     isDragging.value = false;
//   }, 0);
// };

const selectedCase = computed(() => {
  if (casesRef.value) {
    const index =
      props.currentRound > -1
        ? props.currentRound
        : casesRef.value.length + props.currentRound;
    return casesRef.value[index];
  }
  return undefined;
});
const { width: containerWidth } = useElementSize(containerRef);
const { width: selectedWidth } = useElementSize(selectedCase);
const { t } = useI18n();

// 计算属性
const flattenedCases = computed(() => {
  return props.cases.flatMap((caseItem) =>
    Array(caseItem.qty).fill({
      case_image: caseItem.case_image,
      case_name: caseItem.case_name,
    }),
  );
});

const roundStatus = computed(() => {
  if (props.status === BattleStatusType.WAITING) {
    return props.totalRounds;
  }
  if (isFinishedOrCancelled.value) {
    if (props.status === BattleStatusType.CANCELLED) {
      return t('cancelled');
    }
    return t('ended');
  }
  return props.currentRound !== null
    ? `${props.currentRound ?? '0'}/${props.totalRounds}`
    : null;
});

const isFinishedOrCancelled = computed(() =>
  [BattleStatusType.FINISHED, BattleStatusType.CANCELLED].includes(
    props.status,
  ),
);

const isInProgress = computed(
  () =>
    ![
      BattleStatusType.FINISHED,
      BattleStatusType.CANCELLED,
      BattleStatusType.WAITING,
    ].includes(props.status),
);

// 计算滚动位置
const startOffset = computed(() =>
  props.currentRound > -1 ? containerWidth.value / 2 : 0,
);
const endOffset = computed(() =>
  props.currentRound > -1 ? selectedWidth.value / 2 : 0,
);
const scrollPosition = computed(() => {
  if (!isInProgress.value || props.currentRound === null) return null;

  return (
    CARD_MARGIN * (props.currentRound - 1) + endOffset.value - startOffset.value
  );
});

// 监听滚动位置变化
watch(
  scrollPosition,
  (newPosition) => {
    if (
      newPosition !== null &&
      isAutoScrollEnabled.value &&
      !isDragging.value
    ) {
      nextTick(() => {
        containerRef.value?.scrollTo({
          left: newPosition,
          behavior: 'smooth',
        });
      });
    }
  },
  { immediate: true },
);
</script>
