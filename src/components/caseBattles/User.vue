<template>
  <div
    class="relative flex h-full w-[86px] flex-col items-center justify-center"
  >
    <div
      :class="[
        'relative  size-[50px] mb-[5px] transition-transform duration-700',
        {
          'opacity-[0.3]':
            (!isMiddleCard && !spinEnded) || (!isSelectedItem && spinEnded),
        },
      ]"
    >
      <img
        :key="item?.avatar"
        :src="item?.avatar"
        class="relative z-10 h-full w-full rounded-md object-contain"
      />
    </div>
    <div
      class="flex flex-col items-center justify-center w-full"
      :class="[
        {
          'opacity-[0.3]':
            (!isMiddleCard && !spinEnded) || (!isSelectedItem && spinEnded),
        },
      ]"
    >
      <Text
        class="whitespace-nowrap w-[95%] text-white"
        size="small"
        align="center"
        ellipsis
        bold
      >
        {{ item?.nickname }}
      </Text>
    </div>
  </div>
</template>

<script setup lang="ts">
interface IUserItem {
  avatar: string;
  nickname: string;
}

defineProps<{
  item: IUserItem;
  battleMode: number;
  isMiddleCard?: boolean;
  isSelectedItem?: boolean;
  index: number;
  selectedItemPosition: number;
  isHorizontalSpinner?: boolean;
  spinEnded?: boolean;
}>();
</script>
