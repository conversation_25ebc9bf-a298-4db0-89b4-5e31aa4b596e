<template>
  <!-- 路由出口 -->
  <Layout v-if="shouldShowLayout">
    <Suspense>
      <NuxtLayout>
        <NuxtPage />
      </NuxtLayout>
    </Suspense>
  </Layout>
  <Suspense v-else>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </Suspense>
</template>
<script setup>
const route = useRoute();
const noLayoutPages = ['/login', '/live-mng'];

const shouldShowLayout = computed(() => {
  return !noLayoutPages.some((path) => route.path.startsWith(path));
});
</script>
