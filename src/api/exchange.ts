/**
 * 筛选条件
 * @param
 * @returns {V1GetOnSaleGoodsListReply}
 */
export const getHotExchangeFilter = () => {
  return useRequest.get<V1GetGoodsFilterReply>(
    '/api/v1/goods/filter',
    {},
    { server: false },
  );
};

/**
 * 可兑换饰品列表
 */
export const getShopList = (data: V1GetShopListRequest) => {
  return useRequest.post<ApiV1ShopListPost200ResponseData>(
    '/api/v1/shop/list',
    data,
  );
};

/**
 * 03-兑换指定饰品
 */
export const exchangeGoods = (data: V1ShopExchangeGoodsRequest) => {
  return useRequest.post('/api/v1/shop/exchange', data);
};

/**
 * 判断用户是否可以兑换饰品
 */
export const checkExchange = (data: ApiV1ShopCheckExchangePostRequest) => {
  return useRequest.post('/api/v1/shop/check_exchange', data);
};

/**
 *cdk兑换
 */
export const cdkRedeem = (data: ApiV1CdkRedeemPostRequest) => {
  return useRequest.post<ApiV1CdkRedeemPost200ResponseData>(
    '/api/v1/cdk/redeem',
    data,
  );
};
