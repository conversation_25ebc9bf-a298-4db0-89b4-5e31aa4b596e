/**
 * 登录
 */
export const loginSteam = (redirectUrl: string) => {
  return useRequest.get<ApiV1LoginSteamGet200ResponseData>(
    '/api/v1/login/steam',
    {
      back_url: redirectUrl,
    },
  );
};

/**
 * steam登录轮询获取登录token

 * @returns token
 */
export const loginStatus = () => {
  return useRequest.get<ApiV1LoginSteamVerifyGet200ResponseData>(
    '/api/v1/login/status',
  );
};

/**
 * 退出登录
 */
export const logout = () => {
  return useRequest.post('/api/v1/user/logout');
};

/**
 * 登出所有设备
 */
export const logoutAll = () => {
  return useRequest.post('/api/v1/user/logout/all');
};

/**
 * 邮箱注册
 */
export const emailRegister = (data: V1EmailRegisterRequest) => {
  return useRequest.post<V1EmailRegisterReply>(
    '/api/v1/user/email_register',
    data,
  );
};

/**
 * 邮箱登录
 */
export const emailLogin = (data: V1EmailLoginRequest) => {
  return useRequest.post<V1EmailLoginReply>('/api/v1/user/email_login', data);
};

/**
 * 邮箱重置密码
 */
export const emailResetPassword = (data: V1EmailResetPasswordRequest) => {
  return useRequest.post<V1EmailResetPasswordReply>(
    '/api/v1/user/email_reset_password',
    data,
  );
};
