import * as settingApi from '@/api/setting';
import * as exchangeApi from '@/api/exchange';
import * as casesApi from '@/api/cases';
import * as loginApi from '@/api/login';
import * as homeApi from '@/api/home';
import * as coinflipApi from '@/api/coinflip';
import * as fairnessApi from '@/api/fairness';
import * as backpackApi from '@/api/backpack';
import * as depositApi from '@/api/deposit';
import * as rollroomApi from '@/api/rollroom';
import * as battleApi from '@/api/battle';
import * as notificationApi from '@/api/notification';
import * as liveMngApi from '@/api/liveMng';

export default {
  settingApi,
  exchangeApi,
  casesApi,
  loginApi,
  homeApi,
  coinflipApi,
  fairnessApi,
  backpackApi,
  depositApi,
  rollroomApi,
  battleApi,
  notificationApi,
  liveMngApi,
};
