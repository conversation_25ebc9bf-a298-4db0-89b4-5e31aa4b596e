/**
 * 获取主播返利总览
 */
export const getStreameroOverview = () => {
  return useRequest.get<V1GetStreameroOverview>('/api/v1/streamer/overview');
};

/**
 * 获取主播每日收入情况
 */
export const getStreamerIncomeList = () => {
  return useRequest.get<V1GetStreamerIncomeList>(
    '/api/v1/streamer/income/list',
  );
};

/**
 * 获取主播返利明细列表
 */
export const getStreamerRebateRecordList = (
  params: ApiV1StreamerRebateRecordListGetRequest,
) => {
  return useRequest.get<V1GetStreamerRebateRecordList>(
    '/api/v1/streamer/rebate_record/list',
    params,
  );
};

/**
 * 获取主播每日收入情况
 */
export const getStreamerWithdrawnList = (
  params: ApiV1StreamerWithdrawnListGetRequest,
) => {
  return useRequest.get<V1GetStreamerWithdrawnList>(
    '/api/v1/streamer/withdrawn/list',
    params,
  );
};

/**
 * 获取主播权限信息
 */
export const getStreamerPermission = (server: boolean = false) => {
  return useRequest.get<V1GetStreamerPermission>(
    '/api/v1/streamer/permission',
    {},
    { server, ignoreGlobalErrorMessage: true },
  );
};

/**
 * 获取推广用户列表 主播/公会
 */
export const getStreamerUserList = (
  params: ApiV1StreamerUserListGetRequest,
) => {
  return useRequest.get<V1GetStreamerUserList>(
    '/api/v1/streamer/user/list',
    params,
  );
};

/**
 * 获取公会主播列表
 */
export const getStreamerList = (params: ApiV1StreamerListGetRequest) => {
  return useRequest.get<V1GetStreamerList>('/api/v1/streamer/list', params);
};

/**
 * 获取公会主播进出日志
 */
export const getStreamerRecord = (params: ApiV1StreamerRecordGetRequest) => {
  return useRequest.get<V1GetStreamerRecord>('/api/v1/streamer/record', params);
};

/**
 * 获取公会指定主播的权限详情
 */
export const getStreamerPermissionInfo = (
  params: ApiV1StreamerPermissionInfoGetRequest,
) => {
  return useRequest.get<V1GetStreamerPermissionInfo>(
    '/api/v1/streamer/permission/info',
    params,
  );
};

/**
 * 编辑公会主播权限
 */
export const setStreamerPermissionSave = (
  data: ApiV1StreamerPermissionSavePostRequest,
) => {
  return useRequest.post('/api/v1/streamer/permission/save', data);
};

/**
 * 编辑公会主播信息
 */
export const setStreamerGuildStreamerSave = (
  data: ApiV1StreamerGuildStreamerSavePostRequest,
) => {
  return useRequest.post('/api/v1/streamer/guild/streamer_save', data);
};

/**
 * 移除公会主播或主动退出公会
 */
export const removeStreamerGuildLeave = (
  data: ApiV1StreamerGuildLeavePostRequest,
) => {
  return useRequest.post('/api/v1/streamer/guild/leave', data);
};

/**
 * 添加公会主播
 */
export const addStreamerGuildStreamer = (
  data: ApiV1StreamerGuildStreamerAddPostRequest,
) => {
  return useRequest.post('/api/v1/streamer/guild/streamer_add', data);
};
