/**
 * 创建游戏
 * @param {V1CoinflipCreateRequest}
 * @returns {V1CoinflipCreateReply}
 */
export const createGame = (params: V1CoinflipCreateRequest) => {
  return useRequest.post<V1CoinflipCreateReply>(
    '/api/v1/coinflip/create',
    params,
  );
};

/**
 * 加入游戏
 * @param
 * @returns {ApiV1CoinflipCancelIdPost200ResponseData}
 */
export const joinGame = (id: number) => {
  return useRequest.post<ApiV1CoinflipCancelIdPost200ResponseData>(
    `/api/v1/coinflip/challenge/${id}`,
  );
};

/**
 * 取消游戏
 * @param
 * @returns {ApiV1CoinflipCancelIdPost200ResponseData}
 */
export const cancelGame = (id: number) => {
  return useRequest.post<ApiV1CoinflipCancelIdPost200ResponseData>(
    `/api/v1/coinflip/cancel/${id}`,
  );
};

/**
 * 呼叫机器人加入
 * @param
 * @returns {ApiV1CoinflipCancelIdPost200ResponseData}
 */
export const callBot = (id: number) => {
  return useRequest.post<ApiV1CoinflipCancelIdPost200ResponseData>(
    `/api/v1/coinflip/callbot/${id}`,
  );
};

/**
 * 开放游戏列表
 * @param {ApiV1CoinflipListGetRequest}
 * @returns {V1CoinflipListReply}
 */
export const openGameList = (
  params: ApiV1CoinflipListGetRequest,
  server: boolean = false,
) => {
  return useRequest.get<V1CoinflipListReply>('/api/v1/coinflip/list', params, {
    server,
  });
};

/**
 * 我的游戏列表
 * @param {ApiV1CoinflipOngoingGetRequest}
 * @returns {V1CoinflipOngoingReply}
 */
export const myGameList = (server: boolean = false) => {
  return useRequest.get<V1CoinflipOngoingReply>(
    '/api/v1/coinflip/ongoing',
    {},
    {
      server,
    },
  );
};

/**
 * 历史游戏列表
 * @param {ApiV1CoinflipHistoryGetRequest}
 * @returns {V1CoinflipHistoryReply}
 */
export const historyGameList = (params: ApiV1CoinflipHistoryGetRequest) => {
  return useRequest.get<V1CoinflipHistoryReply>(
    '/api/v1/coinflip/history',
    params,
  );
};
