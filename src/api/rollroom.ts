/**
 * 列表
 * @param
 * @returns {V1GetOnSaleGoodsListReply}
 */
export const getRollroomList = (
  params: ApiV1RollroomListGetRequest,
  server: boolean = false,
) => {
  return useRequest.get<ApiV1RollroomListGet200ResponseData>(
    '/api/v1/rollroom/list',
    params,
    { server, lazy: false },
  );
};

/**
 * roll房详情
 * @param
 * @returns {ApiV1RollroomDetailGet200ResponseData}
 */
export const getRollroomDetail = (roomId: number) => {
  return useRequest.get<ApiV1RollroomDetailGet200ResponseData>(
    '/api/v1/rollroom/detail',
    { room_id: roomId },
  );
};
/**
 * 奖品列表
 * @param
 * @returns {ApiV1RollroomAwardsGet200ResponseData}
 */
export const getRollroomAwards = (roomId: number) => {
  return useRequest.get<ApiV1RollroomAwardsGet200ResponseData>(
    '/api/v1/rollroom/awards',
    { room_id: roomId },
  );
};
/**
 * 参加用户
 * @param {ApiV1RollroomUsersGetRequest}
 * @returns {ApiV1RollroomUsersGet200ResponseData}
 */
export const getRollroomUsers = (params: ApiV1RollroomUsersGetRequest) => {
  return useRequest.get<ApiV1RollroomUsersGet200ResponseData>(
    '/api/v1/rollroom/users',
    params,
  );
};
/**
 * 加入roll房
 * @param
 * @returns {ApiV1RollroomJoinIdPost200ResponseData}
 */
export const joinRollroom = (roomId: number) => {
  return useRequest.post<ApiV1RollroomJoinIdPost200ResponseData>(
    `/api/v1/rollroom/join/${roomId}`,
    {},
  );
};
