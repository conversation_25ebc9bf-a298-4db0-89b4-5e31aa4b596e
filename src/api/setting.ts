/**
 * 获取用户信息
 * @param
 * @returns {ApiV1UserInfoGet200Response}
 */
export const getUserInfo = (server: boolean = false, params = {}) => {
  return useRequest.get<ApiV1UserInfoGet200ResponseData>(
    '/api/v1/user/info',
    params,
    { server, ignoreGlobalErrorMessage: true },
  );
};

/**
 * 设置交易链接
 */
export const setTradeUrl = (data: ApiV1UserTradeUrlPostRequest) => {
  return useRequest.post('/api/v1/user/trade_url', data, { server: true });
};

/**
 * 设置邮箱发送验证码
 */
export const emailApply = (data: V1SendUserEmailCodeRequest) => {
  return useRequest.post<V1SendUserEmailCodeReply>(
    '/api/v1/user/email_apply',
    data,
    { server: true },
  );
};

/**
 * 设置邮箱确认
 */
export const emailVerify = (data: V1SetUserEmailRequest) => {
  return useRequest.post('/api/v1/user/email_verify', data, { server: true });
};

/**
 * 修改用户名
 */
export const userNameUpdate = (data: ApiV1UserUsernamePostRequest) => {
  return useRequest.post('/api/v1/user/username', data, { server: true });
};

/**
 * 等级配置信息
 */
export const getLevelConfig = () => {
  return useRequest.get<V1GetLevelConfigReply>(
    '/api/v1/cases/level_config',
    {},
    // { server: true },
  );
};

/**
 * 用户推荐总览
 */
export const getCommissionOverview = () => {
  return useRequest.get<ApiV1UserCommissionOverviewGet200ResponseData>(
    '/api/v1/user/commission/overview',
    {},
  );
};

/**
 * 推荐收益领取
 */
export const claimCommission = () => {
  return useRequest.post('/api/v1/user/commission/claim', {}, { server: true });
};

/**
 * 佣金明细列表
 */
export const getCommissionDetails = (
  data: ApiV1UserCommissionDetailsGetRequest,
) => {
  return useRequest.get<ApiV1UserCommissionDetailsGet200ResponseData>(
    '/api/v1/user/commission/details',
    data,
  );
};

/**
 * 推荐用户列表
 */
export const getReferredUsers = (
  data: ApiV1UserCommissionReferredGetRequest,
) => {
  return useRequest.get<ApiV1UserCommissionReferredGet200ResponseData>(
    '/api/v1/user/commission/referred',
    data,
  );
};

/**
 * 人机验证
 */
export const cfCheck = (data: ApiV1CfCheckPostRequest) => {
  return useRequest.post<ApiV1CfCheckPost200ResponseData>(
    '/api/v1/cf_check',
    data,
    { server: false },
  );
};

/**
 * 头像列表
 */
export const avatarList = () => {
  return useRequest.get<V1GetAvatarMaterialListReply>(
    '/api/v1/user/avatar_materials',
    {},
  );
};
/**
 * 设置头像
 */
export const setAvatar = (avatar: string) => {
  return useRequest.post<V1SetUserAvatarReply>('/api/v1/user/avatar', {
    avatar,
  });
};
