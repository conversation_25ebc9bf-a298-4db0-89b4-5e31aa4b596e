/**
 * 战报
 * @param
 * @returns {V1GetOnSaleGoodsListReply}
 */
export const getBroadcast = (type: number, pageSize: number) => {
  return useRequest.get<V1GetCasesRecentReply>(
    '/api/v1/cases/recent',
    {
      type,
      page: 1,
      page_size: pageSize,
    },
    { server: false, lazy: false },
  );
};
/**
 * roll房
 * @param
 * @returns {ApiV1RollroomRecommendGet200ResponseData}
 */
export const getRollroom = () => {
  return useRequest.get<ApiV1RollroomRecommendGet200ResponseData>(
    '/api/v1/rollroom/recommend',
    {
      number: 4,
    },
    { server: false, lazy: false },
  );
};

/**
 * 综合元数据
 */
export const getMetadata = (server: boolean) => {
  return useRequest.get<V1GetMetadataReply>(
    '/api/v1/metadata',
    {},
    {
      server,
    },
  );
};

/**
 * 获取公告列表
 */
export const getNotice = () => {
  return useRequest.get<ApiV1NotifyNoticeGet200ResponseData>(
    '/api/v1/notify/notice',
  );
};

/**
 * 获取活动列表
 */
export const getActivitis = () => {
  return useRequest.get<ApiV1NotifyPopupGet200ResponseData>(
    '/api/v1/notify/popup',
  );
};
