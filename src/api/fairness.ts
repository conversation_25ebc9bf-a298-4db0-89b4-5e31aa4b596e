import type { V1GetBattleListReply } from './openapi';

/**
 * 获取我的宝箱历史
 * @param {GetCasesMyRequest}
 * @returns {V1GetCasesMyReply}
 */
export const getCasesList = (params: GetCasesMyRequest) => {
  return useRequest.get<V1GetCasesMyReply>('/api/v1/cases/my', params);
};
/**
 * 获取抛硬币历史
 * @param {ApiV1CoinflipHistoryGetRequest}
 * @returns {V1CoinflipHistoryReply}
 */
export const getCoinflipList = (params: ApiV1CoinflipHistoryGetRequest) => {
  return useRequest.get<V1CoinflipHistoryReply>('/api/v1/coinflip/history', {
    list_type: 1,
    ...params,
  });
};
/**
 * 获取对战历史
 * @param {GetBattleListRequest}
 * @returns {V1GetBattleListReply}
 */
export const getBattlesList = (params: { page: number; page_size: number }) => {
  return useRequest.get<V1GetBattleListReply>('/api/v1/battle/my', {
    ...params,
    order_by: 'joined_at',
    order: 2, // 降序
    type: 0,
  });
};
/**
 * 获取roolroom历史
 * @param {ApiV1RollroomHistoryGetRequest}
 * @returns {ApiV1RollroomHistoryGet200ResponseData}
 */
export const getRollList = (params: ApiV1RollroomHistoryGetRequest) => {
  return useRequest.get<ApiV1RollroomHistoryGet200ResponseData>(
    '/api/v1/rollroom/history',
    params,
  );
};
/**
 * 服务器种子获取
 * @param
 * @returns
 */
export const getServerSeed = () => {
  return useRequest.get<{ hashed_seed: string }>(
    '/api/v1/meta/server_seed',
    {},
    { server: true, lazy: false },
  );
};
/**
 * 重置服务器种子
 * @param
 * @returns
 */
export const resetServerSeed = () => {
  return useRequest.post<{ hashed_seed: string }>(
    '/api/v1/meta/regenerate_server_seed',
  );
};
