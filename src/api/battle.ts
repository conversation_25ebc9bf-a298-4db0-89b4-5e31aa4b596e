/**
 * 创建战斗
 */
export const createBattle = (data: V1CreateBattleRequest) => {
  return useRequest.post<V1CreateBattleReply>('/api/v1/battle/create', data);
};

/**
 * 对战详情
 */
export const getBattleInfo = (data: GetBattleInfoRequest) => {
  return useRequest.get<V1GetBattleInfoReply>('/api/v1/battle/info', data);
};

/**
 * 加入战斗
 */
export const joinBattle = (data: V1JoinBattleRequest) => {
  return useRequest.post<V1JoinBattleReply>('/api/v1/battle/join', data);
};

/**
 * 取消战斗
 */
export const cancelBattle = (data: V1CancelBattleRequest) => {
  return useRequest.post<V1CancelBattleReply>('/api/v1/battle/cancel', data);
};

/**
 * 调用bot
 */
export const callBot = (data: V1BattleCallBotRequest) => {
  return useRequest.post<V1JoinBattleReply>('/api/v1/battle/call_bot', data);
};

/**
 * 获取对战列表
 */
export const getBattleList = (data: GetBattleListRequest) => {
  return useRequest.get<V1GetBattleListReply>('/api/v1/battle/list', data);
};

/**
 * 获取我的对战列表
 */
export const getMyBattleList = (data: GetMyBattleListRequest) => {
  return useRequest.get<V1GetBattleListReply>('/api/v1/battle/my', data);
};

/**
 * 获取对战某轮的开箱记录
 */
export const battleRound = (data: GetBattleRoundRequest) => {
  return useRequest.get<V1GetBattleRoundReply>('/api/v1/battle/round', data);
};

/**
 * 检查是否可以加入对战
 */
export const checkJoin = (data: V1CheckJoinBattleRequest) => {
  return useRequest.post<V1CheckJoinBattleReply>(
    '/api/v1/battle/check_join',
    data,
  );
};
