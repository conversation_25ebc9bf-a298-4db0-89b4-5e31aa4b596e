/**
 * 充值卡充值
 */
export const redeemGiftCode = (data: ApiV1TradeGiftCodesRedeemPostRequest) => {
  return useRequest.post('/api/v1/trade/gift_codes/redeem', data);
};

/**
 * 充值方式列表
 */
export const getPaymentMethods = () => {
  return useRequest.get<ApiBffInterfaceV1PaymentMethodsReply>(
    '/api/v1/trade/payment_methods',
  );
};

/**
 * 获取加密货币地址
 */
export const getCryptoDepositAddress = (currency: string) => {
  return useRequest.get<ApiV1TradeCryptoDepositTypeGet200ResponseData>(
    `/api/v1/trade/crypto/deposit/${currency}`,
  );
};

/**
 * 虚拟币充值-汇率获取
 */
export const getCryptoExchangeRate = () => {
  return useRequest.get<ApiV1TradeCryptoExchangeRateGet200ResponseData>(
    '/api/v1/trade/crypto/exchange_rate',
  );
};

/**
 * 流水结算记录
 */
export const getTransactions = (data: ApiV1TradeTransactionsGetRequest) => {
  return useRequest.get<ApiV1TradeTransactionsGet200ResponseData>(
    '/api/v1/trade/transactions',
    data,
  );
};

/**
 * 充值记录
 */
export const getDepositRecords = (data: ApiV1TradeDepositRecordsGetRequest) => {
  return useRequest.get<ApiV1TradeDepositRecordsGet200ResponseData>(
    '/api/v1/trade/deposit_records',
    data,
  );
};

/**
 * 提现记录
 */
export const getWithdrawRecords = (
  data: ApiV1TradeWithdrawRecordsGetRequest,
) => {
  return useRequest.get<ApiV1TradeWithdrawRecordsGet200ResponseData>(
    '/api/v1/trade/withdraw_records',
    data,
  );
};

/**
 * 流水记录类型筛选项
 */
export const getTradeType = () => {
  return useRequest.get<ApiV1TradeTradeTypeGet200ResponseData>(
    '/api/v1/trade/trade_type',
  );
};

/**
 * 充值-提交
 */
export const createDeposit = (data: ApiBffInterfaceV1DepositRequest) => {
  return useRequest.post<ApiBffInterfaceV1DepositReply>(
    '/api/v1/trade/deposit',
    data,
  );
};

/**
 * 充值-查询
 */
export const getDepositByRechargeNo = (rechargeNo: string) => {
  return useRequest.get<ApiBffInterfaceV1RechargeItem>(
    `/api/v1/trade/deposit/by_recharge_no/${rechargeNo}`,
  );
};

/**
 * 充值-提交
 */
export const createDepositGateway = (
  data: ApiV1TradeGatewayDepositPostRequest,
) => {
  return useRequest.post<any>('/api/v1/trade/gateway/deposit', data);
};
