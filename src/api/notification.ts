/**
 * 消息列表
 * @param {ApiV1NotifyGetRequest}
 * @returns {ApiV1NotifyGet200ResponseData}
 */
export const getList = (
  params: ApiV1NotifyGetRequest,
  server: boolean = false,
) => {
  return useRequest.get<ApiV1NotifyGet200ResponseData>(
    '/api/v1/notify',
    params,
    { server },
  );
};
/**
 * 消息详情
 * @param
 * @returns {ApiV1NotifyInfoGet200ResponseData}
 */
export const getDetail = (id: number) => {
  return useRequest.get<ApiV1NotifyInfoGet200ResponseData>(
    '/api/v1/notify/info',
    {
      id,
    },
    { server: true },
  );
};
/**
 * 全部已读
 * @param
 * @returns
 */
export const readAll = () => {
  return useRequest.post('/api/v1/notify/read');
};

/**
 * 全部已读
 * @param { ApiV1UserJoinGuildPostRequest}
 * @returns
 */
export const joinGuild = (params: ApiV1UserJoinGuildPostRequest) => {
  return useRequest.post('/api/v1/user/join_guild', params);
};
