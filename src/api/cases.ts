/**
 * 获取宝箱列表
 * @param {V1GetCasesListRequest}
 * @returns {V1GetCasesListReply}
 */
export const getCasesList = (
  params: V1GetCasesListRequest,
  server: boolean = false,
) => {
  return useRequest.post<V1GetCasesListReply>('/api/v1/cases/list', params, {
    server,
    lazy: false,
  });
};
/**
 * 获取宝箱详情
 * @param {GetCasesInfoRequest}
 * @returns {V1GetCasesInfoReply}
 */
export const getCasesInfo = (
  params: GetCasesInfoRequest,
  server: boolean = false,
) => {
  return useRequest.get<V1GetCasesInfoReply>('/api/v1/cases/info', params, {
    server,
    lazy: false,
  });
};
/**
 * 开宝箱
 * @param {V1OpenCasesRequest}
 * @returns {V1OpenCasesReply}
 */
export const openCases = (params: V1OpenCasesRequest) => {
  return useRequest.post<V1OpenCasesReply>('/api/v1/cases/open', params);
};
/**
 * 开等级宝箱
 * @param {V1OpenLevelCasesRequest}
 * @returns {V1OpenCasesReply}
 */
export const openLevelCases = (params: V1OpenLevelCasesRequest) => {
  return useRequest.post<V1OpenCasesReply>('/api/v1/cases/open_level', params);
};
/**
 * 个人开箱历史
 * @param {GetCasesMyRequest}
 * @returns {V1GetCasesMyReply}
 */
export const myCases = (params: GetCasesMyRequest, server: boolean) => {
  return useRequest.get<V1GetCasesMyReply>('/api/v1/cases/my', params, {
    server,
  });
};
/**
 * 最新开箱记录
 * @param
 * @returns {V1GetCasesRecentReply}
 */
export const recentCases = () => {
  return useRequest.get<V1GetCasesRecentReply>('/api/v1/cases/recent');
};
/**
 * 转换
 * @param {goods: ApiV1BackpackPawnPostRequestGoodsInner}
 * @returns {ApiV1BackpackPawnPost200ResponseData}
 */
export const convertToCoin = (
  goods: ApiV1BackpackPawnPostRequestGoodsInner[],
) => {
  return useRequest.post<ApiV1BackpackPawnPost200ResponseData>(
    '/api/v1/backpack/pawn',
    {
      goods,
    },
  );
};
