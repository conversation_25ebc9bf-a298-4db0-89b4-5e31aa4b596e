/**
 * 个人背包列表(分页排序)
 */
export const getBackpackList = (data: ApiV1BackpackListGetRequest) => {
  return useRequest.get<V1GetBackpackListReply>('/api/v1/backpack/list', data);
};
/**
 * 饰品兑换成硬币
 */
export const pawnGoods = (data: ApiV1BackpackPawnPostRequest) => {
  return useRequest.post<ApiV1BackpackPawnPost200ResponseData>(
    '/api/v1/backpack/pawn',
    data,
  );
};

/**
 * 饰品提取
 */
export const extractGoods = (data: ApiV1BackpackExtractPostRequest) => {
  return useRequest.post('/api/v1/backpack/extract', data);
};

/**
 * 饰品提取记录
 */
export const getExtractProgress = (
  data: ApiV1BackpackExtractListGetRequest,
) => {
  return useRequest.get<ApiV1BackpackExtractListGet200ResponseData>(
    '/api/v1/backpack/extract/list',
    data,
  );
};

/**
 * 背包出入库记录列表
 */
export const getRecordList = (data: ApiV1BackpackRecordListGetRequest) => {
  return useRequest.get<V1GetBackpackRecordListReply>(
    '/api/v1/backpack/record/list',
    data,
  );
};

/**
 * 背包数据统计
 */
export const getBackpackStatistics = () => {
  return useRequest.get<V1GetBackpackStatisticsReply>(
    '/api/v1/backpack/statistics',
  );
};

/**
 * 取消提取饰品
 */
export const cancelExtract = (data: ApiV1BackpackExtractCancelPostRequest) => {
  return useRequest.post('/api/v1/backpack/extract/cancel', data);
};

/**
 *  检查是否可以提取饰品到steam
 */
export const checkExtract = (data: ApiV1BackpackExtractPostRequest) => {
  return useRequest.post('/api/v1/backpack/check_extract', data);
};
