@layer tailwind-base {
  @tailwind base;
}

@layer tailwind-utilities {
  @tailwind utilities;
  @tailwind components;

  .gradient-to-r {
    @apply bg-gradient-to-r from-primary-500 to-primary-50;
  }

  .input-warning {
    @apply text-warning-500 mt-2 font-normal text-sm;
  }
}

@layer components {
  .nav-active::after {
    @apply block absolute;
    content: '';
    left: 50%;
    bottom: -26px;
    transform: translateX(-50%);
    width: 100%;
    height: 4px;
    background-color: #ffe100;
  }

  .m-shop-header-gradient {
    background: radial-gradient(circle at 100% 0%, #7995fe, #348bff),
      radial-gradient(circle at 0% 0%, #35e1ff, #348bff);
  }

  .m-accessory-card-gradient {
    background: radial-gradient(#e0e3f3, #8b92b7);
  }

  .base-color {
    color: #b0aaa7;
  }

  .sub-text {
    color: #b0aaa7;
    cursor: pointer;
    font-size: 14px;
  }

  .sub-text-hover:hover {
    font-weight: bold;
    color: #ffe100;
  }

  .common-active:hover {
    color: #ffe100;
  }

  .gradient-color {
    background: linear-gradient(90deg, #ffe100 0%, #fef4bc 100%);
  }

  .card-item-filter {
    background-size: 200% 200%;
    background-position: center center;
    filter: blur(30px) brightness(1) contrast(100%) grayscale(0%)
      hue-rotate(0deg) invert(0%) opacity(100%) saturate(100%) sepia(0%)
      drop-shadow(0px 0px 100px #000000);
  }

  .card-popover-sub-bg {
    background: rgba(255, 255, 255, 0.08);
  }

  .card-popover-tag-color {
    background: linear-gradient(90deg, #fdd479 0%, #d261a8 49%, #6260bd 100%);
  }

  .card-popover-center-color {
    background: rgba(0, 0, 0, 0.15);
  }

  .hide-scrollbar::-webkit-scrollbar {
    width: 0;
  }

  .card-popover-triangle {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid white;
  }

  .common-price {
    color: #ffe100;
    font-size: 24px;
  }

  .common-price::before {
    content: '￥';
    /* margin-right: 5px; */
  }

  .common-btn1 {
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    font-weight: 600;
    color: #ffe100;
    margin-right: 20px;
    cursor: pointer;
  }

  .common-btn2 {
    background: linear-gradient(90deg, #ffe100 0%, #fef4bc 100%);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    text-align: center;
    cursor: pointer;
  }

  .flex-layout {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
  }
}

:root {
  --surface-0: 255 255 255;
  --surface-50: 249 250 251;
  --surface-100: 243 244 246;
  --surface-200: 229 231 235;
  --surface-300: 209 213 219;
  --surface-400: 156 163 175;
  --surface-500: 107 114 128;
  --surface-600: 75 85 99;
  --surface-700: 55 65 81;
  --surface-800: 31 41 55;
  --surface-900: 17 24 39;
  --surface-950: 8 8 8;

  --theme-color: 248 184 56;
  --sub-text: 215 215 215;
  --black-text: 17 23 41;
  --highlight-text: 215 215 215;
  --primary-text: 255 255 255;
  --bg-black: 22 25 28;
  --bg-gray: 36 41 46;
  --bg-light-gray: 51 55 65;

  --green-1: 62 255 149;
  --red-1: 255 85 85;

  --light-1: 220 220 220;
  --light-2: 146 147 166;
  --light-3: 78 80 88;

  --bg-opacity: 1;

  --dark: 31 40 46;
  --dark-1: 80 100 160;
  --dark-2: 32 38 59;
  --dark-3: 21 26 41;
  --dark-4: 5 6 10;
  --dark-5: 39 50 79;
  --dark-6: 19 21 27;
  --dark-7: 20 20 25;

  --gray-1: 146 156 166;
  --gray-2: 36 41 46;
  --gray-3: 75 86 121;

  --purple-1: 125 144 202;
  --purple-2: 137 116 255;
}
