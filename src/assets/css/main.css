html,
body {
  margin: 0 !important;
  padding: 0 !important;
  /* background-color: rgb(10 13 20 / var(--bg-opacity)); */
  background: #0a0d14;
  overflow-x: hidden;
  scrollbar-gutter: stable;
  font-size: 14px;
  font-family: '<PERSON><PERSON><PERSON>', 'Helvetica Neue', 'Arial', 'Helvetica',
    'PingFang SC', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
body {
  @apply hide-scrollbar;
}

.n-modal-mask {
  background: rgba(255, 255, 255, 0.105);
  backdrop-filter: blur(6px) brightness(100%);
}

.n-button {
  min-width: inherit;
  min-height: inherit;
}

.page {
  position: relative;
  z-index: 0;
  min-height: 100vh;
  overflow: hidden;
}

h1 {
  font-family: Flama-Bold, sans-serif;
  font-weight: 800;
  line-height: 1.2;
  font-size: 42px;
}

h2 {
  line-height: 1.2;
  font-size: 20px;
}

h3 {
  line-height: 1.2;
  font-size: 16px;
}

h4 {
  line-height: 1.2;
  font-size: 14px;
}

h5 {
  line-height: 1.2;
  font-size: 12px;
}

h6 {
  line-height: 1.2;
  font-size: 10px;
}

img[data-src] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img.loaded {
  opacity: 1;
}

.item-shadow {
  filter: drop-shadow(0px 14px 9px rgba(0, 0, 0, 0.4));
}

input[type='password']::-ms-reveal {
  display: none;
}

input[type='password']::-ms-clear {
  display: none;
}

input[type='password']::-o-clear {
  display: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}

input:-internal-autofill-selected {
  background-color: transparent;
}

/* naiveui select hover字体 */
.n-base-select-option--pending {
  color: #929ca6;
}

/* 预览解析按钮 */

button.pswp__button--analyze-button {
  font-size: 14px;
  color: #fff;
  width: 100px;
}

/* 预览弹窗z-index 默认100000 */
.toast-avatar {
  z-index: 100001;
}

/* naiveui notification icon */
.toast-avatar .n-notification__avatar {
  padding-bottom: 6px;
}

/* naiveui qr-code 外层div */
.n-qr-code {
  box-sizing: content-box;
}

.n-input .n-input-wrapper:focus-visible {
  outline: none !important;
}

.n-input::before,
.n-input::after {
  outline: none !important;
  border: 0 !important;
}

.n-avatar {
  background: transparent !important;
}

.n-base-select-menu {
  border: 1px solid #323a51;
}

.n-base-select-menu .n-base-select-option::before {
  right: 0px;
  left: 0px;
}

.n-base-select-option__content {
  /* padding: 0 10px !important; */
}

.n-base-select-menu-option-wrapper {
  padding: 8px !important;
}

.n-badge .n-badge-sup {
  height: 16px;
  padding: 0 5px;
  left: auto;
  right: 0;
  transform: translateX(0);
}

.n-dialog__close {
  font-size: 20px;
  color: white;
}

.n-dialog__close-position .n-dialog__close {
  margin: 12px 24px 0 0;
}

.tag-primary-green {
  background-color: #01bf4d1a;
  --tw-text-opacity: 1;
  color: rgb(1 191 77 / var(--tw-text-opacity));
}

.tag-primary-red {
  background-color: #ff5c5c1a;
  --tw-text-opacity: 1;
  color: rgb(255 92 92 / var(--tw-text-opacity));
}

.tag-primary-yellow {
  background-color: #e9b10e1a;
  --tw-text-opacity: 1;
  color: rgb(233 177 14 / var(--tw-text-opacity));
}

.tag-primary-grey {
  background-color: #9293a61a;
  --tw-text-opacity: 1;
  color: rgb(146 147 166 / var(--tw-text-opacity));
}

.n-drawer-mask {
  backdrop-filter: blur(6px) brightness(100%);
  background: hsla(0, 0%, 100%, 0.105);
}
