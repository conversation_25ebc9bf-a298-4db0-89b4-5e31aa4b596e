@use 'sass:map';

:root {
  /* 主题色 */
  --primary: #ffa22f;
  --primary-hover: #f8b838;
  --primary-active: #f3740c;
  --primary-light: #ffcf70;
  --primary-light-hover: #ffe5b3;
  --primary-light-active: #ffc249;
  --primary-border: #ffe8bb;
  --primary-border-bottom: #f8b838;
  --primary-active-border: #ffce6d;
  --primary-active-border-bottom: #c6932d;
  --primary-hover-border: #ffe8bb;
  --primary-hover-border-bottom: #f8b838;

  --purple: #524fe5;
  --purple-active: #2d2ac4;
  --purple-light: #8974ff;
  --purple-hover: #8583f7;
  --purple-light-active: #4960cd;
  --purple-light-hover: #c4baff;
  --purple-border: #7088ff;
  --purple-border-bottom: #7088ff;
  --purple-active-border: #3b56db;
  --purple-active-border-bottom: #4960cd;
  --purple-hover-border: #a1b1ff;
  --purple-hover-border-bottom: #8a9beb;

  /* 背景 */
  --bg-primary: var(--primary);
  --bg-purple: var(--purple);

  /* 边框 */
  --border-primary: var(--primary-border);
  --border-purple: var(--purple-border);

  /* 间距 */
  --spacing-xxs: 1px;
  --spacing-xs: 2px;
  --spacing-sm: 4px;
  --spacing-md: 8px;
  --spacing-lg: 16px;
  --spacing-xl: 32px;
  --spacing-xxl: 64px;

  /* 圆角 */
  --radius-xxs: 1px;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-xl: 32px;
  --radius-xxl: 64px;

  /* z-index */
  --z-modal: 1000;
  --z-popup: 1100;
  --z-tooltip: 1200;
}

$spacing-sizes: ('xxs', 'xs', 'sm', 'md', 'lg', 'xl', 'xxl');
$gradient-colors: (
  'primary': (
    text: #11141e,
    hover-text: #11141e,
    active-text: #11141e,
  ),
  'purple': (
    text: #fff,
    hover-text: #fff,
    active-text: #fff,
  ),
);

// 间距
@mixin spacing($property, $size) {
  #{$property}: var(--spacing-#{$size});
}

.gap {
  @each $size in $spacing-sizes {
    &-#{$size} {
      @include spacing('gap', $size);
    }
  }
}

.radius {
  @each $size in $spacing-sizes {
    &-#{$size} {
      @include spacing('border-radius', $size);
    }
  }
}

// padding
@each $size in $spacing-sizes {
  .p-#{$size} {
    @include spacing('padding', $size);
  }
}

// padding-x 和 padding-y
@each $size in $spacing-sizes {
  .px-#{$size} {
    @include spacing('padding-left', $size);
    @include spacing('padding-right', $size);
  }

  .py-#{$size} {
    @include spacing('padding-top', $size);
    @include spacing('padding-bottom', $size);
  }
}

// 单独方向的padding
@each $size in $spacing-sizes {
  .pt-#{$size} {
    @include spacing('padding-top', $size);
  }

  .pb-#{$size} {
    @include spacing('padding-bottom', $size);
  }

  .pl-#{$size} {
    @include spacing('padding-left', $size);
  }

  .pr-#{$size} {
    @include spacing('padding-right', $size);
  }
}

// margin-x 和 margin-y
@each $size in $spacing-sizes {
  .mx-#{$size} {
    @include spacing('margin-left', $size);
    @include spacing('margin-right', $size);
  }

  .my-#{$size} {
    @include spacing('margin-top', $size);
    @include spacing('margin-bottom', $size);
  }
}

@mixin negative-spacing($property, $size) {
  #{$property}: calc(-1 * var(--spacing-#{$size}));
}

// margin
@each $size in $spacing-sizes {
  .mt-#{$size} {
    @include spacing('margin-top', $size);
  }

  .mb-#{$size} {
    @include spacing('margin-bottom', $size);
  }

  .ml-#{$size} {
    @include spacing('margin-left', $size);
  }

  .mr-#{$size} {
    @include spacing('margin-right', $size);
  }

  .-mt-#{$size} {
    @include negative-spacing('margin-top', $size);
  }

  .-mb-#{$size} {
    @include negative-spacing('margin-bottom', $size);
  }

  .-ml-#{$size} {
    @include negative-spacing('margin-left', $size);
  }

  .-mr-#{$size} {
    @include negative-spacing('margin-right', $size);
  }

  .-mx-#{$size} {
    @include negative-spacing('margin-left', $size);
    @include negative-spacing('margin-right', $size);
  }

  .-my-#{$size} {
    @include negative-spacing('margin-top', $size);
    @include negative-spacing('margin-bottom', $size);
  }
}

@mixin gradient-button-with-clip($color) {
  $color-map: map.get($gradient-colors, $color);
  $text-color: if($color-map, map.get($color-map, 'text'), #fff);

  color: $text-color;
  box-sizing: border-box;
  position: relative;
  font-weight: 500;
  background:
    radial-gradient(
        100% 100% at 50% 0%,
        var(--#{$color}) 0%,
        var(--#{$color}-light) 100%
      )
      padding-box,
    linear-gradient(
        180deg,
        var(--#{$color}-border) 0%,
        var(--#{$color}-border-bottom) 100%
      )
      border-box;
  border: 1px solid transparent !important;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;

  &:hover {
    $hover-text: if($color-map, map.get($color-map, 'hover-text'), #fff);
    color: $hover-text !important;

    background:
      radial-gradient(
          100% 100% at 50% 0%,
          var(--#{$color}-hover) 0%,
          var(--#{$color}-light-hover) 100%
        )
        padding-box,
      linear-gradient(
          180deg,
          var(--#{$color}-hover-border) 0%,
          var(--#{$color}-hover-border-bottom) 100%
        )
        border-box !important;

    &.n-button .n-button__state-border {
      display: none;
    }
  }

  &:focus,
  &.n-button__state-border:focus {
    $active-text: if($color-map, map.get($color-map, 'active-text'), #fff);
    color: $active-text !important;
    background-color: var(--#{$color}-active) !important;
    &.n-button .n-button__state-border {
      border: 1px solid transparent !important;
    }
  }

  &:active {
    $active-text: if($color-map, map.get($color-map, 'active-text'), #fff);
    color: $active-text !important;

    background:
      radial-gradient(
          100% 100% at 50% 0%,
          var(--#{$color}-active) 0%,
          var(--#{$color}-light-active) 100%
        )
        padding-box,
      linear-gradient(
          180deg,
          var(--#{$color}-active-border) 0%,
          var(--#{$color}-active-border-bottom) 100%
        )
        border-box !important;

    &.n-button .n-button__state-border {
      display: none;
    }
  }

  &.n-button .n-button__border {
    border: 0 !important;
  }

  // 禁用状态
  &.n-button--disabled,
  &.n-button--disabled:hover,
  &.n-button--disabled:focus,
  &.n-button--disabled:focus-visible {
    background: #262b3d !important;
    color: #464c64 !important;
    border: 1px solid #464c64 !important;
  }
}

// 按钮类
.btn-gradient-primary {
  @include gradient-button-with-clip('primary');
}

.btn-gradient-purple {
  @include gradient-button-with-clip('purple');
}

.btn-gradient-gray {
  background: #2e3757 !important;
  color: #fff !important  ;
  border: 0 !important;

  &:hover {
    border: 0 !important;
    background: #576187 !important;
    color: #fff !important;
  }

  &:active {
    background: #1f2744 !important;
  }

  &.n-button {
    .n-button__border {
      border: 0 !important;
    }

    .n-button__state-border {
      display: none;
    }
  }
}
