import { reactive } from 'vue';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';

const DEFAULT_FILTER_PARAMS = {
  search: '',
  order_type: null,
} as const;

const createFormSchema = () => {
  const { t } = useI18n();

  const sortOptions = computed(() => [
    {
      label: () => t('high_value'),
      value: '2',
      width: '250px',
    },
    {
      label: () => t('low_value'),
      value: '1',
    },
  ]);

  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      placeholder: computed(() => t('enter_skin_name_to_search')),
      component: 'n-input-search',
      auth: true,
      value: DEFAULT_FILTER_PARAMS.search,
      listeners: {
        'complete:select': true,
      },
    },
    order_type: {
      key: 'order_type',
      type: 'select',
      component: 'n-select',
      value: DEFAULT_FILTER_PARAMS.order_type,
      placeholder: computed(() => t('default_sorting')),
      consistentMenuWidth: false,
      options: sortOptions,
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

export { createFormSchema };
