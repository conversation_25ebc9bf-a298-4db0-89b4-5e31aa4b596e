import type { DataTableColumns } from 'naive-ui';
import { createCaseColumns } from './fairnessCaseColumnModel';
import { createBattleColumns } from './fairnessBattleColumnModel';
// import { createCoinflipColumns } from './fairnessCoinflipColumnModel';
import { createRollColumns } from './fairnessRollColumnModel';
import FairnessCases from '~/components/fairness/Cases.vue';
// import FairnessCoinflip from '~/components/fairness/Coinflip.vue';
import FairnessBattles from '~/components/fairness/battles/index.vue';
import FairnessRoll from '~/components/fairness/Roll.vue';
import type { PaginatorType } from '~/types/base';
const createMenuSchema = () => {
  const { t } = useI18n();
  const caseColumns = createCaseColumns();
  const battleColumns = createBattleColumns();
  // const coinflipColumns = createCoinflipColumns();
  type MenuType = {
    key: string;
    label: any;
    columns: DataTableColumns<any>;
    paginator: PaginatorType;
    data: any[];
    api: string;
    component: Component;
    restrictIP: boolean;
  };
  const menus = reactive<{
    [key: string]: MenuType;
  }>({
    cases: {
      key: 'cases',
      label: computed(() => t('cases')),
      columns: caseColumns,
      paginator: {
        page_size: 10,
        total: 0,
        page: 1,
      },
      data: [],
      api: 'getCasesList',
      component: shallowRef(FairnessCases),
      restrictIP: true,
    },
    'case-battles': {
      key: 'case-battles',
      label: computed(() => t('case_battles')),
      columns: battleColumns,
      paginator: {
        page_size: 10,
        total: 0,
        page: 1,
      },
      data: [],
      api: 'getBattlesList',
      component: shallowRef(FairnessBattles),
      restrictIP: true,
    },
    // coinflip: {
    //   key: 'coinflip',
    //   label: computed(() => t('coinflip')),
    //   columns: coinflipColumns,
    //   paginator: {
    //     page_size: 10,
    //     total: 0,
    //     page: 1,
    //   },
    //   data: [],
    //   api: 'getCoinflipList',
    //   component: shallowRef(FairnessCoinflip),
    //   restrictIP: true,
    // },
    rollroom: {
      key: 'rollroom',
      label: computed(() => t('roll')),
      columns: [],
      paginator: {
        page_size: 10,
        total: 0,
        page: 1,
      },
      data: [],
      api: 'getRollList',
      component: shallowRef(FairnessRoll),
      restrictIP: false,
    },
  });
  const rollColumns = createRollColumns({
    menus,
  });
  menus.rollroom.columns = rollColumns;
  return { menus };
};
export { createMenuSchema };
