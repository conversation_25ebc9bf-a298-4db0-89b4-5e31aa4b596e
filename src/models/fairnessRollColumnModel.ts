import type { DataTableColumns } from 'naive-ui';
import { RollStatus } from '~/types/rollroom';
import rollroomDialog from '~/components/rollroom/dialog/index.vue';
const createRollColumns = (opt: {
  menus: any;
}): DataTableColumns<V1RollRoomInfo> => {
  const ellipsis = {
    tooltip: true,
  };
  const { menus } = opt;
  const { t } = useI18n();
  const dialog = useAppDialog();
  return [
    {
      title: 'ID',
      key: 'id',
      width: '10%',
    },
    {
      title: () => t('date'),
      key: 'draw_time',
      width: '18%',
      ellipsis,
    },
    {
      title: () => t('public_seed'),
      key: 'public_seed',
      width: '18%',
      ellipsis,
      render(row: V1RollRoomInfo) {
        return colorTextRender(
          row.cstatus !== RollStatus.FINISH ? '--' : row.public_seed,
          {
            color: '#1A9DFF',
            onClick: () => copy(row.public_seed),
          },
        );
      },
    },
    {
      title: () => t('public_seed_hash'),
      key: 'public_seed_hash',
      width: '18%',
      ellipsis: {
        tooltip: true,
      },
      render(row: V1RollRoomInfo) {
        return colorTextRender(row.public_seed_hash, {
          color: '#1A9DFF',
          onClick: () => copy(row.public_seed_hash),
        });
      },
    },
    {
      title: () => t('prnumber'),
      key: 'award_total',
      width: '8%',
      ellipsis,
    },
    {
      title: () => t('plnumber'),
      key: 'player_number',
      width: '8%',
      ellipsis,
      render(row: V1RollRoomInfo) {
        if (row.cstatus !== RollStatus.FINISH) {
          return '--';
        } else {
          return row.player_number || '--';
        }
      },
    },
    {
      title: () => t('your_location'),
      key: 'user_position',
      width: '8%',
      ellipsis,
    },
    {
      title: () => t('result'),
      key: 'prize_number',
      width: '12%',
      render(row: V1RollRoomInfo) {
        let result = '--';
        if (row.cstatus === RollStatus.FINISH) {
          result = row.prize_number || '--';
        }
        const index = menus.rollroom.data.findIndex(
          (el: any) => el.id === row.id,
        );
        const detail = computed(() => menus.rollroom.data[index]);
        const detailEl = colorTextRender(t('view_roll'), {
          color: '#F8B838',
          onClick: () => {
            dialog.open(rollroomDialog, {
              class:
                'max-w-[95%] w-[880px] bg-[#151A29] rounded-[8px] pb-[15px] sm:pb-[32px] n-dialog__close-position max-sm:px-[12px]',
              titleClass: '',
              contentProps: {
                detail,
                history: true,
                onUpdateRoom: (
                  data: ApiV1RollroomListGet200ResponseDataListInner,
                ) => {
                  menus.rollroom.data[index] = data;
                },
              },
            });
          },
        });
        return h(
          'div',
          {
            class: 'flex justify-between',
          },
          [result, detailEl],
        );
      },
    },
  ];
};

export { createRollColumns };
