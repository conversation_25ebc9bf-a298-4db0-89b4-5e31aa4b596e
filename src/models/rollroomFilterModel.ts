import { reactive } from 'vue';
import type { FilterFormSchema } from '~/types/filter';

const createFormSchema = () => {
  const { t } = useI18n();
  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      placeholder: computed(() => t('enter_room_id_or_name_to_search')),
      component: 'n-input-search',
      auth: true,
      value: '',
      listeners: {
        'complete:select': true,
      },
      class: 'w-full sm:w-[300px]',
    },
    sort_field: {
      key: 'sort_field',
      type: 'select',
      component: 'n-select',
      value: null,
      consistentMenuWidth: false,
      placeholder: computed(() => t('default_sorting')),
      options: computed(() => [
        {
          label: t('abreast_of_the_times'),
          value: 'time',
        },
        {
          label: t('the_most_valuable'),
          value: 'value',
        },
      ]),
    },
  });

  const formState = reactive(
    Object.keys(formSchema).reduce(
      (state, key) => {
        state[key] = formSchema[key].value;
        return {
          ...state,
          status: 'All',
        };
      },
      {} as Record<string, any>,
    ),
  );
  return {
    formSchema,
    formState,
  };
};

export { createFormSchema };
