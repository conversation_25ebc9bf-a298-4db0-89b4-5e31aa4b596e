import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';
import LiveMngUserAvatar from '@/components/liveMng/UserAvatar.vue';
import LiveMngStatus from '@/components/liveMng/Status.vue';
import { searchSchema, selectSchema } from '~/constants/liveMng';

const createFormSchema = () => {
  const sortOptions = computed(() => [
    {
      label: () => 'All States',
      value: null,
    },
    {
      label: () => 'Posted',
      value: '1',
    },
    {
      label: () => 'Frozen Earnings',
      value: '2',
    },
    {
      label: () => 'Invalid Earnings',
      value: '3',
    },
  ]);

  const formSchema: FilterFormSchema = reactive({
    rebate_status: {
      ...selectSchema,
      placeholder: 'All States',
      options: sortOptions,
    },
    search: {
      ...searchSchema,
      placeholder: 'Please enter the bill ID it serach',
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
  };
};

type StatusType = { icon: string; color: string; bg: string; label: string };
const rebateStatusMap: Record<number, StatusType> = {
  1: { label: 'Posted', icon: 'success-ic', color: '#00B42A', bg: '#E8FFEA' },
  2: {
    label: 'Frozen Earnings',
    icon: 'error-ic',
    color: '#F53F3F',
    bg: '#FFECE8',
  },
  3: {
    label: 'Invalid Earnings',
    icon: 'abnormal-ic',
    color: '#86909C',
    bg: '#F2F3F5',
  },
};
const renderMoney = (value: string | number) => `$ ${value}`;
const createGuildColumns = (): DataTableColumns => {
  return [
    {
      title: 'Bill ID',
      key: 'streamer_rebate_no',
    },
    {
      title: 'Rebate Amount',
      key: 'rebate_amount',
      render: (row: any) => renderMoney(row.rebate_amount),
    },
    {
      title: 'Rebate User',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.invitee_avatar,
          name: row.invitee_username,
        });
      },
    },
    {
      title: 'Bill Amount',
      key: 'target_amount',
      render: (row: any) => renderMoney(row.target_amount_usd),
    },
    {
      title: 'Time',
      key: 'add_time',
    },
    {
      title: 'Inviter',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.streamer_avatar,
          name: row.streamer_username,
        });
      },
    },
    {
      title: 'Inviter UID',
      key: 'streamer_uid',
    },
    {
      title: 'Status',
      key: 'rebate_status',
      render(row: any) {
        return h(LiveMngStatus, { ...rebateStatusMap[row.rebate_status] });
      },
    },
  ];
};

const createPersonalColumns = (): DataTableColumns => {
  return [
    {
      title: 'Bill ID',
      key: 'streamer_rebate_no',
    },
    {
      title: 'Rebate Amount',
      key: 'rebate_amount',
      render: (row: any) => renderMoney(row.rebate_amount),
    },
    {
      title: 'User',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.invitee_avatar,
          name: row.invitee_username,
        });
      },
    },
    {
      title: 'Bill Amount',
      key: 'target_amount',
      render: (row: any) => renderMoney(row.target_amount_usd),
    },
    {
      title: 'Time',
      key: 'add_time',
    },
    {
      title: 'Status',
      key: 'rebate_status',
      render(row: any) {
        return h(LiveMngStatus, { ...rebateStatusMap[row.rebate_status] });
      },
    },
  ];
};
export { createFormSchema, createGuildColumns, createPersonalColumns };
