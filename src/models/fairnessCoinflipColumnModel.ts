import type { DataTableColumns } from 'naive-ui';
import { useSettingStore } from '~/stores/modules/setting';
import { ChosenSide, Status } from '~/types/coinflip';
import SvgoGold from '~/assets/icons/gold.svg';
const createCoinflipColumns = (): DataTableColumns<V1CoinFlipRoomInfo> => {
  const ellipsis = {
    tooltip: true,
  };
  const { t } = useI18n();
  const settingStore = useSettingStore();
  const selfChosenSide = (row: V1CoinFlipRoomInfo) => {
    const uid = settingStore.userInfo?.uid;
    const creatorSide = row.creator_chosen_side;
    let chosenSide = 1;
    if (uid === row.creator_uid) {
      chosenSide = creatorSide as number;
    } else {
      chosenSide =
        creatorSide === ChosenSide.CT_BLACK
          ? ChosenSide.T_ORANGE
          : ChosenSide.CT_BLACK;
    }
    return chosenSide;
  };
  const priceRender = (text: string | number, color?: string) => {
    return h(
      'div',
      {
        class: 'flex items-center gap-x-2',
      },
      [
        h(SvgoGold, { class: 'text-theme-color mb-0', filled: true }),
        colorTextRender(text, { color }),
      ],
    );
  };
  const isFinished = (row: V1CoinFlipRoomInfo) => {
    let finished = false;
    if (row.status === Status.RESULT || row.status === Status.CANCEL) {
      finished = true;
    }
    return finished;
  };
  return [
    {
      title: () => t('game_id'),
      key: 'id',
      width: '8.5%',
    },
    {
      title: () => t('date'),
      key: 'user_join_time',
      width: '16%',
      ellipsis,
    },
    {
      title: () => t('bet'),
      key: 'bet_amount',
      width: '8%',
      render(row: V1CoinFlipRoomInfo) {
        return priceRender(row.bet_amount || 0);
      },
    },
    {
      title: () => t('private_seed'),
      key: 'private_seed',
      width: '14%',
      ellipsis,
      render(row: V1CoinFlipRoomInfo) {
        const finished = isFinished(row);
        return colorTextRender(finished ? row.private_seed : '--', {
          color: '#1A9DFF',
          onClick: () => copy(row.private_seed),
        });
      },
    },
    {
      title: () => t('private_seed_hash'),
      key: 'private_seed_hash',
      width: '14%',
      ellipsis,
      render(row: V1CoinFlipRoomInfo) {
        return colorTextRender(row.private_seed_hash, {
          color: '#1A9DFF',
          onClick: () => copy(row.private_seed_hash),
        });
      },
    },
    {
      title: () => t('eos_block_id'),
      key: 'seed_index',
      width: '9%',
      ellipsis,
      render(row: V1CoinFlipRoomInfo) {
        const finished = isFinished(row);
        return finished ? row.seed_index || '--' : '--';
      },
    },
    {
      title: () => t('eos_block_seed'),
      key: 'seed',
      width: '14%',
      ellipsis,
      render(row: V1CoinFlipRoomInfo) {
        const finished = isFinished(row);
        return colorTextRender(finished ? row.seed : '--', {
          color: '#1A9DFF',
          onClick: () => copy(row.seed),
        });
      },
    },
    {
      title: () => t('you_side'),
      key: 'creator_chosen_side',
      width: '7%',
      render(row: V1CoinFlipRoomInfo) {
        const chosenSide = selfChosenSide(row);
        return chosenSide === 1 ? 'CT' : 'T';
      },
    },
    {
      title: () => t('result'),
      key: 'result',
      width: '14%',
      render(row: V1CoinFlipRoomInfo) {
        let result = '';
        let amount = (row.bet_amount || 0).toString();
        let textColor;
        const chosenSide = selfChosenSide(row);
        if (row.status === Status.WAITJOIN) {
          result = '--';
        } else if (row.status === Status.CANCEL) {
          result = 'Cancel';
        } else {
          result = row.result === Status.JOIN ? 'CT' : 'T';
          if (chosenSide === row.result) {
            amount = `+${row.win_amount}`;
            textColor = '#3EFF95';
          } else {
            amount = `-${amount}`;
            textColor = '#FF5555';
          }
        }
        return h(
          'div',
          {
            class: 'flex items-center gap-x-2',
          },
          [
            h('span', { style: 'width: 45px' }, result),
            priceRender(amount, textColor),
          ],
        );
      },
    },
  ];
};

export { createCoinflipColumns };
