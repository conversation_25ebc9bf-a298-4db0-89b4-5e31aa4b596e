import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';
import RecordDescribe from '@/components/backpack/RecordDescribe.vue';
import type { RecordItemWithGoods } from '~/types/backpack';
import { getBackpackRecordTypeLabel } from '~/constants/backpack';
import type { BackpackRecordType } from '~/constants/backpack';

const DEFAULT_FILTER_PARAMS = {
  search: '',
} as const;

const createFormSchema = () => {
  const { t } = useI18n();

  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      class: 'flex-col sm:flex-row',
      placeholder: t('enter_skin_name_or_id_to_search'),
      component: 'n-input-search',
      auth: true,
      value: DEFAULT_FILTER_PARAMS.search,
      listeners: {
        'complete:select': true,
      },
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

const createObtainColumns = (): DataTableColumns<RecordItemWithGoods> => {
  const { t } = useI18n();
  const { $td } = useNuxtApp();
  return [
    {
      title: () => t('skin'),
      key: 'market_hash_name',
      ellipsis: {
        tooltip: true,
      },
      width: '25%',
      render(row: RecordItemWithGoods) {
        const key = row.market_hash_name || '';
        return $td(key);
      },
    },
    {
      title: 'ID',
      width: '15%',
      key: 'backpack_id',
    },
    {
      title: () => t('type'),
      width: '15%',
      key: 'record_type',
      render(row: RecordItemWithGoods) {
        return getBackpackRecordTypeLabel(row.record_type);
      },
    },
    {
      title: () => t('date'),
      key: 'record_time',
      width: '20%',
    },
    {
      title: () => t('describe'),
      key: 'record_detail',
      ellipsis: true,
      render(row) {
        return h(RecordDescribe, {
          type: (row.record_type ?? 1) as BackpackRecordType,
          recordDetail: row.record_detail ?? '',
        });
      },
    },
  ];
};

const createLostColumns = (): DataTableColumns<RecordItemWithGoods> => {
  const { t } = useI18n();
  const { $td } = useNuxtApp();
  return [
    {
      title: () => t('skin'),
      key: 'market_hash_name',
      ellipsis: {
        tooltip: true,
      },
      width: '25%',
      render(row: RecordItemWithGoods) {
        const key = row.market_hash_name || '';
        return $td(key);
      },
    },
    {
      title: () => 'ID',
      width: '15%',
      key: 'backpack_id',
    },
    {
      title: () => t('access'),
      width: '15%',
      key: 'record_type',
      render(row: RecordItemWithGoods) {
        return getBackpackRecordTypeLabel(row.record_type);
      },
    },
    {
      title: () => t('date'),
      key: 'record_time',
      width: '20%',
    },
    {
      title: () => t('describe'),
      key: 'record_detail',
      ellipsis: true,
      render(row) {
        return h(RecordDescribe, {
          type: (row.record_type ?? 1) as 1 | 2 | 3 | 4 | 5 | 6,
          recordDetail: row.record_detail ?? '',
        });
      },
    },
  ];
};

export { createFormSchema, createObtainColumns, createLostColumns };
