import { NTooltip, type DataTableColumns } from 'naive-ui';
import Currency from '@/components/Currency.vue';

const DEPOSIT_TYPE_MAP = {
  1: 'gift_card_deposit',
  2: 'bank_deposit',
  3: 'crypto_deposit',
} as const;

const createColumns =
  (): DataTableColumns<ApiV1TradeDepositRecordsGet200ResponseDataItemsInner> => {
    const { t } = useI18n();
    return [
      {
        title: 'ID',
        key: 'recharge_no',
        width: '25%',
      },
      {
        title: () => t('method'),
        key: 'recharge_type_text',
        width: '25%',
        render(row) {
          const payType = row.pay_type as keyof typeof DEPOSIT_TYPE_MAP;
          const descLang = DEPOSIT_TYPE_MAP[payType];
          const description = descLang
            ? t(descLang, row.recharge_type_text ?? '')
            : (row.recharge_type_text ?? '');
          return h(
            NTooltip,
            {
              trigger: 'hover',
              placement: 'top-start',
            },
            {
              default: () => h('span', {}, row?.payment_method_name),
              trigger: () =>
                h(
                  'div',
                  {
                    class: 'flex items-center gap-2 cursor-pointer',
                  },
                  [
                    h('img', {
                      src: row?.payment_method_logo,
                      class: 'size-[18px] object-contain',
                    }),
                    h('span', {}, description),
                  ],
                ),
            },
          );
        },
      },
      {
        title: () => t('coins'),
        key: 'amount',
        width: '25%',
        render(row) {
          return h(
            'div',
            {
              class: 'flex items-center',
            },
            h(Currency, { amount: row.amount }),
          );
        },
      },
      {
        title: () => t('date'),
        key: 'c_time',
        width: '25%',
      },
    ];
  };

export { createColumns };
