import type { DataTableColumns } from 'naive-ui';
import { FairBattleResult } from '~/types/battles';
import BattleDetailDialog from '~/components/fairness/battles/DetailDialog.vue';
import { useSettingStore } from '~/stores/modules/setting';
type CaseBattleRowType = V1BattleItem & {
  goods: Map<string, V1MarketGoodsInfo>;
  cases: Map<string, V1CasesItem>;
  users: Map<string, V1SimpleUserInfo>;
};
const createBattleColumns = (): DataTableColumns<CaseBattleRowType> => {
  const ellipsis = {
    tooltip: true,
  };
  const { t } = useI18n();
  const settingStore = useSettingStore();
  const $router = useRouter();
  const dialog = useAppDialog();
  return [
    {
      title: 'ID',
      key: 'arena_id',
      width: '15%',
      render(row: CaseBattleRowType) {
        return row.battle_info?.arena_id;
      },
    },
    {
      title: () => t('date'),
      key: 'started_at',
      width: '16%',
      ellipsis,
      render(row: CaseBattleRowType) {
        const uid = settingStore.userInfo?.uid;
        const player = row.all_players || [];
        const user = player.find((el) => el.uid === uid);
        if (user) {
          return user.created_at;
        } else {
          return '--';
        }
      },
    },
    {
      title: () => t('eos_block_id'),
      key: 'seed_index',
      width: '9%',
      ellipsis,
      render(row: CaseBattleRowType) {
        return row.battle_info?.seed_index || '--';
      },
    },
    {
      title: () => t('eos_block_seed'),
      key: 'seed',
      width: '15%',
      ellipsis,
      render(row: CaseBattleRowType) {
        return colorTextRender(row.battle_info?.seed, {
          color: '#1A9DFF',
          onClick: () => copy(row.battle_info?.seed),
        });
      },
    },
    {
      title: () => t('server_seed'),
      key: 'private_seed',
      width: '15%',
      ellipsis,
      render(row: CaseBattleRowType) {
        return colorTextRender(row.battle_info?.private_seed, {
          color: '#1A9DFF',
          onClick: () => copy(row.battle_info?.private_seed),
        });
      },
    },
    {
      title: () => t('server_seed_hash'),
      key: 'private_seed_hash',
      width: '15%',
      ellipsis,
      render(row: CaseBattleRowType) {
        return colorTextRender(row.battle_info?.private_seed_hash, {
          color: '#1A9DFF',
          onClick: () => copy(row.battle_info?.private_seed_hash),
        });
      },
    },
    {
      title: () => t('result'),
      key: 'result',
      width: '15%',
      render(row: CaseBattleRowType) {
        // status: 1-等待中;2-战斗中;3-已结束;4-已取消;
        const status = row.battle_info?.status;
        // result: 未知 0; 赢了1; 输了2; 已取消3; 已退出4
        const result = row.battle_info?.result;
        const isWin = result === FairBattleResult.WIN;
        const finished = status === 3;
        let resultTxt = '';
        switch (result) {
          case FairBattleResult.WIN:
            resultTxt = 'Win';
            break;
          case FairBattleResult.LOSE:
            resultTxt = 'Lose';
            break;
          case FairBattleResult.CANCEL:
            resultTxt = 'Cancel';
            break;
          case FairBattleResult.EXIT:
            resultTxt = 'Exit';
            break;
          default:
            resultTxt = '--';
            break;
        }
        const resultEl = colorTextRender(resultTxt, {
          color: isWin ? '#3EFF95' : '',
          style: { width: '45px' },
        });
        const detailEl = colorTextRender(finished ? t('details') : ' ', {
          color: '#F8B838',
          onClick: () => {
            // 已结束
            if (finished) {
              dialog.open(BattleDetailDialog, {
                class:
                  'max-w-[90%] bg-[#151A29] rounded-lg n-dialog__close-position max-sm:px-[10px]',
                titleClass: 'h-0',
                style: 'width: 880px;',
                contentProps: {
                  info: row,
                },
              });
            }
          },
        });
        const viewEl = colorTextRender(t('view_battle'), {
          color: '#F8B838',
          onClick: () => {
            $router.push(`/case-battles/game/${row.battle_info?.arena_id}`);
          },
        });
        return h(
          'div',
          {
            class: 'flex items-center justify-between',
          },
          [resultEl, detailEl, viewEl],
        );
      },
    },
  ];
};

export { createBattleColumns };
