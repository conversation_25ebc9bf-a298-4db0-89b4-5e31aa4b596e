import type { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import { reactive } from 'vue';
import type { SelectProps } from 'naive-ui';
import type { FilterFormSchema } from '~/types/filter';

type SelectThemeOverrides = NonNullable<SelectProps['themeOverrides']>;

const themeOverrides: SelectThemeOverrides = {
  peers: {
    InternalSelection: {
      border: 'none',
    },
  },
};

const amountKeyDescTheme: SelectThemeOverrides = {
  peers: {
    InternalSelection: {
      border: 'none',
      textColor: '#7d90ca',
    },
  },
};

const createFormSchema = () => {
  const { t } = useI18n();
  const formSchema: FilterFormSchema = reactive({
    price_range: {
      key: 'price_range',
      type: 'select',
      component: 'n-select',
      value: '0,0',
      themeOverrides,
      consistentMenuWidth: false,
      options: computed(() => [
        { label: t('all').toUpperCase(), value: '0,0' },
        { label: '0.00 - 5.00', value: '0.00,5.00' },
        { label: '5.00 - 25.00', value: '5.00,25.00' },
        { label: '25.00 - 50.00', value: '25.00,50.00' },
        { label: '50.00 - 100.00', value: '50.00,100.00' },
        { label: '100.00 - 500.00', value: '100.00,500.00' },
        { label: '500.00 - ∞', value: '500.00,0' },
      ]),
      field_label_place: 'left',
      label_style: 'color: #7D90CA; margin-right: -8px;',
      field_class:
        'border-1 border-[#323A51] rounded flex items-center h-[42px]',
      renderLabel: (option: SelectBaseOption) =>
        renderSelectOption({ option, showIcon: true }),
      renderTag: ({ option }: { option: SelectBaseOption }) =>
        renderSelectOption({
          option,
          label: computed(() => t('price_range') + ':').value,
          showIcon: true,
        }),
      clearable: false,
    },
    page_size: {
      key: 'page_size',
      type: 'select',
      component: 'n-select',
      value: 10,
      themeOverrides,
      consistentMenuWidth: false,
      options: [
        { label: '10', value: 10 },
        { label: '20', value: 20 },
        { label: '30', value: 30 },
        { label: '50', value: 50 },
        { label: '100', value: 100 },
      ],
      field_label_place: 'left',
      label_style: 'color: #7D90CA; margin-right: -8px;',
      field_class:
        'border-1 border-[#323A51] rounded flex items-center h-[42px]',
      renderLabel: (option: SelectBaseOption) => renderSelectOption({ option }),
      renderTag: ({ option }: { option: SelectBaseOption }) =>
        renderSelectOption({
          option,
          label: computed(() => t('show_games') + ':').value,
        }),
      clearable: false,
    },
    amount_key_desc: {
      key: 'amount_key_desc',
      type: 'select',
      component: 'n-select',
      value: 1,
      themeOverrides: amountKeyDescTheme,
      consistentMenuWidth: false,
      options: computed(() => [
        {
          label: t('highest_amount_first'),
          value: 1, // desc
        },
        {
          label: t('lowest_amount_first'),
          value: 2, // asc
        },
      ]),
      field_style: 'padding: 0 0px 0 0px;',
      field_class:
        'border-1 border-[#323A51] rounded flex items-center h-[42px]',
      clearable: false,
    },
  });

  const formState = reactive(
    Object.keys(formSchema).reduce(
      (state, key) => {
        state[key] = formSchema[key].value;
        return state;
      },
      {} as Record<string, any>,
    ),
  );

  return {
    formSchema,
    formState,
  };
};

export { createFormSchema };
