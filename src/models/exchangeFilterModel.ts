import { reactive } from 'vue';
import { createFormState } from './index';
import { useExchangeStore } from '~/stores/modules/exchange';
import type { FilterFormSchema } from '~/types/filter';

const DEFAULT_FILTER_PARAMS = {
  capsule_group: [],
  category: [],
  category_group: [],
  exterior: [],
  category_select: '',
  redeemable_only: false,
  order_type: '',
} as const;

const createFormSchema = () => {
  const exchangeStore = useExchangeStore();
  const { t, te } = useI18n();

  const exchangeFilterParamsArray = Object.keys(DEFAULT_FILTER_PARAMS);

  const orderTypeOptions = computed(() => [
    {
      label: t('highest_price_first'),
      value: 2,
      width: '250px',
    },
    {
      label: t('lowest_price_first'),
      value: 1,
    },
  ]);

  const formSchema: FilterFormSchema = reactive({
    exterior: {
      key: 'exterior',
      type: 'select',
      component: 'n-select',
      value: null,
      placeholder: computed(() => t('exterior')),
      consistentMenuWidth: false,
      options: computed(() => {
        const exterior = exchangeStore.exterior?.map(
          (item: any, index: number) => ({
            label: te(index === 0 ? item.title : item.value)
              ? t(index === 0 ? item.title : item.value)
              : item.title,
            value: item.value,
          }),
        );
        return exterior;
      }),
    },
    quality: {
      key: 'quality',
      type: 'select',
      label: '',
      component: 'n-select',
      placeholder: computed(() => t('category')),
      value: null,
      consistentMenuWidth: false,
      options: computed(() => {
        return exchangeStore.quality?.map((item: any) => ({
          label: te(item.title) ? t(item.title) : item.title,
          value: item.value,
        }));
      }),
    },
    redeemable_only: {
      key: 'redeemable_only',
      type: 'checkbox',
      component: 'n-checkbox',
      label: computed(() => t('convertible')),
      field_label: '',
      value: false,
    },
    order_type: {
      key: 'order_type',
      type: 'select',
      component: 'n-select',
      value: null,
      placeholder: computed(() => t('default_sorting')),
      consistentMenuWidth: false,
      options: orderTypeOptions,
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
    exchangeFilterParamsArray,
  };
};

export { createFormSchema };

export const CategoryTopTabIcons: any = {
  all: {
    icon: '&#xe608;',
  },
  knife: {
    icon: '&#xe607;',
  },
  gloves: {
    icon: '&#xe60b;',
  },
  rifle: {
    img: '../../imgs/rifle.png',
    activeImg: '../../imgs/rifle_active.png',
  },
  pistol: {
    img: '../../imgs/pistol.png',
    activeImg: '../../imgs/pistol_active.png',
  },
  smg: {
    icon: '&#xe606;',
  },
  heavy: {
    icon: '&#xe609;',
  },
  stickers: {
    icon: '&#xe605;',
  },
  agents: {
    icon: '&#xe60a;',
  },
  others: {
    icon: '&#xe60c;',
  },
};
