import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';
import LiveMngUserAvatar from '~/components/liveMng/UserAvatar.vue';
import LogContent from '~/components/liveMng/LogContent.vue';

import PermissionContent from '~/components/liveMng/dialog/Permission.vue';
import { useLiveMngStore } from '~/stores/modules/liveMng';
import LiveMngForm from '~/components/liveMng/Form.vue';
import {
  DISMISS_CONTENT,
  editFormConfig,
  searchSchema,
} from '~/constants/liveMng';
import { useSettingStore } from '~/stores/modules/setting';
interface EditParams {
  uid: string;
  name: string;
  avatar: string;
  remarks: string;
}
const createFormSchema = () => {
  const formSchema: FilterFormSchema = reactive({
    search: {
      ...searchSchema,
      placeholder: 'Please enter a nickname or UID to search',
    },
  });
  const formState = createFormState(formSchema);
  return {
    formSchema,
    formState,
  };
};

const renderMoney = (value: string | number) => `$ ${value}`;
const createFormerColumns = (): DataTableColumns => {
  return [
    {
      title: 'UID',
      key: 'streamer_uid',
    },
    {
      title: 'User',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.streamer_avatar,
          name: row.streamer_username,
        });
      },
    },
    {
      title: 'Posted rebates',
      key: 'rebate_amount',
      render: (row: any) => renderMoney(row.total_rebate_credited),
    },
    {
      title: 'Frozen rebates',
      key: 'target_amount',
      render: (row: any) => renderMoney(row.frozen_rebate),
    },
    {
      title: 'Join time',
      key: 'add_time',
    },
    {
      title: 'Remarks',
      key: 'remark',
    },
  ];
};

const createActiveColumns = (cb: () => void): DataTableColumns => {
  const { openLiveMngDialog, closeLiveMngDialog } = useLiveMngDialog();
  const liveMngStore = useLiveMngStore();
  const settingStore = useSettingStore();
  const edit = (params: EditParams) => {
    const { uid, name, avatar, remarks } = params;
    const formRef = ref();
    const userClass = 'flex text-[#1D2129] mb-[20px] text-[14px] font-medium';
    openLiveMngDialog({
      class: 'w-[620px] rounded-[8px]',
      title: 'Edit information',
      content: () =>
        h('div', null, [
          h('div', { class: userClass }, [
            h('span', { style: { width: '100px' } }, 'User'),
            h(LiveMngUserAvatar, {
              name,
              avatar,
            }),
          ]),
          h(LiveMngForm, {
            ref: formRef,
            defineValue: {
              remarks,
            },
            config: editFormConfig,
          }),
        ]),
      confirmText: 'Confirm Save',
      onConfirm: () => {
        const data = formRef.value.getFormData();
        liveMngStore
          .setStreamerRemark({
            remark: data.remarks,
            streamer_uid: uid,
          })
          .then(() => cb());
        closeLiveMngDialog();
      },
    });
  };

  const permission = (uid: string) => {
    const permissionRef = ref();
    openLiveMngDialog({
      class: 'w-[620px] rounded-[8px]',
      title: 'Permission Settings',
      content: () =>
        h(PermissionContent, {
          ref: permissionRef,
          uid,
        }),
      confirmText: 'Confirm Save',
      onConfirm: () => {
        const permission = permissionRef.value?.getCurrentPermission();
        liveMngStore.setStreamerPermission({
          streamer_uid: uid,
          guild_permission: permission,
        });
        closeLiveMngDialog();
      },
    });
  };
  const dismiss = (uid: string) => {
    openLiveMngDialog({
      class: 'w-[468px] rounded-[8px]',
      title: 'Confirm Removal from Guild',
      content: DISMISS_CONTENT,
      onConfirm: () => {
        liveMngStore.removeStreamerGuildLeave(uid).then(() => cb());
        closeLiveMngDialog();
      },
    });
  };
  return [
    ...createFormerColumns(),
    {
      title: 'Handle',
      key: 'handle',
      render(row: any) {
        const userInfo = settingStore.userInfo;
        const isMaster = row.streamer_uid === userInfo?.uid;
        return h('div', { class: 'flex items-center gap-3' }, [
          colorTextRender('Edit', {
            color: '#165dff',
            disabled: isMaster,
            onClick: () =>
              edit({
                uid: row.streamer_uid,
                name: row.streamer_username,
                avatar: row.streamer_avatar,
                remarks: row.remark,
              }),
          }),
          colorTextRender('Permission', {
            color: '#165dff',
            disabled: isMaster,
            onClick: () => permission(row.streamer_uid),
          }),
          colorTextRender('Dismiss', {
            color: '#165dff',
            disabled: isMaster,
            onClick: () => dismiss(row.streamer_uid),
          }),
        ]);
      },
    },
  ];
};

const createLogColumns = (): DataTableColumns => {
  return [
    {
      title: 'Time',
      key: 'add_time',
      width: 220,
    },
    {
      title: 'Log Content',
      key: 'record_type',
      render(row: any) {
        return h(LogContent, {
          type: row.record_type,
          streamer: `${row.streamer_username} (${row.streamer_uid})`,
          operation: `${row.operation_username} (${row.operation_uid})`,
        });
      },
    },
  ];
};

export {
  createFormSchema,
  createActiveColumns,
  createFormerColumns,
  createLogColumns,
};
