import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';
import LiveMngUserAvatar from '@/components/liveMng/UserAvatar.vue';
import { searchSchema } from '~/constants/liveMng';

const createPersonalFormSchema = () => {
  const formSchema: FilterFormSchema = reactive({
    search: {
      ...searchSchema,
      placeholder: 'Enter nickname to search',
    },
  });
  const formState = createFormState(formSchema);
  return {
    formSchema,
    formState,
  };
};
const createGuildFormSchema = () => {
  const formSchema: FilterFormSchema = reactive({
    search: {
      ...searchSchema,
      placeholder: 'Search by inviter (name/UID) and invitee (name)',
    },
  });
  const formState = createFormState(formSchema);
  return {
    formSchema,
    formState,
  };
};

const createPersonalColumns = (): DataTableColumns => {
  return [
    {
      title: 'User',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.invitee_avatar,
          name: row.invitee_username,
        });
      },
    },
    {
      title: 'Time',
      key: 'add_time',
    },
  ];
};
const createGuildColumns = (): DataTableColumns => {
  return [
    {
      title: 'Inviter',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.streamer_avatar,
          name: row.streamer_username,
        });
      },
    },
    {
      title: 'Inviter UID',
      key: 'streamer_uid',
    },
    {
      title: 'Invitee',
      key: 'uid_sender',
      render(row: any) {
        return h(LiveMngUserAvatar, {
          avatar: row.invitee_avatar,
          name: row.invitee_username,
        });
      },
    },
    {
      title: 'Time',
      key: 'add_time',
    },
  ];
};
export {
  createGuildFormSchema,
  createPersonalFormSchema,
  createPersonalColumns,
  createGuildColumns,
};
