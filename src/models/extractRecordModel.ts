import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import GoodsCard from '@/components/goods/Card.vue';
import ReasonInfo from '@/components/backpack/ReasonInfo.vue';
import type { FilterFormSchema } from '~/types/filter';
import type { ExtractItemWithGoods } from '~/types/backpack';

const DEFAULT_FILTER_PARAMS = {
  search: '',
} as const;

const createFormSchema = () => {
  const { t } = useI18n();

  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      class: 'flex-col sm:flex-row',
      placeholder: computed(() => t('enter_skin_name_or_order_id_to_search')),
      component: 'n-input-search',
      auth: true,
      value: DEFAULT_FILTER_PARAMS.search,
      listeners: {
        'complete:select': true,
      },
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

const createColumns = (): DataTableColumns<ExtractItemWithGoods> => {
  return [
    {
      title: '',
      key: 'no',
      width: '520px',
      render(row) {
        return h(GoodsCard, {
          width: '83px',
          marketNameClassName: '!w-[180px]',
          ellipsis: false,
          cover: row.goods_info?.icon_url ?? '',
          name: row.goods_info?.market_hash_name ?? '',
          tags: row.goods_info?.tags,
        });
      },
    },
    {
      title: '',
      key: 'order_id',
    },
    {
      title: '',
      key: 'extract_time',
    },
    {
      title: '',
      key: 'actions',
      render(row: ApiV1BackpackExtractListGet200ResponseDataItemsInner) {
        return h(ReasonInfo, {
          item: row,
        });
      },
    },
  ];
};

export { createFormSchema, createColumns };
