import type { DataTableColumns } from 'naive-ui';
const createColumns = (): DataTableColumns<V1LevelConfigItem> => {
  const { t } = useI18n();
  return [
    {
      title: () => t('tier'),
      key: 'level_name',
      width: '25%',
      render(row: V1LevelConfigItem) {
        return h(
          'div',
          {
            class: 'flex gap-1.5 items-center',
          },
          [
            h('img', {
              src: row?.level_url,
              class: 'w-[30px] ',
            }),
            h(
              'span',
              {
                style: { color: row?.level_color },
              },
              row?.level_name ?? '',
            ),
          ],
        );
      },
    },
    {
      title: () => t('level'),
      key: 'level_id',
      width: '25%',
    },
    {
      title: () => t('level_xp'),
      key: 'step_score',
      width: '25%',
    },
    {
      title: () => t('reward'),
      key: 'award_num',
      align: 'center',
      width: '25%',
      render(row: V1LevelConfigItem) {
        return h(
          'div',
          {
            class: 'flex items-center justify-center text-[12px]',
          },
          [
            h('img', {
              src: row.image_url,
              class: 'w-[24px] h-[24px] ',
            }),
            h('span', row?.award_num ? 'x' + row?.award_num : ''),
          ],
        );
      },
    },
  ];
};

export { createColumns };
