import type { DataTableColumns } from 'naive-ui';
type CasesListType = V1CasesRecord & V1CasesNameItem & V1GoodsNameItem;
const createCaseColumns = (): DataTableColumns<CasesListType> => {
  const ellipsis = {
    tooltip: true,
  };
  const { t } = useI18n();
  const { $td } = useNuxtApp();
  return [
    {
      title: 'ID',
      key: 'record_id',
      width: '13.5%',
    },
    {
      title: () => t('date'),
      key: 'created_at',
      width: '16%',
      ellipsis,
    },
    {
      title: () => t('case'),
      key: 'case_name',
      width: '10%',
      ellipsis,
    },
    {
      title: () => t('client_seed'),
      key: 'client_seed',
      width: '15%',
      ellipsis,
      render(row: CasesListType) {
        return colorTextRender(row.client_seed, {
          onClick: () => copy(row.client_seed),
        });
      },
    },
    {
      title: () => t('server_seed'),
      key: 'server_seed',
      width: '15%',
      ellipsis,
      render(row: CasesListType) {
        return colorTextRender(row.server_seed, {
          color: row.is_hash === 1 ? '#FF5555' : '#1A9DFF',
          onClick: () => copy(row.server_seed),
        });
      },
    },
    {
      title: () => t('nonce'),
      key: 'nonce',
      width: '5.5%',
    },
    {
      title: () => t('result'),
      key: 'result_value',
      width: '6%',
    },
    {
      title: () => t('win'),
      key: 'market_hash_name',
      ellipsis,
      render(row: CasesListType) {
        const key = row.market_hash_name || '';
        return $td(key);
      },
    },
  ];
};

export { createCaseColumns };
