import type { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import { reactive } from 'vue';
import type { SelectProps } from 'naive-ui';
import type { FilterFormSchema } from '~/types/filter';

type SelectThemeOverrides = NonNullable<SelectProps['themeOverrides']>;

const themeOverrides: SelectThemeOverrides = {
  peers: {
    InternalSelection: {
      border: 'none',
    },
  },
};

const createFormSchema = () => {
  const { t } = useI18n();
  const iconClass = 'text-primary-400 w-5 h-5 mr-1.5';
  const formSchema: FilterFormSchema = reactive({
    price_range: {
      key: 'price_range',
      type: 'select',
      component: 'n-select',
      value: '0,0',
      themeOverrides,
      consistentMenuWidth: false,
      options: computed(() => [
        { label: t('all').toUpperCase(), value: '0,0' },
        { label: '0.00 - 5.00', value: '0.00,5.00' },
        { label: '5.00 - 25.00', value: '5.00,25.00' },
        { label: '25.00 - 50.00', value: '25.00,50.00' },
        { label: '50.00 - 100.00', value: '50.00,100.00' },
        { label: '100.00 - 500.00', value: '100.00,500.00' },
        { label: '500.00 - ∞', value: '500.00,0' },
      ]),
      field_label_place: 'left',
      label_style: 'color: #7D90CA; margin-right: -8px; ',
      field_class:
        'border-1 border-[#323A51] rounded flex items-center h-[42px]',
      renderLabel: (option: SelectBaseOption) =>
        renderSelectOption({
          option,
          showIcon: true,
          iconClass,
        }),
      renderTag: ({ option }: { option: SelectBaseOption }) =>
        renderSelectOption({
          option,
          label: computed(() => t('price_range') + ':').value,
          showIcon: true,
          iconClass,
        }),
      clearable: false,
    },
    sort: {
      key: 'sort',
      type: 'select',
      component: 'n-select',
      value: 'popular_sorting',
      consistentMenuWidth: false,
      clearable: false,
      options: computed(() => [
        {
          label: t('popular_sorting'),
          value: 'popular_sorting',
        },
        {
          label: t('highest_price_first'),
          value: 'highest_price_first',
        },
        {
          label: t('lowest_price_first'),
          value: 'lowest_price_first',
        },
        {
          label: t('newest_first'),
          value: 'newest_first',
        },
      ]),
    },
  });

  const formState = reactive(
    Object.keys(formSchema).reduce(
      (state, key) => {
        state[key] = formSchema[key].value;
        return state;
      },
      {} as Record<string, any>,
    ),
  );
  const formSearchSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      placeholder: computed(() => t('search')),
      component: 'n-input-search',
      maxLength: '60',
      auth: true,
      value: '',
      listeners: {
        'complete:select': true,
      },
    },
  });

  const formSearchState = reactive(
    Object.keys(formSearchSchema).reduce(
      (state, key) => {
        state[key] = formSearchSchema[key].value;
        return state;
      },
      {} as Record<string, any>,
    ),
  );
  return {
    formSchema,
    formState,
    formSearchSchema,
    formSearchState,
  };
};

export { createFormSchema };
