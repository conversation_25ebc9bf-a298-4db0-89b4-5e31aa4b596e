import type { DataTableColumns } from 'naive-ui';
import Currency from '@/components/Currency.vue';

const createColumns =
  (): DataTableColumns<ApiV1TradeWithdrawRecordsGet200ResponseDataItemsInner> => {
    const { t } = useI18n();
    return [
      {
        title: 'ID',
        key: 'withdraw_no',
      },
      {
        title: () => t('method'),
        key: 'withdraw_type_text',
      },
      {
        title: () => t('coins'),
        key: 'amount',
        render(row) {
          return h(Currency, {
            amount: row.amount,
          });
        },
      },
      {
        title: () => t('date'),
        key: 'c_time',
      },
      {
        title: () => t('state'),
        key: 'withdraw_status',
        render(row) {
          const status = row.withdraw_status;
          let statusText = '';
          let statusColor = '';
          if (status === 2) {
            statusText = t('success');
            statusColor = 'green';
          } else if (status === 3) {
            statusText = t('failed');
            statusColor = 'red';
          } else if (status === 1) {
            statusText = t('perform');
            statusColor = 'orange';
          }
          return `
              <span class="text-${statusColor}">
                ${statusText}
            </span>
          `;
        },
      },
    ];
  };

export { createColumns };
