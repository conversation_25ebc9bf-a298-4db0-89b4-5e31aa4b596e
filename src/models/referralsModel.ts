import type { DataTableColumns } from 'naive-ui';
import Currency from '@/components/Currency.vue';
import User from '@/components/User.vue';

const createCommissionDetailsColumns =
  (): DataTableColumns<ApiV1UserCommissionDetailsGet200ResponseDataItemsInner> => {
    const { t } = useI18n();
    return [
      {
        title: () => t('name'),
        key: 'name',
        width: '33.33%',
        render(row) {
          return h(User, {
            avatar: row.avatar,
            nickname: row.name,
          });
        },
      },
      {
        title: () => t('commission'),
        key: 'commission',
        width: '33.33%',
        render(row) {
          return h(Currency, {
            amount: row.commission,
          });
        },
      },
      {
        title: () => t('time'),
        key: 'time',
      },
    ];
  };

const createReferredUsersColumns =
  (): DataTableColumns<ApiV1UserCommissionReferredGet200ResponseDataItemsInner> => {
    const { t } = useI18n();
    return [
      {
        title: () => t('name'),
        key: 'name',
        width: '33.33%',
        render(row) {
          return h(User, {
            avatar: row.avatar ?? '',
            nickname: row.name ?? '',
          });
        },
      },
      {
        title: () => t('total_commission'),
        key: 'commission',
        width: '33.33%',
        render(row) {
          return h(Currency, {
            amount: row.commission,
          });
        },
      },
      {
        title: () => t('affiliates_time'),
        key: 'time',
      },
    ];
  };

export { createCommissionDetailsColumns, createReferredUsersColumns };
