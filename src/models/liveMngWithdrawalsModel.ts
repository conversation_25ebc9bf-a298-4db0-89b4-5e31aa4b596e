import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';
import LiveMngModal from '~/components/liveMng/dialog/ViewVoucher.vue';
import LiveMngStatus from '@/components/liveMng/Status.vue';
import { searchSchema } from '~/constants/liveMng';

type StatusType = { icon: string; color: string; bg: string; label: string };
const rebateStatusMap: Record<number, StatusType> = {
  1: {
    label: 'Withdrawal in progress',
    icon: 'error-ic',
    color: '#F53F3F',
    bg: '#FFECE8',
  },
  2: {
    label: 'Completed',
    icon: 'success-ic',
    color: '#00B42A',
    bg: '#E8FFEA',
  },
};

const createFormSchema = () => {
  const formSchema: FilterFormSchema = reactive({
    search: {
      ...searchSchema,
      placeholder: 'Search by Withdrawal ID',
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
  };
};
const createColumns = (): DataTableColumns => {
  const dialog = useAppDialog();

  return [
    {
      title: 'Order ID',
      key: 'withdrawn_no',
    },
    {
      title: 'Withdrawal Amount',
      key: 'withdraw_amount',
      render(row: any) {
        return '$ ' + row.withdraw_amount;
      },
    },
    {
      title: 'Time',
      key: 'add_time',
    },
    {
      title: 'Status',
      key: 'withdraw_status',
      render(row: any) {
        return h(LiveMngStatus, { ...rebateStatusMap[row.withdraw_status] });
      },
    },

    {
      title: 'Handle',
      key: 'finish_certificate',
      render(row: any) {
        if (!row.finish_certificate || row.withdraw_status === 1) {
          return colorTextRender();
        }
        return colorTextRender('View Voucher', {
          color: '#165dff',
          onClick: () => {
            dialog.open(LiveMngModal, {
              class:
                'max-w-[95%] w-[880px] bg-white rounded-[4px] pb-[15px] custom-dialog',
              style: 'border:none',
              title: 'Remittance Proof',
              titleClass: 'flex justify-center',
              contentProps: {
                src: row.finish_certificate,
              },
            });
          },
        });
      },
    },
  ];
};
export { createFormSchema, createColumns };
