import { reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { createFormState } from './index';
import Currency from '@/components/Currency.vue';
import type { FilterFormSchema } from '~/types/filter';
import RecordDescribe from '@/components/deposit/RecordDescribe.vue';
import CopyableText from '@/components/CopyableText.vue';
import MethodDescribe from '@/components/profile/MethodDescribe.vue';
import {
  TRADE_TYPE_MAP,
  FLOW_TYPE_DESCRIPTION,
  type RecordTypeId,
  type TradeTypeMapKey,
  type FlowTypeDescriptionKey,
} from '~/constants/transaction';

const DEFAULT_FILTER_PARAMS = {
  search: undefined,
  flow_type: undefined,
} as const;

const getTradeTypeText = (type: TradeTypeMapKey): string => {
  return TRADE_TYPE_MAP[type] || 'unknown';
};

const createFormSchema = () => {
  const { depositApi } = useApi();
  const { t } = useI18n();

  const sortOptions = ref<Array<{ label: string; value: number }>>([]);
  const getSortOptions = async () => {
    const { data } = await depositApi.getTradeType();
    if (data.value?.code === 0) {
      sortOptions.value = data.value.data.items.map(
        (item: ApiV1TradeTradeTypeGet200ResponseDataItemsInner) => ({
          label: toCamelCase(t(getTradeTypeText(item.key as TradeTypeMapKey))),
          value: item.key,
        }),
      );
    } else {
      return [];
    }
  };

  const options = computed(() => {
    return [
      {
        label: t('all_method'),
        value: undefined,
      },
      ...sortOptions.value,
    ];
  });

  getSortOptions();

  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      placeholder: computed(() => t('search')),
      component: 'n-input-search',
      auth: true,
      value: DEFAULT_FILTER_PARAMS.search,
      listeners: {
        'complete:select': true,
      },
    },
    flow_type: {
      key: 'flow_type',
      type: 'select',
      component: 'n-select',
      value: DEFAULT_FILTER_PARAMS.flow_type,
      placeholder: computed(() => t('all_method')),
      consistentMenuWidth: false,
      options,
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

const createColumns =
  (): DataTableColumns<ApiV1TradeTransactionsGet200ResponseDataListInner> => {
    const { t } = useI18n();
    return [
      {
        title: 'ID',
        key: 'flow_no',
        width: 220,
      },
      {
        title: () => t('method'),
        key: 's_flow_type_text',
        width: 190,
        render(row) {
          return h(
            CopyableText,
            {
              disabled: true,
              tooltip: row?.name !== '',
              tooltipDefault: (() => {
                const flowType = row?.s_flow_type as FlowTypeDescriptionKey;
                const description = FLOW_TYPE_DESCRIPTION[flowType];
                return description
                  ? t(description, row?.name)
                  : (row?.name ?? '');
              })(),
              ellipsis: true,
              align: 'left',
              placement: 'top-start',
              size: 'large',
            },
            {
              default: () =>
                h(MethodDescribe, {
                  item: row,
                }),
            },
          );
        },
      },
      {
        title: () => t('coins'),
        key: 'amount',
        width: '125',
        render(row) {
          const type = row.add;
          const textColor =
            Number(type) === 1 ? 'text-green-500' : 'text-red-500';
          return h(Currency, {
            amount: Number(type) === 1 ? '+' + row.amount : '-' + row.amount,
            textColor,
          });
        },
      },
      {
        title: () => t('balance'),
        key: 'balance',
        width: '135',
        render(row) {
          return h(Currency, {
            amount: row.balance,
          });
        },
      },
      {
        title: () => t('date'),
        key: 'c_time',
        width: '220',
      },
      {
        title: () => t('describe'),
        key: 'record_detail',
        render(row) {
          const flowType = row.s_flow_type as RecordTypeId;
          return h(RecordDescribe, {
            className: 'max-w-[130px]',
            type: flowType,
            recordDetail: flowType === 13 ? '' : row.target_id,
          });
        },
      },
    ];
  };

export { createColumns, createFormSchema };
