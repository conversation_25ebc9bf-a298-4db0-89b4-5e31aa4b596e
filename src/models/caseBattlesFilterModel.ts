import { reactive, computed } from 'vue';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';

const DEFAULT_FILTER_PARAMS = {
  is_can_join: false,
  order_key: 'order_by=battle_cost,order=2',
} as const;

const createFormSchema = () => {
  const { t } = useI18n();

  const sortOptions = computed(() => [
    {
      label: t('highest_price_first'),
      value: 'order_by=battle_cost,order=2',
    },
    {
      label: t('lowest_price_first'),
      value: 'order_by=battle_cost,order=1',
    },
    {
      label: t('newest_first'),
      value: 'order_by=created_at,order=2',
    },
    {
      label: t('oldest_first'),
      value: 'order_by=created_at,order=1',
    },
  ]);

  const formSchema: FilterFormSchema = reactive({
    is_can_join: {
      key: 'is_can_join',
      type: 'checkbox',
      component: 'n-checkbox',
      label: computed(() => t('joinable')),
      field_label: '',
      value: DEFAULT_FILTER_PARAMS.is_can_join,
    },
    order_key: {
      type: 'select',
      field_label: '',
      key: 'order_key',
      component: 'n-select',
      placeholder: computed(() => t('default_sorting')),
      value: DEFAULT_FILTER_PARAMS.order_key,
      consistentMenuWidth: false,
      options: sortOptions,
      clearable: false,
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

export { createFormSchema };
