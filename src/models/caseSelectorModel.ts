import { reactive } from 'vue';
import { createFormState } from './index';
import type { FilterFormSchema } from '~/types/filter';

const DEFAULT_FILTER_PARAMS = {
  search: '',
  highest_price_first: 'highest_price_first',
  price_range: '',
  sort_by: 'highest_price_first',
} as const;

const createFormSchema = () => {
  const { t } = useI18n();

  const sortOptions = computed(() => [
    {
      label: t('highest_price_first'),
      value: 'highest_price_first',
    },
    {
      label: t('lowest_price_first'),
      value: 'lowest_price_first',
    },
    {
      label: t('newest_first'),
      value: 'Newest first',
    },
  ]);

  const formSchema: FilterFormSchema = reactive({
    search: {
      key: 'search',
      type: 'string',
      placeholder: computed(() => t('search')),
      component: 'n-input-search',
      auth: true,
      value: DEFAULT_FILTER_PARAMS.search,
      listeners: {
        'complete:select': true,
      },
    },
    highest_price_first: {
      type: 'n-select',
      field_label: '',
      key: 'highest_price_first',
      component: 'n-select',
      value: DEFAULT_FILTER_PARAMS.highest_price_first,
      consistentMenuWidth: false,
      options: sortOptions,
    },
    price_range: {
      type: 'n-select',
      field_label: '',
      key: 'price_range',
      component: 'n-select',
      value: DEFAULT_FILTER_PARAMS.price_range,
      consistentMenuWidth: false,
      options: sortOptions,
    },
    sort_by: {
      type: 'n-select',
      field_label: '',
      key: 'sort_by',
      component: 'n-select',
      value: DEFAULT_FILTER_PARAMS.sort_by,
      consistentMenuWidth: false,
      options: sortOptions,
    },
  });

  const formState = createFormState(formSchema);

  return {
    formSchema,
    formState,
    filterParams: DEFAULT_FILTER_PARAMS,
  };
};

export { createFormSchema };
