import { type Plugin } from 'vue';
export default {
  install(app: any, _options: any) {
    app.directive('lazyload', {
      mounted(el: any) {
        function loadImage() {
          // 对于 img 标签和背景图片的不同处理
          if (
            el.tagName.toLowerCase() === 'img' ||
            el.tagName.toLowerCase() === 'video'
          ) {
            const imageSrc = el.dataset.src;
            if (imageSrc) {
              el.src = imageSrc;
              delete el.dataset.src;
              el.onload = () => {
                el.classList.add('loaded');
                setTimeout(() => el.classList.add('loaded'), 100);
              };
            }
          } else {
            // 对于背景图片
            const backgroundImageSrc = el.dataset.backgroundImage;
            if (backgroundImageSrc) {
              el.style.backgroundImage = `url(${backgroundImageSrc})`;
            }
          }
        }

        function handleIntersect(
          entries: IntersectionObserverEntry[],
          observer: IntersectionObserver,
        ) {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              loadImage();
              observer.unobserve(el);
            }
          });
        }

        function createObserver() {
          const options: IntersectionObserverInit = {
            root: null,
            threshold: 0,
          };
          const observer = new IntersectionObserver(handleIntersect, options);
          observer.observe(el);
        }
        if (typeof window !== 'undefined') {
          if (window.IntersectionObserver) {
            createObserver();
          } else {
            loadImage();
          }
        }
      },

      getSSRProps(_binding: any, _vnode: any) {
        return {};
      },
    });
  },
} as Plugin;
