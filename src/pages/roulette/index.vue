<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="py-5">
    <div class="w-full flex justify-end mb-8">
      <SoundSwitch />
    </div>

    <n-button @click="handlePlay">开始</n-button>
    <!-- 动画 -->
    <div>
      <div ref="roulette" class="roulette">
        <!-- 倒计时 -->
        <div
          v-show="!hidePollingMask"
          class="bg-[#1a1e23]/70 absolute left-0 top-0 right-0 bottom-0 flex flex-col justify-center items-center text-white font-bold text-4xl"
        >
          <div class="text-base font-light text-gray-300 mb-2">Rolling</div>
          <n-countdown
            ref="countdown"
            :render="renderCountDown"
            :duration="1 * 1000"
            :active="countDownActive"
            :precision="2"
            @finish="rollingFinish"
          />
        </div>
        <!-- 中心线 -->
        <div
          v-show="hidePollingMask"
          class="h-[120px] absolute -top-[10px] rounded-full left-1/2 w-[4px] -translate-x-[2px] z-50 bg-red-600"
        ></div>
      </div>
    </div>

    <!-- 上一圈 -->
    <RoulettePrevLap
      class="hidden lg:flex"
      :preRes="preRes"
      :lastRes="lastRes"
    />
    <!-- 投注金额输入 -->
    <BetAmount v-model:betAmount="betAmount" class="my-4 lg:mx-auto" />

    <n-button @click="addBetUser">添加下注人</n-button>
    <n-button @click="resetStatus">reset</n-button>
    <div class="grid grid-cols-3 gap-x-12 relative">
      <!-- 下注 -->
      <div
        v-for="(item, key) in bets"
        :key="key"
        :class="{ 'opacity-50': resultShow !== key && hidePollingMask }"
      >
        <div
          class="flex items-center bg-[#333540] rounded-lg justify-between px-4 py-3 cursor-pointer max-lg:text-xs max-lg:flex-col"
          @click="betGame"
        >
          <div class="flex items-center flex-col lg:flex-row">
            <img
              class="w-[40px] h-[40px] lg:mr-2 max-lg:mb-2"
              :src="icons[key]"
              alt=""
            />
            <span>下注</span>
          </div>
          <div class="max-lg:mt-6">
            赢得
            <b>{{ item.multiple }}x</b>
          </div>
        </div>
      </div>

      <div
        v-for="(item, key) in bets"
        :key="key"
        :class="{ 'opacity-50': resultShow !== key && hidePollingMask }"
        class="col-span-3 lg:col-auto"
      >
        <!-- 统计 -->
        <div class="bg-[#16171c] rounded-lg mt-2 border-1 border-black">
          <div
            class="px-4 py-2 flex justify-between text-sm"
            @click="item.listHide = !item.listHide"
          >
            <div class="flex items-center">
              <img
                class="w-[24px] h-[24px] mr-2 block lg:hidden"
                :src="icons[key]"
                alt=""
              />
              <span>{{ item.list.length }} 投注总额</span>
            </div>
            <div class="flex item-center">
              <BaseIcon name="amount" class="text-theme-color"></BaseIcon>
              <div
                :class="{
                  'text-green-600': resultShow === key,
                  'text-red-500': resultShow && resultShow !== key,
                }"
              >
                <span v-show="resultShow">
                  {{ resultShow === key ? '+' : '-' }}
                </span>
                <n-number-animation
                  :from="item.preTotalAmount"
                  :to="item.totalAmount"
                  :precision="2"
                  :duration="500"
                />
              </div>
            </div>
          </div>
          <!-- 列表 -->
          <div
            v-show="!item.listHide && item.list.length"
            class="px-4 py-2 relative max-h-[400px] overflow-hidden transition: all 300ms border-t-1 border-black"
            :class="{ 'bet-users': item.list.length >= 6 }"
          >
            <transition-group name="bet-user">
              <div
                v-for="list in item.list"
                :key="list.uid"
                class="flex py-2 items-center justify-between"
              >
                <div class="flex items-center">
                  <n-avatar
                    :src="list.avatar"
                    round
                    :size="20"
                    class="mr-2 cursor-pointer shrink-0"
                  />
                  <div class="text-xs grow">
                    <span
                      class="inline-flex items-center justify-center w-[40px] rounded mr-2"
                      :style="{
                        color: list.levelColor,
                        background: hexToRgba(list.levelColor),
                      }"
                    >
                      <BaseIcon name="level1"></BaseIcon>
                      {{ list?.level }}
                    </span>
                    <span class="text-white mr-2">{{ list.username }}</span>
                  </div>
                </div>
                <div
                  :class="{
                    'text-green-500': resultShow === key,
                    'text-red-500': resultShow && resultShow !== key,
                  }"
                  class="text-sm"
                >
                  <span v-show="resultShow">
                    {{ resultShow === key ? '+' : '-' }}
                  </span>
                  {{ list.amount }}
                </div>
              </div>
            </transition-group>
          </div>
        </div>
      </div>
    </div>

    <RoulettePrevLap
      class="w-[276px] mx-auto flex lg:hidden"
      :preRes="preRes"
      :lastRes="lastRes"
    />
  </div>
</template>
<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { BigNumberCalc } from '~/utils/index';
const { add } = BigNumberCalc;
const toast = useAppToast();
// 元素ref
const roulette = ref();
const countdown = ref();
// 隐藏轮盘倒计时
const hidePollingMask = ref(false);
// 倒计时开始
const countDownActive = ref(false);
// 输入框下注金额
const betAmount = ref();
// 展示结果,下注输赢方
const resultShow = ref();
type IconType = {
  [key: number]: string; // 添加数字索引签名
};
const icons: IconType = {
  1: '/imgs/coin_gray.webp',
  2: '/imgs/coin_orange.webp',
  3: '/imgs/roulette_center.webp',
};
// 上一圈结果
const preRes = ref([
  {
    result: 2,
    id: 'x',
  },
  {
    result: 3,
    id: 'x2',
  },
  {
    result: 1,
    id: 'x3',
  },
  {
    result: 1,
    id: 'x4',
  },
  {
    result: 2,
    id: 'x5',
  },
  {
    result: 1,
    id: 'x6',
  },
  {
    result: 2,
    id: 'x7',
  },
  {
    result: 2,
    id: 'x8',
  },
  {
    result: 2,
    id: 'x9',
  },
  {
    result: 1,
    id: 'x10',
  },
]);
// 前100次结果
const lastRes = {
  1: 46,
  2: 12,
  3: 42,
};
type Item = {
  totalCount: number;
  preTotalAmount: number;
  totalAmount: number;
  multiple: number;
  listHide: boolean;
  list: {
    username: string | number;
    uid: string | number;
    avatar: string;
    level: number;
    levelIcon: string;
    levelColor: string;
    amount: number;
  }[];
};

type Data = {
  [key: number]: Item; // 添加数字索引签名
};
const bets = reactive<Data>({
  1: {
    totalCount: 2,
    preTotalAmount: 0,
    totalAmount: 1.12,
    multiple: 2,
    listHide: false,
    list: [
      {
        username: 'nickname xxx',
        uid: '1',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 2,
        levelIcon: '&#xe60c;',
        levelColor: 'red',
        amount: 0.01,
      },
      {
        username: '1nickname xxx',
        uid: '2',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 66,
        levelIcon: '&#xe609;',
        levelColor: '#edc254',
        amount: 0.01,
      },
      {
        username: '2nickname xxx',
        uid: '3',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 34,
        levelIcon: '&#xe60a;',
        levelColor: '#00afdc',
        amount: 0.01,
      },
    ],
  },
  2: {
    totalCount: 2,
    preTotalAmount: 0,
    totalAmount: 1.12,
    multiple: 2,
    listHide: false,
    list: [
      {
        username: 'nickname xxx',
        uid: '1',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 2,
        levelIcon: '&#xe60c;',
        levelColor: 'red',
        amount: 0.01,
      },
      {
        username: 'nickname xxx',
        uid: '11',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 2,
        levelIcon: '&#xe60c;',
        levelColor: 'red',
        amount: 0.01,
      },
      {
        username: 'nickname xxx',
        uid: '12',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 2,
        levelIcon: '&#xe60c;',
        levelColor: 'red',
        amount: 0.01,
      },
      {
        username: '1nickname xxx',
        uid: '2',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 66,
        levelIcon: '&#xe609;',
        levelColor: '#edc254',
        amount: 0.01,
      },
      {
        username: '1nickname xxx',
        uid: '22',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 66,
        levelIcon: '&#xe609;',
        levelColor: '#edc254',
        amount: 0.01,
      },
      {
        username: '2nickname xxx',
        uid: '3',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 34,
        levelIcon: '&#xe60a;',
        levelColor: '#00afdc',
        amount: 0.01,
      },
    ],
  },
  3: {
    totalCount: 2,
    preTotalAmount: 0,
    totalAmount: 1.12,
    multiple: 14,
    listHide: false,
    list: [
      {
        username: 'nickname xxx',
        uid: '1',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 2,
        levelIcon: '&#xe60c;',
        levelColor: 'red',
        amount: 0.01,
      },
      {
        username: '1nickname xxx',
        uid: '2',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 66,
        levelIcon: '&#xe609;',
        levelColor: '#edc254',
        amount: 0.01,
      },
      {
        username: '2nickname xxx',
        uid: '3',
        avatar:
          'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
        level: 34,
        levelIcon: '&#xe60a;',
        levelColor: '#00afdc',
        amount: 0.01,
      },
    ],
  },
});
// test 动画
const handlePlay = () => {
  countDownActive.value = true;
  // 1100 550
  // const rouletteW = roulette.value.offsetWidth
  // console.log(rouletteW, rouletteW/2)
  // roulette.value.style.backgroundPositionX = `${rouletteW/2 + 50}px`;
};
// test 添加下注人
const addBetUser = () => {
  const randomNum = Math.floor(Math.random() * 3) + 1; // 1-3
  // preRes.value.shift();
  // preRes.value.push({
  //   result: randomNum,
  //   id: (new Date()).getTime().toString()
  // })
  // return;
  const randomAmount = Number((Math.random() * 10).toFixed(2)); // 0-10
  const uid = new Date().getTime();
  // const arr = ['灰色', '黄色', '骰子'];
  // console.log('当前添加的是: ', arr[randomNum - 1], randomNum);
  // console.log('金额是: ', randomAmount);
  // console.log('uid: ', uid);
  const list = cloneDeep(bets[randomNum].list);
  list.push({
    username: uid,
    uid,
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
    level: randomNum,
    levelIcon: '&#xe60c;',
    levelColor: 'red',
    amount: randomAmount,
  });
  list.sort(
    (a: { amount: number }, b: { amount: number }) => b.amount - a.amount,
  );
  bets[randomNum].list = list;
  bets[randomNum].preTotalAmount = Number(bets[randomNum].totalAmount);
  bets[randomNum].totalAmount = Number(
    list.reduce(
      (pre: string | number, cur: { amount: string | number }) =>
        add(pre, cur.amount),
      0,
    ),
  );
};
const betGame = () => {
  if (betAmount.value > 0) {
    // 下注成功
    // 禁止再次下注??
  } else {
    toast.error({ content: '输入下注金额' });
  }
};

// 倒计时结束
const rollingFinish = () => {
  // 获取滚动结果 异步/socket
  const randomNum = Math.floor(Math.random() * 3) + 1; // 1-3
  // 背景图中有7黄7灰1骰子,随机黄灰7个位置中的一个
  const randomPosition = Math.floor(Math.random() * 7); // 0-6
  // 离中心点的偏移距离
  const stopPosition = Math.floor(Math.random() * 91) + 5; // 10-90

  // 这个值应该是动态的,根据屏幕宽度尺寸计算
  const rouletteW = roulette.value.offsetWidth;
  const basePosition = rouletteW / 2; // 第一个骰子起始位置的position

  // const arr = ['灰色', '黄色', '骰子'];
  // console.log('当前选中的是: ', arr[randomNum - 1], randomNum);
  // console.log('stopPosition: ', stopPosition);
  // console.log('randomPosition: ', randomPosition);
  let pollingPosition = basePosition + stopPosition;
  if (randomNum !== 3) {
    // 骰子
    pollingPosition += 200 * randomPosition + (randomNum === 1 ? 200 : 100);
  }
  // 开始滚动2s,修改bg-image位置
  roulette.value.style.backgroundPositionX = `${6000 + basePosition + 50}px`;
  hidePollingMask.value = true;
  setTimeout(() => {
    roulette.value.style.transition = 'background 4s';
    roulette.value.style.backgroundPositionX = `${pollingPosition}px`;
  }, 50);
  // 监听动画结束
  roulette.value.addEventListener(
    'transitionend',
    () => {
      roulette.value.style.transition = 'none'; // 移除动画效果
      resultShow.value = randomNum;
      // 更新上一圈动画
      preRes.value.shift();
      preRes.value.push({
        result: randomNum,
        id: new Date().getTime().toString(),
      });
      setTimeout(() => {
        // 重置状态
        resetStatus();
      }, 4000);
    },
    { once: true },
  );
};
// 重置状态
const resetStatus = () => {
  const rouletteW = roulette.value.offsetWidth;
  roulette.value.style.backgroundPositionX = `${rouletteW / 2 + 50}px`;
  countDownActive.value = false;
  countdown.value.reset();
  hidePollingMask.value = false;
  resultShow.value = null;
  Object.values(bets).forEach((el: any) => {
    el.list = [];
    el.preTotalAmount = 0;
    el.totalAmount = 0;
  });
};
// 倒计时render
const renderCountDown = ({
  seconds,
  milliseconds,
}: {
  seconds: number;
  milliseconds: number;
}) => {
  const milli = parseInt(String(milliseconds / 10));
  return `${seconds}.${String(milli).padStart(2, '0')}`;
};

// 将 HEX 颜色转换为 RGBA 格式的函数
const hexToRgba = (hex: string) => {
  if (!hex) return;
  const bigint = parseInt(hex.replace('#', ''), 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  return `rgba(${r}, ${g}, ${b}, 0.2)`;
};

onMounted(() => {
  const rouletteW = roulette.value.offsetWidth;
  roulette.value.style.backgroundPositionX = `${rouletteW / 2 + 50}px`; // 移除动画效果
});
</script>
<style lang="scss">
.roulette {
  @apply h-[100px] w-full max-w-[1100px] mx-auto relative;
  background-image: url('/imgs/roulette.png');
  background-repeat: repeat-x;
  background-size: auto 100%;
  // background-position: 600px 0;
  transition: none;
  &::before {
    @apply h-[100px] w-[300px] absolute top-0 left-0 bg-gradient-to-r from-[#1a1e23] to-[#1a1e23]/0;
    content: '';
  }
  &::after {
    @apply h-[100px] w-[300px] absolute top-0 right-0 bg-gradient-to-l from-[#1a1e23] to-[#1a1e23]/0;
    content: '';
  }
}

// 下注列表动画
.bet-users::after {
  content: '';
  @apply h-[100px] w-full absolute bottom-0 left-0 bg-gradient-to-t from-[#1a1e23] to-[#1a1e23]/0;
}

.bet-user-move, /* 对移动中的元素应用的过渡 */
.bet-user-enter-active {
  transition: all 0.5s ease;
}
.bet-user-enter-from {
  opacity: 0;
  transform: translate3d(0, -34px, 0);
}
</style>
