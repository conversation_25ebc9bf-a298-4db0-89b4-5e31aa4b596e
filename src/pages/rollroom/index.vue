<template>
  <n-spin :show="loading && !pageLoading" class="grow">
    <RollroomFilter @update-filter="updateFilter" />
    <div v-if="pageLoading || roomList.size" class="rollroom-list">
      <template v-if="pageLoading">
        <SkeletonRollroom v-for="index in 8" :key="index" />
      </template>
      <template v-else>
        <RollroomCard
          v-for="[id, item] in roomList"
          :key="id"
          :info="item"
          @update-room="updateRoom"
        />
      </template>
    </div>
    <Empty
      v-else
      :msg="$t('no_record_found')"
      class="mt-5 h-[100px]"
      column
    ></Empty>
    <ListPaginator
      :total="totalList"
      :page-size="20"
      :page="page"
      @update:page="handlePage"
    ></ListPaginator>
  </n-spin>
</template>
<script lang="ts" setup>
import { FilterRollStatus, RollStatus } from '~/types/rollroom';
const { rollroomApi } = useApi();
const page = ref(1);
const totalList = ref(0);
const roomList = ref<Map<number, ApiV1RollroomListGet200ResponseDataListInner>>(
  new Map(),
);
const filter = ref({
  search: '',
  status: 'All',
  sort_field: null,
});
const pageLoading = ref(true);
const loading = ref(false);

// 列表接口参数
const listParams = computed(() => {
  const { status, sort_field: sortField, search } = filter.value;
  const params: ApiV1RollroomListGetRequest = {
    page: page.value,
    page_size: 20,
    status: FilterRollStatus[status as keyof typeof FilterRollStatus],
    sort_field: sortField,
    sort_order_desc: true,
  };
  if (search) {
    if (/^\d+$/.test(search)) {
      params.room_id = Number(search);
    } else {
      params.room_name = search;
    }
  }
  return params;
});
// 获取列表
const getList = async (server: boolean = false) => {
  loading.value = true;
  try {
    const { data: req } = await rollroomApi.getRollroomList(
      listParams.value,
      server,
    );
    const data = req.value?.data;
    if (data) {
      roomList.value = new Map(data.list.map((item) => [item.id, item]));
      totalList.value = data.total;
    }
  } finally {
    loading.value = false;
    pageLoading.value = false;
  }
};
// 筛选条件
const updateFilter = (info: any) => {
  filter.value = info;
  page.value = 1;
  getList();
};
// 页码
const handlePage = (pageInfo: { page: number }) => {
  page.value = pageInfo.page;
  getList();
};
// 更新房间信息
const updateRoom = (info: ApiV1RollroomListGet200ResponseDataListInner) => {
  const tabCurrent = filter.value.status === 'Current';
  // roll房结束 - tab为current删除结束卡片
  if (tabCurrent && info.cstatus === RollStatus.FINISH) {
    roomList.value.delete(info.id);
  } else {
    roomList.value.set(info.id, {
      ...roomList.value.get(info.id),
      ...info,
    });
  }
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.rollroom-list {
  @apply grid gap-x-[16px];
  grid-template-columns: repeat(auto-fill, minmax(332px, 1fr));
}
</style>
