<template>
  <div class="min-h-screen live-login-bg">
    <canvas
      ref="canvas"
      class="cavs w-full h-full"
      @mousemove="mousemove"
    ></canvas>
    <div class="live-login-content">
      <div
        class="p-[32px] flex flex-col justify-center items-center gap-[20px]"
      >
        <div class="flex items-center justify-center border-gray-700">
          <img src="/imgs/live-logo.png" alt="LKSK" class="w-[172px]" />
        </div>
        <LiveLoginForm
          class="w-full"
          :show-terms-and-privacy="false"
          @success="handleAuthSuccess"
          @switch-to-register="true"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import LiveLoginForm from '@/components/liveMng/LiveLoginForm.vue';
import { useSettingStore } from '~/stores/modules/setting';
const settingStore = useSettingStore();
await settingStore.getUserInfo(true);
const $router = useRouter();

const canvas = ref<HTMLCanvasElement | null>(null);
const { mousemove } = useLiveParticles(canvas);
if (import.meta.client && settingStore.userInfo) {
  location.replace('/live-mng');
}
const handleAuthSuccess = () => {
  $router.replace('/live-mng');
};
</script>
<style lang="scss" scoped>
.live-login-bg {
  background: linear-gradient(119deg, #dbe9ff 0%, #ffffff 50%, #dceeff 100%);
}
.live-login-content {
  @apply w-[500px] flex flex-col bg-white absolute  left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-xl overflow-hidden;
  box-shadow: 0 5px 15px 0 rgba(184, 190, 193, 0.3);
}
</style>
