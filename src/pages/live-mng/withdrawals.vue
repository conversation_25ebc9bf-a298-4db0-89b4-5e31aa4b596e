<template>
  <div class="min-h-full flex flex-col">
    <LiveMngWithdrawalsHeader />
    <div class="bg-white mt-[20px] flex-1">
      <LiveMngFetchTable
        title="Withdrawal Order List"
        :fetch-fn="fetchFn"
        :columns="columns"
        :form-schema="formSchema"
        :form-state="formState"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  createFormSchema,
  createColumns,
} from '~/models/liveMngWithdrawalsModel';

definePageMeta({
  layout: 'live-mng',
  middleware: ['auth'],
});

const { formSchema, formState } = createFormSchema();
const columns = createColumns();
const { liveMngApi } = useApi();
const fetchFn = (params: any) => liveMngApi.getStreamerWithdrawnList(params);
</script>
