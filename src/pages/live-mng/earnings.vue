<template>
  <div class="min-h-full flex flex-col">
    <LiveMngEarningsHeader />
    <div class="bg-white mt-[20px] flex-1">
      <LiveMngFetchTable
        title="Earnings Details"
        :fetch-fn="fetchFn"
        :columns="columns"
        :form-schema="formSchema"
        :form-state="formState"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  createFormSchema,
  createGuildColumns,
  createPersonalColumns,
} from '~/models/liveMngEamingsModel';
import { useLiveMngStore } from '~/stores/modules/liveMng';

definePageMeta({
  layout: 'live-mng',
  middleware: ['auth'],
});
const liveMngStore = useLiveMngStore();

const { formSchema, formState } = createFormSchema();
const columns = liveMngStore.isPersona
  ? createPersonalColumns()
  : createGuildColumns();
const { liveMngApi } = useApi();
const fetchFn = (params: any) => liveMngApi.getStreamerRebateRecordList(params);
</script>
