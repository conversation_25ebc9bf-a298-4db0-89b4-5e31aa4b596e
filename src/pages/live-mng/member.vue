<template>
  <div class="min-h-full flex flex-col gap-[20px]">
    <LiveMngFetchTable
      :key="buttonAction"
      ref="fetchTableRef"
      title="Member List"
      :fetch-fn="fetchFn"
      :columns="columns"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <template #prefix>
        <LiveMngButtonGroup v-model="buttonAction" :options="buttonOptions" />
      </template>
      <template #suffix>
        <n-button
          v-if="liveMngStore.isMaster"
          type="success"
          @click="addMembers"
        >
          Add members
        </n-button>
      </template>
    </LiveMngFetchTable>
    <div class="flex-1 bg-white">
      <LiveMngFetchTable
        title="Guild Member Log"
        :fetch-fn="logFetchFn"
        :columns="logColumns"
        :form-schema="logFormSchema"
        :form-state="logFormState"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DataTableColumns } from 'naive-ui';
import {
  createFormSchema,
  createActiveColumns,
  createFormerColumns,
  createLogColumns,
} from '~/models/liveMngMemberModel';
import LiveMngForm from '~/components/liveMng/Form.vue';
import { INVITE_CONTENT, inviteFormConfig } from '~/constants/liveMng';
import { useLiveMngStore } from '~/stores/modules/liveMng';

definePageMeta({
  layout: 'live-mng',
  middleware: ['auth'],
});
interface ButtonConfig {
  columns: DataTableColumns;
  formSchema: ReturnType<typeof createFormSchema>['formSchema'];
  formState: ReturnType<typeof createFormSchema>['formState'];
  fetchFn: (params: any) => Promise<any>;
}
enum ActionKey {
  active = 'active',
  former = 'former',
}
const { liveMngApi } = useApi();
const liveMngStore = useLiveMngStore();
const fetchTableRef = ref();
const { openLiveMngDialog, closeLiveMngDialog } = useLiveMngDialog();

const buttonOptions = [
  { label: 'Active member', value: ActionKey.active },
  { label: 'Former member', value: ActionKey.former },
];
const buttonAction = ref<ActionKey>(ActionKey.active);
const refreshTable = () => {
  fetchTableRef.value?.refreshTable?.();
};
const addMembers = () => {
  const formRef = ref();
  openLiveMngDialog({
    class: 'w-[620px] rounded-[8px]',
    title: 'Add members',
    content: () =>
      h('div', null, [
        h('p', { class: 'text-[#3b82f6] mb-[20px]' }, INVITE_CONTENT),
        h(LiveMngForm, {
          ref: formRef,
          config: inviteFormConfig,
        }),
      ]),
    confirmText: 'Confirm Invitation',
    onConfirm: async () => {
      const valid = await formRef.value?.validate?.();
      if (!valid) return;
      const data = formRef.value?.getFormData?.();
      await liveMngStore.addStreamerGuildStreamer({
        streamer_uid: data.uid,
        remark: data.remarks,
      });
      closeLiveMngDialog();
    },
  });
};

const { formSchema: logFormSchema, formState: logFormState } =
  createFormSchema();
const logFetchFn = (params: any) => liveMngApi.getStreamerRecord(params);
const logColumns = createLogColumns();

const BUTTON_CONFIG: Record<ActionKey, ButtonConfig> = {
  active: {
    columns: liveMngStore.isMaster
      ? createActiveColumns(refreshTable)
      : createFormerColumns(),
    ...createFormSchema(),
    fetchFn: (params) =>
      liveMngApi.getStreamerList({ ...params, list_type: 1 }),
  },
  former: {
    columns: createFormerColumns(),
    ...createFormSchema(),
    fetchFn: (params) =>
      liveMngApi.getStreamerList({ ...params, list_type: 2 }),
  },
};

const currentConfig = computed(() => BUTTON_CONFIG[buttonAction.value]);

const columns = computed(() => currentConfig.value.columns);
const formSchema = computed(() => currentConfig.value.formSchema);
const formState = computed(() => currentConfig.value.formState);
const fetchFn = computed(() => currentConfig.value.fetchFn);
</script>
