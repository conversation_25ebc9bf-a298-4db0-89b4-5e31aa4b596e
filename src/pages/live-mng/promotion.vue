<template>
  <div class="min-h-full bg-white">
    <LiveMngFetchTable
      title="Promotion Invitation Records"
      :fetch-fn="fetchFn"
      :columns="columns"
      :form-schema="formSchema"
      :form-state="formState"
    />
  </div>
</template>

<script setup lang="ts">
import {
  createGuildFormSchema,
  createPersonalFormSchema,
  createPersonalColumns,
  createGuildColumns,
} from '~/models/liveMngPromotionModel';
import { useLiveMngStore } from '~/stores/modules/liveMng';

definePageMeta({
  layout: 'live-mng',
  middleware: ['auth'],
});
const liveMngStore = useLiveMngStore();

const { liveMngApi } = useApi();
const columns = liveMngStore.isPersona
  ? createPersonalColumns()
  : createGuildColumns();
const { formSchema, formState } = liveMngStore.isPersona
  ? createPersonalFormSchema()
  : createGuildFormSchema();
const fetchFn = (params: any) => liveMngApi.getStreamerUserList(params);
</script>
