<template>
  <n-spin :show="loading">
    <div class="min-h-full flex flex-col gap-[20px]">
      <LiveMngPromotionLink />
      <LiveMngGuildCard
        v-if="!liveMngStore.isPersona"
        :guild-name="permissionInfo?.guild_name ?? ''"
        :guild-master="permissionInfo?.guild_leader ?? ''"
        :is-guild-master="liveMngStore.isMaster"
        :leave-guild="leaveGuild"
      />
      <LiveMngIncome
        v-if="showHomeData"
        :is-persona="liveMngStore.isPersona"
        :income-balance="overview.rebateBalance"
        :blocked-income="overview.frozenRebate"
      />
      <LiveMngChartTable
        v-if="showHomeData"
        ref="chartTableRef"
        :data="tableData"
        :loading="loading"
      />
    </div>
  </n-spin>
</template>
<script lang="ts" setup>
import { LEAVE_GUILD_CONTENT } from '~/constants/liveMng';
import { useLiveMngStore } from '~/stores/modules/liveMng';
import { LiveMngPage, Permission } from '~/types/liveMng';
definePageMeta({
  layout: 'live-mng',
  middleware: ['auth'],
});
const liveMngStore = useLiveMngStore();
const permissionInfo = computed(() => liveMngStore.permissionInfo);
const showHomeData = computed(() => {
  if (liveMngStore.isMaster || liveMngStore.isPersona) return true;
  const guildPermission = permissionInfo.value?.guild_permission;
  if (guildPermission) {
    const homePermission = guildPermission[LiveMngPage.HOME];
    if (homePermission === Permission.NONE) return false;
  }
  return true;
});

const { liveMngApi } = useApi();
const chartTableRef = ref();
const overview = reactive({
  rebateBalance: '',
  frozenRebate: '',
});
const tableData = ref([]);

const loading = ref(true);
const { openLiveMngDialog, closeLiveMngDialog } = useLiveMngDialog();

const leaveGuild = () => {
  openLiveMngDialog({
    class: 'w-[536px] rounded-[8px]',
    title: 'Confirm leaving the guild',
    buttonClass: 'flex-1',
    content: LEAVE_GUILD_CONTENT,
    onConfirm: async () => {
      await liveMngStore.leaveGuild();
      closeLiveMngDialog();
    },
  });
};
const getOverview = async () => {
  const { data: res } = await liveMngApi.getStreameroOverview();
  const data = res.value?.data;
  if (data) {
    overview.rebateBalance = '$ ' + data.rebate_balance;
    overview.frozenRebate = '$ ' + data.frozen_rebate;
  }
};
const getIncomeList = async () => {
  const { data: res } = await liveMngApi.getStreamerIncomeList();
  const data = res.value?.data;
  if (data) {
    const reverseData = data.items.reverse();
    tableData.value = reverseData;
    const sliced = reverseData.slice(0, 30);
    chartTableRef.value?.changeChartData([...sliced].reverse());
  }
};

const requestApi = async () => {
  await Promise.all([getOverview(), getIncomeList()]);
  loading.value = false;
};
onMounted(() => {
  requestApi();
});
</script>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-th) {
  border-bottom: none !important;
}
</style>
