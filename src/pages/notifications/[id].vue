<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="text-[rgb(255,255,255,0.6)]">
    <div class="cursor-pointer text-[#7D90CA] mb-9" @click="backMsg">
      <BaseIcon name="back" class="mr-2 !text-sm mb-0.5"></BaseIcon>
      <span>{{ $t('back') }}</span>
    </div>

    <div class="bg-[rgb(80,100,160,0.15)] rounded-lg px-6 py-8">
      <div class="text-center border-b border-bg-black">
        <h2 class="text-xl text-white mb-4">
          {{ detail.title }}
        </h2>
        <div>{{ detail.send_time }}</div>
      </div>
      <div
        class="mt-6 detail-cont"
        @click="handleTarget"
        v-html="detail.content"
      ></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import JoinGuild from '~/components/notification/dialog/JoinGuild.vue';
import { useAppStore } from '~/stores/modules/app';
const $router = useRouter();
const appStore = useAppStore();
const { notificationApi } = useApi();
const detail = ref<ApiV1NotifyInfoGet200ResponseData>({
  title: '',
  send_time: '',
  content: '',
});
const guild = computed(() => {
  const cont = detail.value.content;
  if (cont) {
    const hasDelTag = /<del[\s\S]*?>[\s\S]*?<\/del>/.test(cont);
    const hasBthTag = /<button[\s\S]*?>[\s\S]*?<\/button>/.test(cont);
    if (hasDelTag && hasBthTag) {
      const matchDelCont = cont.match(/<del[^>]*>(.*?)<\/del>/i);
      if (matchDelCont) {
        return matchDelCont[1];
      }
    }
  }
  return { id: '', name: '' };
});
// 获取详情
const getDetail = async () => {
  const id = $router.currentRoute.value.params.id;
  const { data: req } = await notificationApi.getDetail(Number(id as string));
  const data = req.value?.data;
  if (data) {
    detail.value = data;
  }
};

await getDetail();
appStore.getMetadata();
// 返回上一页
const backMsg = () => {
  if ($router.options.history.state.back) {
    $router.back();
  } else {
    $router.push('/notifications');
  }
};

const dialog = useAppDialog();
const handleTarget = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (guild.value && target.tagName.toLowerCase() === 'button') {
    dialog.open(JoinGuild, {
      style: {
        width: '550px',
      },
      contentProps: {
        guild: guild.value,
      },
    });
  }
};
</script>
<style lang="scss" scoped>
.detail-cont {
  :deep(del) {
    text-decoration: none;
  }
  :deep(img),
  :deep(svg),
  :deep(video),
  :deep(canvas),
  :deep(audio),
  :deep(iframe),
  :deep(embed),
  :deep(object) {
    display: inline-block;
  }
  :deep(img),
  :deep(video) {
    @apply max-xl:!max-w-full max-xl:!h-auto object-contain;
  }
  :deep(button) {
    @apply inline text-theme-color cursor-pointer;
  }
}
</style>
