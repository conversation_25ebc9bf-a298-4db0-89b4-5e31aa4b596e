<template>
  <div class="rounded-lg pt-2 relative mb-6">
    <div class="sm:absolute right-0 top-2 z-10">
      <MainButton
        type="linear"
        text
        class="max-sm:!px-0"
        :disabled="tabs[0].total === 0"
        @click="readAll"
      >
        {{ $t('mark_all_as_read') }}
      </MainButton>
    </div>

    <n-tabs
      animated
      class="w-full"
      tab-class="uppercase"
      :value="tabs[activeTabIndex].id"
      @update:value="changeTab"
    >
      <n-tab-pane
        v-for="(tab, index) in tabs"
        :key="tab.id"
        :tab="`${tab.total} ${tab.title}`"
        :name="tab.id"
      >
        <NotificationTab
          :paginator="{
            total: tab.total,
            page: tab.page,
          }"
          :data="tab.data"
          :empty-msg="tab.empty"
          :loading="tab.loading"
          @update-page="(page: number) => updatePage(page, index)"
        />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '~/stores/modules/app';

definePageMeta({
  middleware: ['auth'],
});

const appStore = useAppStore();
const toast = useAppToast();
const $router = useRouter();
const { notificationApi } = useApi();
const { t } = useI18n();
const routeTabIndex = $router.currentRoute.value.query.tab || 0;
const activeTabIndex = ref(Number(routeTabIndex));
const readAllLoading = ref(false);
const tabs = reactive<
  {
    id: number;
    title: ComputedRef<string>;
    total: number;
    page: number;
    data: ApiV1NotifyGet200ResponseDataListInner[];
    loading: boolean;
    empty: ComputedRef<string>;
  }[]
>([
  {
    id: 1,
    title: computed(() => t('unread')),
    total: 0,
    page: 1,
    data: [],
    loading: false,
    empty: computed(() => t('no_unread_messages')),
  },
  {
    id: 0,
    title: computed(() => t('message')),
    total: 0,
    page: 1,
    data: [],
    loading: false,
    empty: computed(() => t('no_news')),
  },
]);
// 切换tab
const changeTab = (val: number) => {
  const index = tabs.findIndex((el) => el.id === val);
  activeTabIndex.value = index;
  // 请求数据
  getList();
  $router.push(`/notifications?tab=${index}`);
};
// 全部已读
const readAll = async () => {
  readAllLoading.value = true;
  const { data: req } = await notificationApi.readAll();
  readAllLoading.value = false;
  if (req.value.code === 0) {
    getList();
    appStore.getMetadata();
    toast.success({ content: t('operation_successful') });
  }
};
// 获取列表
const getList = async (tabIndex?: number) => {
  // 0：获取全部消息，1：获取未读消息，2：获取已读消息
  const index = tabIndex !== undefined ? tabIndex : activeTabIndex.value;
  const activeTab = tabs[index];
  const page = activeTab.page;
  activeTab.loading = true;
  const { data: req } = await notificationApi.getList({
    read_status: activeTab.id,
    page,
    page_size: 15,
  });
  const data = req.value.data;
  activeTab.loading = false;
  if (data) {
    // 展示
    activeTab.data = data.list;
    tabs[0].total = data.unread_count;
    tabs[1].total = data.total_count;
  }
};

// 分页
const updatePage = (page: number, index: number) => {
  tabs[index].page = page;
  getList();
};
await getList();
appStore.getMetadata();
</script>
<style lang="scss" scoped>
:deep(.n-tab-pane .n-spin-container) {
  border-top: 1px solid rgba(80, 100, 160, 0.15);
}
</style>
