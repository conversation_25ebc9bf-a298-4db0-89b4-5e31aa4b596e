<template>
  <div>
    <Announcement :announcements="announcements" />
    <!-- 战报列表 -->
    <ClientOnly>
      <div
        v-if="ipPermission"
        class="flex gap-x-[8px] gap-y-[15px] max-sm:flex-col"
      >
        <HomeBroadcast
          :size="4"
          :title="$t('hot')"
          icon="hot-active"
          valuable
          class="w-full sm:w-[506px]"
          contentClass="border-[#524833]"
        />
        <HomeBroadcast
          :size="7"
          :title="$t('all')"
          icon="all"
          class="w-full sm:w-[862px]"
          contentClass="border-transparent "
        />
      </div>
    </ClientOnly>
    <!-- roll房列表 -->
    <HomeBox
      v-show="!(!loadings.rollroom && !roomList.size)"
      :title="$t('roll')"
      path="/rollroom"
    >
      <div class="rollroom-list">
        <template v-if="loadings.rollroom">
          <SkeletonRollroom v-for="item in 4" :key="item" />
        </template>
        <template v-else>
          <RollroomCard
            v-for="[id, item] in roomList"
            :key="id"
            :info="item"
            @update-room="updateRoom"
          />
        </template>
      </div>
    </HomeBox>
    <!-- 对战列表 -->
    <ClientOnly>
      <HomeBox
        v-if="ipPermission"
        v-show="!caseBattle?.empty"
        :title="$t('case_battles')"
        path="/case-battles"
      >
        <CaseBattlesListActive
          ref="caseBattle"
          :pageSize="2"
          :params="{ status: [1, 2, 3], order_by: 'cstatus', order: 1 }"
          :canMore="false"
          :removeUserBattle="true"
          :isHomePage="true"
          :limit="2"
        />
      </HomeBox>
      <!-- 宝箱列表 -->
      <template v-for="(caseList, key) in caseLists" :key="key">
        <HomeBox
          v-if="ipPermission"
          v-show="
            !(!loadings[key as keyof typeof loadings] && !caseList.length)
          "
          :title="`${$t(i18nCaseTitle[key as keyof typeof i18nCaseTitle])}`"
          path="/cases"
        >
          <div class="cases-list">
            <template v-if="loadings[key as keyof typeof loadings]">
              <SkeletonCase v-for="item in 6" :key="item" />
            </template>
            <template v-else>
              <CasesCaseCard
                v-for="(item, index) in caseList"
                :key="index"
                :info="item"
              />
            </template>
          </div>
        </HomeBox>
      </template>
    </ClientOnly>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import { useAppStore } from '~/stores/modules/app';
import ActivitiesCarousel from '~/components/home/<USER>/ActivitiesCarousel.vue';
interface BattleComponentInstance {
  empty: ComputedRef<boolean>;
}
definePageMeta({
  middleware: ['verify-email'],
});
const dialog = useAppDialog();
const { homeApi, casesApi } = useApi();
const appStore = useAppStore();
const { announcements } = useAnnouncements();
const ipPermission = computed(() => appStore.ipPermission);
// roll房
const roomList = ref<Map<number, ApiV1RollroomListGet200ResponseDataListInner>>(
  new Map(),
);
// 对战
const caseBattle = ref<BattleComponentInstance | null>(null);
// 宝箱列表数据
const caseLists = ref<{
  [key: string]: V1CasesItem[];
}>({
  New: [],
  Hot: [],
  Recommend: [],
});
const i18nCaseTitle = {
  New: 'new_case',
  Hot: 'hot_case',
  Recommend: 'recommend_case',
};
// 数据loading情况,展示骨架屏
const loadings = ref({
  broadcast: true,
  rollroom: true,
  New: true,
  Hot: true,
  Recommend: true,
});

// 获取roll房列表
const getRollroom = async () => {
  loadings.value.rollroom = true;
  const { data: req } = await homeApi.getRollroom();
  const data = req.value?.data;
  if (data) {
    roomList.value = new Map(data.list.map((item) => [item.id, item]));
  }
  loadings.value.rollroom = false;
};
// 更新房间信息
const updateRoom = (info: ApiV1RollroomListGet200ResponseDataListInner) => {
  roomList.value.set(info.id, {
    ...roomList.value.get(info.id),
    ...info,
  });
};

// 获取宝箱列表
const getCases = async (type: string) => {
  if (!ipPermission.value) return;
  loadings.value[type as keyof typeof loadings.value] = true;
  const { data: req } = await casesApi.getCasesList({
    page: 1,
    page_size: type === 'New' ? 6 : 12,
    is_hot: type === 'Hot' ? 1 : undefined,
    is_recommend: type === 'Recommend' ? 1 : undefined,
    is_new: type === 'New' ? 1 : undefined,
    type: 1,
  });
  const data = req.value?.data;
  if (data) {
    caseLists.value[type as keyof typeof caseLists.value] = data.list || [];
  }
  loadings.value[type as keyof typeof loadings.value] = false;
};
const filiterCarousel = (
  list: ApiV1NotifyPopupGet200ResponseDataListInner[],
) => {
  const todayStr = dayjs().format('YYYY-MM-DD');
  const activitisCarouselLocal = JSON.parse(
    localStorage.getItem('activitisCarousel') ||
      `{"once": [], "${todayStr}": []}`,
  );
  const activitisCarouselSession = JSON.parse(
    sessionStorage.getItem('activitisCarousel') || '[]',
  );

  const newLocal: {
    once: number[];
    [key: string]: number[];
  } = {
    once: [],
    [todayStr]: [],
  };
  const newSession: number[] = [];
  const carouselList = list.filter(
    (item: ApiV1NotifyPopupGet200ResponseDataListInner) => {
      if (item.display_type === 1) {
        // 1: "单次", 存local 不清缓存只展示1次
        newLocal.once.push(item.id);
        if (activitisCarouselLocal.once.includes(item.id)) {
          return false;
        }
      } else if (item.display_type === 2) {
        // 2: "每次", 存session，会话有效
        newSession.push(item.id);
        if (activitisCarouselSession.includes(item.id)) {
          return false;
        }
      } else if (item.display_type === 3) {
        // 3: "每日", 今天时间有效
        newLocal[todayStr].push(item.id);
        if (activitisCarouselLocal[todayStr]?.includes(item.id)) {
          return false;
        }
      }
      // 4: "持续", 不存，每次都展示
      return true;
    },
  );
  localStorage.setItem('activitisCarousel', JSON.stringify(newLocal));
  sessionStorage.setItem('activitisCarousel', JSON.stringify(newSession));
  return carouselList;
};
const getActivitis = async () => {
  const { data: req } = await homeApi.getActivitis();
  const data = req.value?.data;
  if (data.list?.length) {
    const list = data.list.sort((a, b) => b.sort_num - a.sort_num);
    const carouselList = filiterCarousel(list);
    if (carouselList.length > 0) {
      dialog.open(ActivitiesCarousel, {
        class: 'w-[289px] sm:w-[578px] p-0 bg-[#151A29] rounded-[8px]',
        titleClass: '',
        closable: false,
        contentProps: {
          carousel: carouselList,
        },
      });
    }
  }
};
getRollroom();
onMounted(() => {
  if (ipPermission.value) {
    getCases('New');
    getCases('Hot');
    getCases('Recommend');
  }
  getActivitis();
});
</script>
<style lang="scss" scoped>
.cases-list {
  @apply grid gap-2.5 grid-cols-[repeat(auto-fill,minmax(160px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))];
}
.rollroom-list {
  @apply grid gap-x-[16px] -mt-[46px] grid-cols-[repeat(auto-fill,minmax(332px,1fr))];
}
</style>
