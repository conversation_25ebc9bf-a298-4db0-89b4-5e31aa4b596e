<template>
  <n-spin :show="loading && !pageLoading && page === 1" class="grow">
    <div
      class="flex w-full justify-between mb-5 flex-wrap items-center gap-y-4"
    >
      <FilterForm
        class="flex max-sm:w-full"
        :form-schema="formSearchSchema"
        :form-state="formSearchState"
      >
        <FilterComponetsFormItem
          v-for="item in formSearchSchema"
          :key="item.key"
          class="w-full"
          :field="item"
          :field-name="item.key"
          :model-value="formSearchState[item.key]"
          @update:model-value="
            (value: string) => handleSearhcUpdate(item.key, value)
          "
        />
      </FilterForm>
      <FilterForm
        class="flex justify-end items-center max-sm:w-full max-sm:justify-start gap-2"
        :form-schema="formSchema"
        :form-state="formState"
      >
        <FilterComponetsFormItem
          v-for="item in formSchema"
          :key="item.key"
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          @update:model-value="(value: string) => handleUpdate(item.key, value)"
        />
      </FilterForm>
    </div>
    <!-- 骨架屏 -->
    <template v-if="pageLoading">
      <div class="case-list">
        <SkeletonCase v-for="index in 24" :key="index" />
      </div>
    </template>
    <!-- 内容部分 -->
    <template v-else>
      <div v-if="lists.length" class="case-list">
        <CasesCaseCard
          v-for="(item, index) in lists"
          :key="index"
          :info="item"
        />
      </div>
      <Empty
        v-else
        :msg="t('no_corresponding_case_found')"
        class="mt-10 py-20"
        column
      ></Empty>
    </template>
    <template v-if="page > 1">
      <n-spin v-if="loading && !pageLoading" size="small" />
    </template>
  </n-spin>
</template>
<script lang="ts" setup>
import { debounce } from 'lodash-es';
import { createFormSchema } from '~/models/casesFilterModel';
const { formSchema, formState, formSearchSchema, formSearchState } =
  createFormSchema();
const { casesApi } = useApi();
const { t } = useI18n();
const { y: scrollY } = useWindowScroll();
// 分页
const page = ref(1);
const total = ref(0);
const loading = ref(true);
const pageLoading = ref(true);
const noMore = computed(() => lists.value.length >= total.value);
// 列表数据
const lists = ref<V1CasesItem[]>([]);
// 接口参数
const params = computed(() => {
  const priceRange = formState.price_range.split(',');
  return {
    page: page.value,
    page_size: 30,
    price_min: Number(priceRange[0]),
    price_max: Number(priceRange[1]),
    ...formSearchState,
    order_by: formState.sort,
    type: 1,
  };
});
// 获取列表
const getLists = async (reset: boolean = false) => {
  loading.value = true;
  if (reset) {
    page.value = 1;
  }
  try {
    const { data: req } = await casesApi.getCasesList(params.value);
    const data = req.value?.data;
    if (data) {
      if (reset) lists.value = [];
      lists.value.push(...(data.list || []));
      total.value = data.total || 0;
    }
  } finally {
    pageLoading.value = false;
    loading.value = false;
  }
};
getLists();

// 筛选条件
const handleUpdate = (key: string, value: any) => {
  if (formState[key] !== value) {
    formState[key] = value;
    page.value = 1;
    getLists(true);
  }
};
// 搜索
const handleSearhcUpdate = (key: string, value: any) => {
  formSearchState[key] = value;
  page.value = 1;
  getLists(true);
};
// 分页
const handlePage = () => {
  if (loading.value || noMore.value) return;
  page.value += 1;
  getLists();
};
const scrollTouchBottom = debounce(handlePage, 500);
watch(scrollY, (newY) => {
  if (newY && window) {
    const screenH = window.screen.height;
    const pageH = document.body.clientHeight;
    const scrollH = pageH - screenH;
    if (scrollH - newY < 100) {
      scrollTouchBottom();
    }
  }
});
</script>
<style lang="scss" scoped>
.case-list {
  @apply grid my-4 gap-2.5 grid-cols-[repeat(auto-fill,minmax(160px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))];
}
</style>
