<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="case-bg">
    <!-- 箱子介绍 -->
    <CasesCaseInfo :info="caseInfo" />
    <div v-if="putaway" class="bg-bg-gray py-20 text-center text-lg">
      {{ $t('case_offline') }}
    </div>
    <!-- 动画 -->
    <CasesOpenCase
      v-else
      ref="openCaseAni"
      :multiple="casesOpenNum > 1"
      :fast="openSpeed === 'fast'"
      :skip="openSpeed === 'skip'"
      :btnDisabled="disabledControl"
      :convertLoading="convertLoading"
      :levelName="levelName"
      @finish="finish"
      @convert="convertConfirm"
    />
    <!-- 跳过/打开 -->
    <CasesOpenControl
      v-model:open-num="casesOpenNum"
      v-model:openSpeed="openSpeed"
      :disabled="disabledControl || convertLoading || putaway"
      :levelName="levelName"
      :keyQty="caseInfo.key_qty || 0"
      :keyIcon="caseInfo?.level_key_icon || '0'"
      @update-num="caseOpenStore.createSpinnerLists"
      @open-cases="openCases"
      @convert="convertConfirm"
    />
    <div
      class="text-[#7D90CA] w-full text-right mt-4"
      v-html="$t('open_case_enter')"
    ></div>

    <!-- luck 箱子 -->
    <div v-if="caseOpenStore.caseLuck.length && !levelName">
      <h2
        class="text-[20px] sm:text-[24px] font-bold text-white my-[25px] sm:my-[40px]"
      >
        {{ $t('lucky_items') }}
      </h2>
      <AnimateAddNewCase
        class="flex overflow-auto sm:overflow-hidden gap-x-[8px] luck-items"
      >
        <div
          v-for="items in caseOpenStore.caseLuck"
          :key="items[0].record_id"
          class="flex shrink-0 gap-x-[4px] sm:gap-x-[8px]"
        >
          <CasesSkinCardLucky
            v-for="(item, index) in items"
            :key="item.record_id"
            :info="item"
            class="h-[220px] sm:h-[256px] w-[160px] sm:w-[222px] shrink-0"
            :class="{ '-translate-y-full drop-ani': item.update }"
            :style="`animationDelay: ${(400 / items.length) * (items.length - index - 1)}ms`"
            @mouseover="hoverLuckItem(true)"
            @mouseleave="hoverLuckItem(false)"
          />
        </div>
      </AnimateAddNewCase>
    </div>
    <!-- 宝箱内容 -->
    <div>
      <h2
        class="text-[20px] sm:text-[24px] font-bold text-white my-[25px] sm:my-[40px]"
      >
        {{ $t('case_contains') }}
      </h2>
      <div class="case-list">
        <CasesSkinCardContain
          v-for="item in caseOpenStore.caseContain"
          :key="item.goods_id"
          :info="item"
          class="h-[269px]"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useCaseOpenStore } from '~/stores/modules/caseOpen';
import type { SpinnerInfoType } from '~/types/cases';
interface OpenCaseAni {
  start: (experiment: boolean, totalPrice: string) => void;
}
const { casesApi } = useApi();
const toast = useAppToast();
const caseOpenStore = useCaseOpenStore();
await caseOpenStore.getCaseInfo();
const { t } = useI18n();
const { openConfirm } = useDialogPromptsConfirm('caseConvertPrompts');
// opencase el
const openCaseAni = ref<OpenCaseAni>();
// 开箱个数
const casesOpenNum = ref(1);
// 是否在动画中
const disabledControl = ref(false);
// 动画执行速度
const openSpeed = ref('ordinary');
// 2:宝箱下架,1上架
const putaway = computed(() => caseOpenStore.caseInfo.is_putaway === 2);
// 箱子信息
const caseInfo = computed(() => caseOpenStore.caseInfo);
const levelName = computed(() =>
  caseInfo.value.type === 2 ? caseInfo.value.level_name : '',
);
// 战报更新数据
const {
  update,
  setItem,
  hover: broadcastHover,
} = useBroadcastUpdate({
  list: caseOpenStore.caseLuck,
  slug: caseInfo.value.slug,
});

// 打开箱子
const openCases = (experiment: boolean, totalPrice: string) => {
  disabledControl.value = true;
  openCaseAni.value?.start(experiment, totalPrice);
};

// 动画全部结束
const finish = () => {
  disabledControl.value = false;
};
const convertLoading = ref(false);
// 转换
const convert = async (list: SpinnerInfoType[], price: number) => {
  convertLoading.value = true;
  const goods = list.map((el: SpinnerInfoType) => {
    return {
      pawn_price: Number(el.selectItem?.pawn_price || '0'),
      backpack_id: el.res?.backpack_id,
    };
  });
  const { data: req } = await casesApi.convertToCoin(goods);
  const data = req.value?.data;
  if (data) {
    list.forEach((el) => {
      el.converted = true;
    });
    toast.success({
      content: t('conversion_successful_obtain_a_coins', { a: price }),
    });
  }
  convertLoading.value = false;
};
// 转换确认
const convertConfirm = (list: SpinnerInfoType[], price: number) => {
  openConfirm({
    title: t('confirm_conversion'),
    content: t('skin_convert_confirmation', {
      a: price,
    }),
    onConfirm: () => convert(list, price),
  });
};

// 划上luck
const hoverLuckItem = (hover: boolean) => {
  if (hover !== broadcastHover.value) {
    broadcastHover.value = hover;
    if (!hover) {
      setItem();
    }
  }
};
// 战报socket
useSocketListener('cases', {
  onRecord: update,
});
onBeforeUnmount(() => {
  caseOpenStore.reset();
});
</script>
<style lang="scss" scoped>
.case-list {
  @apply grid my-4 gap-[8px] grid-cols-[repeat(auto-fill,minmax(160px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))];
}
.drop-ani {
  animation: drop 300ms linear forwards;
}
@keyframes drop {
  0% {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
