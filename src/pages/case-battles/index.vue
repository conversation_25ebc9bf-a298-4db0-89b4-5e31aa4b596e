<template>
  <div v-if="!isPageLoading">
    <div
      class="flex items-center justify-end gap-[24px] max-md:w-full max-md:mt-2"
    >
      <div class="flex items-center gap-[8px] font-bold">
        <span class="text-purple-1">{{ $t('battles') }}</span>
        <b v-if="!loading" class="text-primary-400">
          {{ totalBattlesCount }}
        </b>
      </div>
      <n-button
        text-color="#000"
        class="!font-medium rounded-[4px] min-w-[149px] min-h-[42px] btn-gradient-primary uppercase"
        color="#edc254"
        @click="createBattle"
      >
        {{ $t('create_battle') }}
      </n-button>
    </div>
    <myCaseBattlesList
      v-if="userInfo?.uid"
      ref="currentBattlesRef"
    ></myCaseBattlesList>
    <div class="my-5">
      <div
        class="flex item-center justify-between flex-wrap text-[20px] sm:text-[24px] font-bold"
      >
        <div class="uppercase">{{ $t('active_battles') }}</div>
        <div class="flex justify-between max-sm:w-full max-sm:mt-2">
          <div class="w-full">
            <FilterForm
              ref="form"
              class="flex w-full justify-between items-center flex-wrap gap-2 sm:gap-6"
              :form-schema="formSchema"
              :form-state="formState"
            >
              <FilterComponetsFormItem
                :field="formSchema.is_can_join"
                :field-name="formSchema.is_can_join.key"
                :model-value="formState.is_can_join"
                @update:model-value="
                  (value: boolean) =>
                    handleUpdate(formSchema.is_can_join.key, value)
                "
              />
              <FilterComponetsFormItem
                :field="formSchema.order_key"
                :field-name="formSchema.order_key.key"
                :model-value="formState.order_key"
                class="w-36!"
                @update:model-value="
                  (value: any) => handleUpdate(formSchema.order_key.key, value)
                "
              />
            </FilterForm>
          </div>
        </div>
      </div>
      <div class="flex-layout min-h-[400px] py-[30px]">
        <ActiveBattles
          ref="activeBattlesRef"
          :params="params"
          :battleListSort="battleListSort"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import myCaseBattlesList from '@/components/caseBattles/list/MyBattleList.vue';
import ActiveBattles from '~/components/caseBattles/list/Active.vue';
import { createFormSchema } from '@/models/caseBattlesFilterModel';
import { useBattleStore } from '~/stores/modules/battle';
import { useSettingStore } from '~/stores/modules/setting';

const { formSchema, formState } = createFormSchema();

const pagination = ref({
  page: 1,
  pageSize: 18,
  total: 0,
});

const router = useRouter();
const battleStore = useBattleStore();
const settingStore = useSettingStore();
const { totalBattlesCount } = storeToRefs(battleStore);
const battleListSort = ref('battle_cost-desc');
const showAffordableOnly = ref(false);
const isPageLoading = ref(true);
const currentBattlesRef = ref<any>(null);
const activeBattlesRef = ref<any>(null);

const userInfo = computed(() => settingStore.userInfo);

const loading = computed(
  () =>
    activeBattlesRef.value?.isPageLoading ||
    currentBattlesRef.value?.isPageLoading,
);

// 排序映射
const SORT_MAPPING = {
  battle_cost: (order: any) => `battle_cost-${order === 1 ? 'asc' : 'desc'}`,
  created_at: (order: any) => `started_at-${order === 1 ? 'asc' : 'desc'}`,
} as const;

type SortKey = keyof typeof SORT_MAPPING;

// 处理排序
const handleSort = (orderBy: SortKey, order: any) => {
  const sortFn = SORT_MAPPING[orderBy];
  if (sortFn) {
    updateSort(sortFn(order));
  }
};
const processOrderKey = (orderKey: any) => {
  return orderKey.split(',').reduce((acc: any, stock: any) => {
    const [key, value] = stock.split('=');
    acc[key] = Number.isNaN(Number(value)) ? value : Number(value);
    return acc;
  }, {});
};

const params = computed(() => {
  const { order_key: orderKey } = formState;
  // 处理order_key
  if (orderKey) {
    Object.assign(formState, processOrderKey(orderKey));
  }
  // eslint-disable-next-line
  const { order_key, is_can_join: isCanJoin, ...rest } = formState;
  handleSort(formState.order_by, formState.order);
  return {
    page: pagination.value.page,
    page_size: pagination.value.pageSize,
    is_active: 1,
    is_can_join: isCanJoin ? 1 : 0,
    ...rest,
  };
});

// 排序和过滤
const handleUpdate = (key: string, value: any) => {
  formState[key] = value;
  showAffordableOnly.value = formState.is_can_join;
};

// 更新排序
const updateSort = (sort: string) => {
  battleListSort.value = sort;
};

// 创建对战
const createBattle = () => {
  if (!checkLogin()) {
    return;
  }
  router.push('/case-battles/create');
};

onMounted(() => {
  isPageLoading.value = false;
});
</script>

<style scoped lang="scss"></style>
