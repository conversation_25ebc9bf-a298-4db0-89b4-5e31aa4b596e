<template>
  <div :key="battleInfo?.arena_id" :data-id="battleInfo?.arena_id">
    <!-- 返回按钮和其他控制按钮 -->
    <div class="mt-xl mb-lg flex flex-wrap items-center justify-between">
      <div
        class="mb-lg flex items-center gap-[8px] cursor-pointer text-purple-1 font-bold hover:text-theme-color"
      >
        <svgo-back class="w-[6px] h-[8px] mb-0" />
        <div>
          <p class="text-md" @click="goBack">
            {{ t('back_to_active_battles') }}
          </p>
        </div>
      </div>

      <div
        v-if="!isPageLoading"
        class="flex flex-wrap items-center gap-x-5 gap-y-2 sm:gap-[40px] text-purple-1"
      >
        <!-- 公平性链接 -->
        <Fairness
          :fairInfo="fairInfo"
          class="font-bold hover:!text-theme-color"
          :size="isMobile ? 'tiny' : 'small'"
        />
        <!-- 分享 -->
        <div class="flex items-center gap-md">
          <div class="flex items-center gap-md max-md:text-sm">
            <!-- 私人战斗 tooltip-->
            <Tooltip
              v-if="battleInfo.battle_mode === BattleModesType.SHARED"
              :trigger="battleInfo?.is_private === 1 ? 'hover' : 'manual'"
            >
              <template #trigger>
                <base-icon
                  name="lock"
                  class="text-red-1 mb-[1px] w-[13px] h-[16px] focus:outline-none"
                  @mouseenter.stop
                  @mouseleave.stop
                />
              </template>
              <span>{{ $t('private_battle') }}</span>
            </Tooltip>
            <Copy
              v-if="shared"
              :showIcon="battleInfo.battle_mode !== BattleModesType.SHARED"
              text="share_battle"
              tooltipSuccess="share_link_copied_successfully"
              :textToCopy="sharedLink"
              class="font-bold"
              @mouseenter.stop
              @mouseleave.stop
            ></Copy>
            <span
              v-if="
                battleInfo.battle_mode === BattleModesType.SHARED && !isCreator
              "
              class="font-bold"
            >
              {{ $t('private_battle') }}
            </span>
          </div>
        </div>
        <!-- 声音控制 -->
        <SoundSwitch
          class="min-w-[100px] flex items-center hover:text-theme-color font-bold"
        />
        <!-- 取消战斗 -->
        <n-button
          v-if="battleInfo.status === BattleStatusType.WAITING && isCreator"
          ghost
          color="#FF5555"
          class="max-sm:text-xs uppercase"
          @click="cancelBattleDialog"
        >
          {{ $t('cancel_the_battle') }}
        </n-button>
      </div>
      <n-skeleton v-else class="w-[400px] h-[42px] sm:ml-auto" />
    </div>
    <template v-if="!isPageLoading">
      <!-- 战斗信息容器 -->
      <CaseBattlesBattleInfoContainer
        :battle-cost="battleInfo.battle_cost"
        :case-list="battleInfo.battleCaseList"
        :finished="battleInfo.status === BattleStatusType.FINISHED"
        :private="battleInfo.is_private === 1"
        :selected-index="currentRound"
        :shared="shared"
        :is-uno-reverse="battleInfo.game_mode === GAME_MODES.UNO_REVERSE"
      />
      <!-- 平局决胜旋转器 -->
      <Transition
        enter-active-class="transition-[height] duration-700"
        enter-from-class="h-0"
        enter-to-class="h-full max-h-[124px]"
        leave-active-class="transition-[height] duration-700"
        leave-from-class="h-full max-h-[124px]"
        leave-to-class=""
        mode="out-in"
      >
        <CaseBattlesUserSpinner
          v-if="
            showTiebreaker && tiebreakerInfo.length && battleInfo.tiebreaker
          "
          :battle-arena-id="battleInfo.arena_id"
          :is-page-loading="isPageLoading"
          :users="tiebreakerInfo"
          :battle-mode="battleInfo.battle_mode"
          :selected-index="battleInfo.tiebreaker.result"
          class="bg-dark-3 h-[124px]"
          @spin-ended="handleTiebreakerSpinEnded"
        />
      </Transition>
      <div
        class="relative mt-lg bg-dark-4 border-dark-5 border-3 rounded-[8px]"
      >
        <!-- 渲染旋转器刻度 -->
        <template v-if="!isLoading">
          <CaseBattlesSpinnerTickSmall
            :class="[
              'absolute inset-y-0 left-[20px] z-10 my-auto translate-x-[-50%]  transition-colors duration-1000 pointer-events-none',
            ]"
          />
          <CaseBattlesSpinnerTickSmall
            :class="[
              'absolute inset-y-0 right-[20px] z-10 my-auto translate-x-[50%] rotate-180 transition-colors duration-1000 pointer-events-none',
            ]"
          />
        </template>
        <div class="relative h-[299px] xl:h-[406px]">
          <!-- 战斗取消状态 -->
          <div
            v-if="battleInfo.status === BattleStatusType.CANCELLED"
            class="flex h-full flex-col items-center justify-center gap-md"
          >
            <BaseIcon name="close-filled" class="size-[40px]" />
            <Text bold="medium">
              {{ t('this_battle_was_cancelled') }}
            </Text>
          </div>
          <!-- 等待状态的战斗控制 -->
          <CaseBattlesBattleControls
            v-if="battleInfo.status === BattleStatusType.WAITING"
            :battle-cost="battleInfo.battle_cost"
            :battle-creator-id="battleInfo.creator_uid"
            :disable-controls="!awaitingPlayers"
            :players="battleInfo.battlePlayers"
            :total-number-of-players="battleInfo.player_number"
            :is-uno-reverse="battleInfo.game_mode === GAME_MODES.UNO_REVERSE"
            @call-bot="handleCallBot"
            @cancel-battle="cancelBattleDialog"
            @join-battle="joinBattleDialog"
            @leave-battle="leaveBattleDialog"
          />

          <!-- 战斗进行中 -->
          <div v-if="battleInfo.status === BattleStatusType.RUNNING">
            <CaseBattlesSpinner
              bg-glow=""
              spinner-gap="gap-0"
              :is-loading="isPageLoading || !spinnerVisible"
              :spinner-config-array="spinnerConfig"
              :show-horizontal-spinner="false"
              :show-tick="false"
              spinner-height="h-[299px] xl:h-[406px]"
              :is-uno-reverse="false"
              @spin-ended="handleSpinEnded"
            >
              <template #default="props">
                <CaseBattlesBattleItem v-bind="props" />
              </template>
            </CaseBattlesSpinner>
          </div>
          <!-- 战斗结束状态 -->
          <CaseBattlesBattleEnd
            v-else-if="
              [BattleStatusType.FINISHED, BattleStatusType.TIEBREAKER].includes(
                battleInfo.status,
              )
            "
            :battle-cost="battleInfo.battle_cost"
            :is-uno-reverse="battleInfo.game_mode === GAME_MODES.UNO_REVERSE"
            :disable-converts="disableConverts"
            :mode="battleInfo.battle_mode"
            :players="battleInfo.battlePlayers"
            :show-insufficient-balance="false"
            :status="battleInfo.status"
            :winner-positions="winnerPositions"
            @modify-battle="handleRecreateOrModifyBattle('modify')"
            @needs-tie-breaker="handleTiebreakerNeeded"
            @recreate-battle="handleRecreateOrModifyBattle('recreate')"
            @recreate-battle-blur="handleRecreateBattleBlur"
            @convert-all="handleConvertItem"
          />
          <!-- 准备开始状态 -->
          <div
            v-else-if="battleInfo.status === BattleStatusType.WAITING_FOR_EOS"
            :class="[
              'relative flex h-full flex-col items-center justify-center',
            ]"
          >
            <svgo-count-backwards
              filled
              class="z-[0] absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] size-[320px]"
              :fontControlled="false"
            />
            <div
              class="z-[0] absolute top-[54%] left-[50%] translate-x-[-50%] translate-y-[-50%] size-[320px] flex flex-col items-center justify-center"
            >
              <Text size="large" bold class="relative z-1">
                {{ t('game_starting') }}
              </Text>
              <CaseBattlesHeading
                class="relative z-1 mb-xl mt-md text-[64px]"
                size="xl"
              >
                {{ countdown }}
              </CaseBattlesHeading>
            </div>
          </div>
        </div>
      </div>
      <!-- 用户对战卡片 -->
      <div class="mt-lg">
        <CaseBattlesBattleUserVsCards
          class="w-full pb-xxs"
          :mode="battleInfo.battle_mode"
          :players="battleInfo.battlePlayers"
          :total-number-of-players="battleInfo.player_number"
          :status="battleInfo.status"
        />
        <Transition
          class="transition-all duration-500"
          enter-from-class="opacity-0 max-h-[0]"
          enter-to-class="opacity-100 max-h-[64px]"
        >
          <CaseBattlesTotalUnboxed
            v-if="
              [
                BattleStatusType.WAITING_FOR_EOS,
                BattleStatusType.RUNNING,
                BattleStatusType.TIEBREAKER,
                BattleStatusType.FINISHED,
              ].includes(battleInfo.status)
            "
            class="h-full w-full pb-xxs"
            :mode="battleInfo.battle_mode"
            :players="battleInfo.battlePlayers"
            :total-number-of-players="battleInfo.player_number"
          />
        </Transition>
      </div>
      <!-- 物品列表 -->
      <n-scrollbar
        class="min-h-[390px] xl:min-h-[493px] max-h-[790px] xl:max-h-[813px]"
      >
        <div ref="itemRef" class="overflow-y-auto relative mt-lg max-w-full">
          <!-- 物品网格 -->
          <div class="grid w-full auto-cols-fr grid-flow-col gap-md">
            <div
              v-for="(_, playerIndex) in battleInfo.player_number"
              :key="`player-${playerIndex}`"
              :class="[
                'grid gap-[9px]',
                {
                  'grid-cols-1  md:grid-cols-3': battleInfo.player_number === 2,
                  'grid-cols-1  md:grid-cols-2': battleInfo.player_number === 3,
                  'grid-cols-1  lg:grid-cols-2': battleInfo.player_number === 4,
                },
              ]"
            >
              <!-- 战斗物品卡片 -->
              <div
                v-for="(item, itemIndex) in getPlayerItems(playerIndex)"
                :key="`player-${playerIndex}-item-${itemIndex}`"
              >
                <CaseBattlesBattleCardItem
                  :item="item"
                  :mode="battleInfo.battle_mode"
                  :status="battleInfo.status"
                  :aspect-ratio="
                    battleInfo.player_number === 4 ? '165/269' : '222/269'
                  "
                  :player-number="battleInfo.player_number"
                  :is-winner="battleInfo.winners === userInfo?.uid"
                  :disabled="disableConverts.includes(item.backpack_id)"
                  @battle-item-convert="handleConvertItem"
                />
              </div>
              <div
                v-for="(_, fillerIndex) in getFillerItems()"
                :key="`player-${playerIndex}-filler-item-${fillerIndex}`"
                class="flex h-[170px] w-full flex-col items-center justify-center px-sm py-lg xl:h-[195px] xl:p-lg"
              />
            </div>
          </div>
        </div>
      </n-scrollbar>
    </template>
    <template v-else>
      <n-skeleton class="w-full h-[140px]" />
      <n-skeleton class="mt-lg h-[412px]" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import { useSettingStore } from '@/stores/modules/setting';
import { useCaseBattleStore } from '@/stores/modules/caseBattle';

import { GAME_MODES } from '@/constants/battle';
import { SPINNER_DELAY, SPINNER_DURATION } from '@/constants/timing';
import {
  BattleStatusType,
  type BattleCase,
  type BattleRecord,
  type BattleType,
  BattleModesType,
} from '@/types/battles';
import { useSocketListener } from '@/composables/useSocketListener';
import { useBattleSelectedCasesStore } from '~/stores/modules/battleSelecteCases';

definePageMeta({
  getMeta: true,
});

const battleStore = useCaseBattleStore();
const {
  battleInfo,
  currentRound,
  awaitingPlayers,
  spinnerVisible,
  timeSinceRoundStarted,
  tiebreakerInfo,
  countdown,
  forceRefresh,
} = storeToRefs(battleStore) as {
  battleInfo: Ref<BattleType>;
  currentRound: ComputedRef<number>;
  awaitingPlayers: ComputedRef<boolean>;
  spinnerVisible: Ref<boolean>;
  timeSinceRoundStarted: Ref<number>;
  tiebreakerInfo: ComputedRef<any[]>;
  countdown: Ref<number>;
  forceRefresh: Ref<number>;
};
const {
  setBattleInitialState,
  pushNewRound,
  getPlayerItems,
  playerJoin,
  cancelBattle,
  clearBattleInfo,
  setEosBlock,
  playerLeave,
  finishBattle,
  setTiebreaker,
  setStatus,
  retryGetBattleInfo,
  generateTiebreakerData,
} = battleStore;

const { spinnerConfig, generateNewConfig } = useSpinnerConfig();
const battleSelecteCases = useBattleSelectedCasesStore();
const {
  caseSelections,
  currentMode,
  currentModePlayers,
  privateBattleEnabled,
  unoModeEnabled,
} = storeToRefs(battleSelecteCases);
const { createBattle, reset } = battleSelecteCases;
const route = useRoute();
const router = useRouter();
const settingStore = useSettingStore();
const { battleApi, casesApi } = useApi();
const { openConfirm } = useDialogPromptsConfirm('caseConvertPrompts');
const { t } = useI18n();
const toast = useAppToast();
const { width: windowWidth } = useWindowSize();
const isMobile = useIsMobile();

const winnerPositions = ref<any>([]);

const showTiebreaker = ref(false);

const isLoading = ref(false);

const cases = ref<V1CasesItem[]>([]);

const page = ref(1);

const pageSize = ref(20);

const recreateMode = ref('modify');

const itemRef = ref();

// const isScrollDisabled = ref(false);

const disableConverts = ref<number[]>([]);

let socketTimer: NodeJS.Timeout;

// 延迟旋转
const delaySpinnerStart = (ms: any) => setTimeout(ms, SPINNER_DELAY);

// 是否展示分享按钮
const shared = computed(() => {
  return (
    (battleInfo.value.battle_mode === 2 && isCreator.value) ||
    battleInfo.value.battle_mode === 1
  );
});

const sharedLink = computed(() => {
  return `${window.location.origin}/case-battles/game/${battleInfo.value?.arena_id}`;
});

const userInfo = computed(() => settingStore.userInfo);

// 是否是创建者
const isCreator = computed(
  () => battleInfo.value.creator_uid === userInfo.value?.uid,
);

// 计算属性：页面是否正在加载
const isPageLoading = computed(() => battleInfo.value?.arena_id === '');

const fairInfo = computed(() => {
  return {
    msg: t('fairness_battle_info'),
    seeds: [
      { title: t('battle_id'), value: battleInfo.value?.arena_id || '--' },
      {
        title: t('server_seed_hash'),
        value: battleInfo.value.private_seed_hash || '-',
      },
      { title: t('server_seed'), value: battleInfo.value.private_seed || '--' },
      {
        title:
          battleInfo.value.status === BattleStatusType.FINISHED
            ? t('eos_block_seed') + ` (${battleInfo.value.seed_index})`
            : t('eos_block_seed'),
        value: battleInfo.value.seed || '--',
      },
    ],
  };
});

watch(forceRefresh, () => {
  getBattleInfo();
  reSubscribe();
});

// 监听对战回合和对战标志
watch([currentRound, spinnerVisible], () => {
  if (!spinnerVisible.value || currentRound.value < 0) return;

  spinnerConfig.value = battleInfo.value.battlePlayers.map(
    (player: any, index: number) => {
      const caseItems =
        battleInfo.value.battleCaseList[currentRound.value].items;
      const selectedItem =
        player?.itemsData[currentRound.value] ||
        caseItems[caseItems.length - 1];
      const hash = `${battleInfo.value?.arena_id}-${currentRound.value}-${index}`;
      return {
        ...generateNewConfig({
          caseItems,
          hash,
          isFastMode: false,
          selectedItem,
          // TODO: 需要根据时间来调整 SPINNER_DURATION 3500 SPINNER_DELAY 500
          customTime: Math.max(
            SPINNER_DURATION - timeSinceRoundStarted.value,
            SPINNER_DELAY,
          ),
        }),
        isSpinning: false,
        spinEnded:
          timeSinceRoundStarted.value >= SPINNER_DURATION - SPINNER_DELAY,
      };
    },
  );
  if (timeSinceRoundStarted.value < SPINNER_DURATION - SPINNER_DELAY) {
    delaySpinnerStart(() => {
      spinnerConfig.value = spinnerConfig.value.map((config) => ({
        ...config,
        isSpinning: true,
      }));
    });
  }

  timeSinceRoundStarted.value = 0;

  // nextTick(() => {
  //   if (itemRef.value && !isScrollDisabled.value) {
  //     itemRef.value.scrollTo({
  //       top: itemRef.value.scrollHeight,
  //       behavior: 'smooth',
  //     });
  //   }
  // });
});

const goBack = () => {
  router.push('/case-battles');
};

function getFillerItems() {
  let itemsPerRow;
  if (battleInfo.value.player_number === 4) {
    itemsPerRow = windowWidth.value <= 1024 ? 1 : 2;
  } else if (battleInfo.value.player_number === 3) {
    itemsPerRow = windowWidth.value <= 448 ? 1 : 2;
  } else {
    itemsPerRow = windowWidth.value <= 448 ? 1 : 3;
  }
  const currentPlayerItems = getPlayerItems(0)?.length || 0;
  const fillerItemsCount = itemsPerRow - (currentPlayerItems % itemsPerRow);
  return fillerItemsCount === itemsPerRow ? 0 : fillerItemsCount;
}

// 处理调用机器人
const handleCallBot = debounce(
  async (position: number) => {
    const res = await battleApi.callBot({
      arena_id: battleInfo.value?.arena_id,
      position,
    });
    if (res.data.value.code === 0) {
      playerJoin(res.data.value.data, 1, position);
      toast.success({
        content: t('join_successfully'),
      });
    }
  },
  500,
  {
    leading: true,
    trailing: false,
  },
);

const cancelBattleDialog = () => {
  openConfirm({
    promptKey: 'cancelBattle',
    title: t('confirm_cancel'),
    content: t('cancel_battle_confirm'),
    confirmText: t('confirm'),
    onConfirm: () => {
      handleCancelBattle();
    },
  });
};

// 处理取消战斗
const handleCancelBattle = debounce(
  async () => {
    try {
      const res = await battleApi.cancelBattle({
        arena_id: battleInfo.value?.arena_id,
      });
      if (res?.data?.value.code === 0) {
        cancelBattle(battleInfo.value?.arena_id);
        toast.success({
          content: t('battle_cancel_success'),
        });
      }
    } catch (error) {}
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
);

const checkJoin = async () => {
  const res = await battleApi.checkJoin({
    arena_id: battleInfo.value.arena_id,
    total_price: `${battleInfo.value.battle_cost}`,
  });
  if (res.data.value.code === 0) {
    return true;
  }
  return false;
};

// 处理加入战斗
const joinBattleDialog = debounce(
  async (position: number) => {
    const canJoin = await checkJoin();
    if (!canJoin) {
      return;
    }
    openConfirm({
      promptKey: 'joinBattle',
      title: t('confirm_join'),
      content: t('join_battle_confirm', {
        a: battleInfo.value.battle_cost,
      }),
      onConfirm: () => {
        handleJoinBattle(position);
      },
    });
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
);

// 处理加入战斗
const handleJoinBattle = async (position: number) => {
  const res = await battleApi.joinBattle({
    arena_id: battleInfo.value?.arena_id,
    position,
    total_price: `${battleInfo.value.battle_cost}`,
  });
  if (res.data.value.code === 0) {
    playerJoin(res.data.value.data, 2, position);
    toast.success({
      content: t('join_successfully'),
    });
  }
};

// 离开战斗
const leaveBattleDialog = () => {
  openConfirm({
    promptKey: 'leaveBattle',
    title: t('confirm_exit'),
    content: t('exit_battle_confirm'),
    confirmText: t('confirm'),
    onConfirm: () => {
      handleLeaveBattle();
    },
  });
};

// 处理离开战斗
const handleLeaveBattle = async () => {
  const res = await battleApi.cancelBattle({
    arena_id: battleInfo.value?.arena_id,
  });
  if (res.data?.value.code === 0) {
    playerLeave({
      arena_id: battleInfo.value?.arena_id,
      players: { position: res?.data?.value?.data?.position },
    });
    toast.success({
      content: t('exit_successful'),
    });
  }
};

// const delayBattleEnd = (ms: any) => useTimeoutFn(ms, BATTLE_END_DELAY);

// 处理旋转结束
const handleSpinEnded = (index: number) => {
  spinnerConfig.value[index] = {
    ...spinnerConfig.value[index],
    isMuted: true,
    isSpinning: false,
    spinEnded: true,
  };

  // if (currentRound.value === battleInfo.value.battleCaseList.length - 1) {
  //   delayBattleEnd(() => {
  //     nextTick(() => {
  //       if (itemRef.value && !isScrollDisabled.value) {
  //         itemRef.value.scrollTo({
  //           top: itemRef.value.scrollHeight,
  //           behavior: 'smooth',
  //         });
  //       }
  //     });
  //   });
  // }
};

const handleTiebreakerSpinEnded = () => {
  if (!battleInfo.value.tiebreaker) return;

  const winnerPosition =
    battleInfo.value.tiebreaker.participantPositions[
      battleInfo.value.tiebreaker.result
    ] - 1;
  winnerPositions.value = [winnerPosition];
};

const handleTiebreakerNeeded = () => {
  showTiebreaker.value = !showTiebreaker.value;
};

const handleRecreateBattleBlur = () => {};

const handleRecreateOrModifyBattle = debounce(
  (mode: string) => {
    if (isLoading.value) return;

    if (!userInfo.value?.uid) {
      settingStore.signin();
      return;
    }
    isLoading.value = true;
    recreateMode.value = mode;
    cases.value = [];
    page.value = 1;
    fetchCases();
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
);

const fetchCases = async () => {
  try {
    const { data } = await casesApi.getCasesList({
      page: page.value,
      page_size: pageSize.value,
    });

    if (data.value?.code === 0) {
      const dataValue = data.value?.data;
      cases.value = [...cases.value, ...(dataValue.list || [])];
      if ((dataValue.total ?? 0) > page.value * pageSize.value) {
        page.value += 1;
        await fetchCases();
      } else {
        await recreateBattle();
      }
    }
  } finally {
    isLoading.value = false;
  }
};

const recreateBattle = async () => {
  if (
    battleInfo.value.cases.some(
      (battleCase: BattleCase) =>
        cases.value.findIndex((c) => c.slug === battleCase.slug) === -1,
    )
  ) {
    isLoading.value = false;
    return toast.error({
      content: t('please_select_the_cases_for_the_battle'),
    });
  }

  try {
    caseSelections.value = battleInfo.value.cases.map(
      (battleCase: BattleCase) => ({
        quantity: battleCase.qty,
        slug: battleCase.slug,
        case_name: battleCase.case_name,
        total_price: battleCase.total_price,
        image_url: battleCase.image_url,
        csort: battleCase.csort,
        category_id: battleCase.category_id,
        qyt: battleCase.qyt,
        is_putaway: battleCase.is_putaway,
      }),
    );
    currentMode.value = battleInfo.value.battle_mode;
    currentModePlayers.value = battleInfo.value.player_number;
    privateBattleEnabled.value = battleInfo.value.battle_mode;
    unoModeEnabled.value = battleInfo.value.game_mode;

    if (recreateMode.value === 'modify') {
      await router.push({
        path: '/case-battles/create',
      });
    } else if (recreateMode.value === 'recreate') {
      const res = await createBattle();
      reset();
      if (res && typeof res === 'object' && 'arena_id' in res) {
        router.push({
          path: '/case-battles/game/' + res.arena_id,
        });
      }
    }
  } finally {
    isLoading.value = false;
  }
};

const getBattleInfo = async () => {
  const res = await battleApi.getBattleInfo({
    arena_id: route.params.id as string,
  });
  if (res.data.value.code === 0) {
    setBattleInitialState(res.data.value.data);
  }
};
// 转换
const convert = async (
  list: BattleRecord[],
  onLoadingChange?: (loading: boolean) => void,
) => {
  onLoadingChange?.(true);
  const goods = list
    .map((e: BattleRecord) => {
      return {
        pawn_price: Number(e?.pawn_price ?? '0'),
        backpack_id: e?.backpack_id,
      };
    })
    .filter((e: any) => !disableConverts.value.includes(e.backpack_id));
  const { data: req } = await casesApi.convertToCoin(goods);
  if (req.value.code === 0) {
    toast.success({
      content: t('conversion_successful_obtain_a_coins', {
        a: goods.reduce((sum: number, item: any) => {
          return Number(BigNumberCalc.add(sum, Number(item.pawn_price)));
        }, 0),
      }),
    });
    disableConverts.value = [
      ...disableConverts.value,
      ...list.map((e: any) => e.backpack_id),
    ];
    battleInfo.value.battlePlayers.forEach((player: any) => {
      player.itemsData = player.itemsData.map((item: any) => {
        if (list.find((e: any) => e.backpack_id === item.backpack_id)) {
          return {
            ...item,
          };
        }
        return item;
      });
    });
  }
  onLoadingChange?.(false);
};

// 转换饰品
const handleConvertItem = debounce(
  (
    item: BattleRecord | BattleRecord[],
    price?: number,
    onLoadingChange?: (loading: boolean) => void,
  ) => {
    if (!userInfo.value?.uid) {
      settingStore.signin();
      return;
    }
    openConfirm({
      title: t('confirm_conversion'),
      content: t('skin_convert_confirmation', {
        a: price ?? (Array.isArray(item) ? 0 : (item.pawn_price ?? 0)),
      }),
      onConfirm: () => {
        convert(Array.isArray(item) ? item : [item], onLoadingChange);
      },
    });
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
);

const watchSocketTimeout = (time: number = 15000) => {
  socketTimer && clearTimeout(socketTimer);
  socketTimer = setTimeout(() => {
    retryGetBattleInfo();
  }, time);
};

const handleBattleInit = (data: any) => {
  if (!data.authenticated) {
    emit('join_battle', route.params.id);
  }
};

const handleBattleJoin = (data: any) => {
  playerJoin(data, data.players.is_bot, data.players.position);
  nextTick(() => {
    if (
      battleInfo.value.battlePlayers.filter((e) => e).length ===
      battleInfo.value.player_number
    ) {
      watchSocketTimeout(4000);
    }
  });
};

const handleBattleCancel = (data: any) => {
  cancelBattle(data.arena_id);
};

const handleBattleRunning = (data: any) => {
  clearTimeout(socketTimer);
  if (data?.eos) {
    setStatus(BattleStatusType.WAITING_FOR_EOS);
    setEosBlock({
      arena_id: data?.arena_id,
      ...data?.eos,
    });
  }
};

const handleBattleNewRound = (data: any) => {
  if (data.arena_id !== battleInfo.value?.arena_id) return;
  if (data.records.length) {
    pushNewRound({
      ...data,
      round: data.round,
      players: battleInfo.value.battlePlayers.map((player: any) => ({
        ...player,
        opened_item_id: data.records?.find(
          (record: any) => record.uid === player.uid,
        )?.goods_id,
      })),
    });
  } else {
    // 处理平局信息
    const tiebreakerData = generateTiebreakerData(
      data,
      battleInfo.value.battlePlayers,
    );
    if (tiebreakerData) {
      setTiebreaker(tiebreakerData);
    }
  }
  watchSocketTimeout();
};

const handleBattleLeave = (data: any) => {
  playerLeave(data);
};

const handleBattleFinish = (data: any) => {
  clearTimeout(socketTimer);
  finishBattle(data);
};

const { emit, reSubscribe } = useSocketListener('casebattle', {
  onInit: handleBattleInit,
  onJoin: handleBattleJoin,
  onCancel: handleBattleCancel,
  onRunning: handleBattleRunning,
  onNewRound: handleBattleNewRound,
  onLeave: handleBattleLeave,
  onFinish: handleBattleFinish,
  // onInit: handleBattleInit,
});

clearBattleInfo();
onMounted(() => {
  getBattleInfo();
});

onBeforeUnmount(() => {
  clearBattleInfo();
});
</script>
