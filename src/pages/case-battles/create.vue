<template>
  <div>
    <div
      class="mb-[30px] sm:mb-[51px] flex items-center gap-2 cursor-pointer font-bold text-purple-1"
      @click="back"
    >
      <svgo-back class="w-[6px] h-[8px] mb-0" />
      <div>{{ $t('back') }}</div>
    </div>
    <div>
      <div
        class="text-[20px] sm:text-[24px] leading-tight font-bold text-white"
      >
        {{ $t('create_battle') }}
      </div>
      <div class="mt-xl grid grid-cols-1 gap-4">
        <CaseBattlesCreateTab
          v-for="(battleType, index) in battleTypes"
          :key="index"
          :title="battleType.title"
          :tabs="battleType.tabs"
          :current-tab="currentTab"
          @change-tab="changeTab"
        />
      </div>
    </div>
    <div class="flex justify-between items-center my-xl font-bold">
      <div class="text-purple-1">
        <span>{{ $t('cases_selected') }}</span>
        <span class="ml-[6px] text-primary-400">{{ totalSelectedCases }}</span>
      </div>
      <div class="font-normal flex items-center">
        <span>{{ $t('battle_value') }}</span>
        <Currency
          :amount="totalValue"
          :size="'22px'"
          class="ml-[7px] font-bold"
        />
      </div>
    </div>
    <div
      class="cases-grid h-max grid grid-cols-[repeat(auto-fill,minmax(160px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))] gap-[6px]"
    >
      <CaseBattlesCaseCard
        v-for="item in caseSelections"
        :key="item.slug"
        :caseItem="item"
        :quantity="item.quantity"
        :selected="true"
        :show-close="true"
        @select="increment(item)"
        @decrement="decrement(item)"
        @increment="increment(item)"
        @update:quantity="(item, quantity) => updateQuantity(item, quantity)"
        @go-detail="goDetail"
      />
      <div
        class="h-[241px] sm:h-[295px] flex flex-col justify-center items-center cursor-pointer hover:bg-dark-1/40 border-dashed border-1 border-[#323A51] rounded-[8px]"
        @click="openAddCaseDialog"
      >
        <base-icon name="add" class="mb-[24px]" :fontControlled="false" />
        <div class="font-bold">
          {{ $t('add_cases') }}
        </div>
      </div>
    </div>
    <!-- 逆转模式 -->
    <div class="mt-[48px] bg-dark-3 rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <div
            class="flex items-center gap-2 pb-3"
            :class="[unoModeEnabled == 2 ? 'text-purple-2' : '']"
          >
            <base-icon name="uno-reverse" :filled="false" />
            <p class="font-bold">
              {{ $t('uno_reverse_mode') }}
            </p>
          </div>
          <p>
            {{ $t('reverse_luck_rule') }}
          </p>
        </div>
        <n-switch
          :rail-style="railStyle(true)"
          :default-value="unoModeEnabled"
          :checked-value="2"
          :unchecked-value="1"
          @update:value="(value: number) => (unoModeEnabled = value)"
        />
      </div>
    </div>
    <!-- 私人对战 -->
    <div class="mt-lg bg-dark-3 rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <div
            :class="[
              'flex items-center gap-2 pb-3',
              privateBattleEnabled == 2 ? 'text-[#FD4058]' : '',
            ]"
          >
            <base-icon
              name="lock"
              :filled="privateBattleEnabled == 2"
              class="w-[13px] h-[16px]"
            />
            <p class="font-bold">
              {{ $t('private_battle') }}
            </p>
          </div>
          <p>
            {{ $t('private_link_only') }}
          </p>
        </div>
        <n-switch
          :rail-style="railStyle()"
          :default-value="privateBattleEnabled"
          :checked-value="2"
          :unchecked-value="1"
          @update:value="(value: number) => (privateBattleEnabled = value)"
        />
      </div>
    </div>
    <div
      class="my-lg flex justify-end items-center max-md:flex-wrap max-md:justify-start gap-4"
    >
      <div class="font-normal flex items-center text-lg gap-md">
        <span>{{ $t('total_cost') }}</span>
        <Currency :amount="totalValue" />
      </div>
      <n-button
        class="w-auto btn-gradient-primary"
        type="primary"
        :loading="loading"
        :disabled="!totalSelectedCases"
        @click="handleCreateBattle"
      >
        {{ $t('create_battle') }}
      </n-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import CaseSelector from '@/components/caseBattles/CaseSelector.vue';
import { useBattleSelectedCasesStore } from '~/stores/modules/battleSelecteCases';
import { BattlePlayerModesType } from '~/types/battles';

const router = useRouter();
const battleTypes = [
  { title: 'standard_battle', tabs: ['1v1', '1v1v1', '1v1v1v1'] },
];
const currentTab = ref('1v1');
const dialog = useAppDialog();
const toast = useAppToast();
const { t } = useI18n();
const railStyle =
  (isUnoReverse: boolean = false) =>
  (params: { focused: boolean; checked: boolean }) => {
    return {
      backgroundColor: params.checked
        ? isUnoReverse
          ? '#8974FF'
          : '#FD4058'
        : '#323A51',
    };
  };

const battleSelecteCases = useBattleSelectedCasesStore();
const {
  caseSelections,
  totalValue,
  totalSelectedCases,
  privateBattleEnabled,
  unoModeEnabled,
  currentModePlayers,
} = storeToRefs(battleSelecteCases);
const { updateQuantity, increment, decrement, createBattle, reset } =
  battleSelecteCases;
const loading = ref(false);

onMounted(() => {
  if (currentModePlayers.value) {
    currentTab.value = BattlePlayerModesType[currentModePlayers.value];
  }
});

const back = () => {
  router.back();
};

const changeTab = (tab: string) => {
  currentTab.value = tab;
  currentModePlayers.value =
    BattlePlayerModesType[tab as keyof typeof BattlePlayerModesType];
};

const openAddCaseDialog = () => {
  dialog.open(CaseSelector, {
    style: {
      width: '978px',
    },
    title: t('standard_battle'),
    contentProps: {
      selectedCases: caseSelections.value,
    },
    onConfirm: (data) => {
      caseSelections.value = data;
      dialog.hideByKey();
    },
  });
};

const handleCreateBattle = async () => {
  loading.value = true;
  try {
    const res = await createBattle();
    if (res && typeof res === 'object') {
      toast.success({
        content: t('created_successfully'),
      });
      router.push(`/case-battles/game/${res?.arena_id}`);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
  } finally {
    loading.value = false;
  }
};

const goDetail = (id: string) => {
  window.open(`/cases/${id}`, '_blank');
};

onBeforeUnmount(() => {
  reset();
});
</script>
<style lang="scss" scoped></style>
