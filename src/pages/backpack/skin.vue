<template>
  <div class="flex-layout min-h-max">
    <FilterForm
      class="mt-lg flex justify-between items-center flex-wrap gap-7 max-sm:gap-2"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <template v-for="item in formSchema" :key="item.key">
        <FilterComponetsFormItem
          :class="{
            'w-[320px]': item?.key === 'search',
          }"
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          @update:model-value="handleUpdate(item.key, $event)"
        />
        <div v-if="item.key === 'search'" class="grow"></div>
      </template>
    </FilterForm>
    <div class="flex-layout justify-between min-h-max mt-lg gap-3">
      <BackpackCard
        :items="backpackListData"
        :loading="loading"
        :is-page-loading="isPageLoading"
        :action-loading="actionLoading"
        :show-reset-btn="false"
        @convert="handleConvert"
        @extract="handleExtract"
      />
      <div class="flex flex-wrap items-center justify-between">
        <BackpackSelectAllAction
          v-if="total"
          :checked="selectAllState.checked"
          :indeterminate="selectAllState.indeterminate"
          :disabled="selectAllState.disabled"
          :loading="selectAllState.loading"
          @update:checked="handleInventorySeleced"
          @convert="handleConvertAll"
        />
        <ListPaginator
          :total="total"
          :page-size="pageSize"
          :page="page"
          @update:page="handlePage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import pickBy from 'lodash-es/pickBy';
import { createFormSchema } from '~/models/backPackSkinModel';
import { useAppStore } from '~/stores/modules/app';
import { useBackpackStore } from '~/stores/modules/backpack';
import type { SkinItemWithGoods } from '~/types/backpack';

definePageMeta({
  layout: 'backpack',
  middleware: ['auth'],
});

const { formSchema, formState } = createFormSchema();
const { formatSteaminventoryList } = useFormatGoods();
const { openConfirm } = useDialogPromptsConfirm('backpackSkin');
const { y } = useWindowScroll();
const { t } = useI18n();
const { backpackApi } = useApi();
const router = useRouter();
const toast = useAppToast();
const dialog = useAppDialog();
const backpackStore = useBackpackStore();
const appStore = useAppStore();

const page = ref(1);
const pageSize = ref(24);
const total = ref(0);
const loading = ref(false);
const actionLoading = ref<string | null>(null);
const isPageLoading = ref(true);
const backpackListData = ref<SkinItemWithGoods[]>([]);
const selectAllState = reactive({
  checked: false,
  indeterminate: false,
  disabled: true,
  loading: false,
});

// 全选
const handleInventorySeleced = (checked: boolean) => {
  selectAllState.checked = checked;
  backpackListData.value.forEach((item: SkinItemWithGoods) => {
    item.selected = checked;
  });
};
watch(
  () => backpackListData.value,
  (newData: SkinItemWithGoods[]) => {
    if (backpackListData.value.length === 0) return;
    const every = newData.every((item) => item.selected);
    const some = newData.some((item) => item.selected);
    selectAllState.disabled = !some;
    selectAllState.checked = every;
    selectAllState.indeterminate = !every && some;
  },
  {
    immediate: true,
    deep: true,
  },
);
// 转换所有
const handleConvertAll = () => {
  const selectData = backpackListData.value.filter(
    (item: SkinItemWithGoods) => item.selected,
  );
  const goods = selectData.map((item: SkinItemWithGoods) => {
    return {
      backpack_id: item.backpack_id,
      pawn_price: Number(item.goods_info?.pawn_price),
    };
  });
  handleConvert({ goods }, true);
};

// 分页
const handlePage = (pageInfo: { page: number }) => {
  y.value = 0;
  page.value = pageInfo.page;
  getBackpackData();
};

const handleOperationSuccess = async () => {
  if (page.value > 1 && backpackListData.value.length === 1) {
    page.value--;
  }
  await getBackpackData();
  backpackStore.getBackpackStatistics();
  dialog.hideByKey();
};

const handleAllOperationSuccess = () => {
  page.value = 1;
  handleOperationSuccess();
};

// 提取
const extract = async (data: ApiV1BackpackExtractPostRequest) => {
  if (!checkLogin()) return;
  try {
    actionLoading.value = 'extract';
    const res = await backpackApi.extractGoods(data);
    if (res.data.value.code === 0) {
      toast.success({ content: t('extraction_sent') });
      handleOperationSuccess();
    }
  } finally {
    actionLoading.value = null;
  }
};

// 充值提示
const openRechargeTipDialog = (data: any) => {
  openConfirm({
    promptKey: 'exchange_recharge',
    enablePrompts: true,
    title: t('extraction_failed'),
    content: t('extraction_failed_description', {
      a: data.recharge_threshold,
      b: data.user_recharge_amount,
      c: data.gap_recharge_amount,
    }),
    confirmText: t('extraction_failed_btn'),
    onConfirm: () => {
      dialog.hideByKey();
      router.push('/profile/deposit');
    },
  });
};

const checkExtract = async (id: number) => {
  try {
    const res = await backpackApi.checkExtract({
      backpack_id: id,
    });
    if (res.data.value.code === 0) {
      if (Number(res.data.value.data?.gap_recharge_amount || 0) > 0) {
        openRechargeTipDialog(res.data.value.data);
        return false;
      }
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const handleExtract = async (data: ApiV1BackpackExtractPostRequest) => {
  if (!checkLogin()) return;
  actionLoading.value = 'extract';
  const canExtract = await checkExtract(data.backpack_id);
  actionLoading.value = null;

  if (!canExtract) return;

  openConfirm({
    promptKey: 'extract',
    title: t('confirm_extraction'),
    content: t('check_steam_connection'),
    onConfirm: () => extract(data),
  });
};

// 转换
const convert = async (data: ApiV1BackpackPawnPostRequest) => {
  try {
    actionLoading.value = 'convert';
    const res = await backpackApi.pawnGoods(data);
    if (res.data.value.code === 0) {
      const successPrice = res.data.value.data.success_price;
      toast.success({
        content: t('conversion_successful_obtain_a_coins', {
          a: successPrice,
        }),
      });
      await handleOperationSuccess();
    }
  } finally {
    actionLoading.value = null;
  }
};
// 转换所有
const convertAll = async (data: ApiV1BackpackPawnPostRequest) => {
  try {
    selectAllState.loading = true;
    const res = await backpackApi.pawnGoods(data);
    if (res.data.value.code === 0) {
      const goodsTotal = data.goods.length;
      const successNum = res.data.value.data.success_num;
      const successPrice = res.data.value.data.success_price;
      let content = '';
      // 部分转换成功
      if (goodsTotal > successNum) {
        const errorNum = goodsTotal - successNum;
        content = t('partial_convert_success', {
          a: successPrice,
          x: goodsTotal,
          s: errorNum,
        });
      } else {
        content = t('all_convert_success', {
          a: successPrice,
          x: goodsTotal,
        });
      }
      openConfirm({
        title: t('conversion_results'),
        content,
        enablePrompts: true,
        hideCancelBtn: true,
        onConfirm: () => handleAllOperationSuccess(),
        onClose: () => handleAllOperationSuccess(),
        onEsc: () => handleAllOperationSuccess(),
      });
    }
  } finally {
    selectAllState.loading = false;
  }
};

const handleConvert = (data: ApiV1BackpackPawnPostRequest, isAll = false) => {
  if (!checkLogin()) return;
  if (!data.goods || data.goods.length === 0) {
    return;
  }
  const pawnPrice = data.goods.reduce((sum: number, item: any) => {
    return Number(BigNumberCalc.add(sum, Number(item.pawn_price)));
  }, 0);
  const goodsTotal = data.goods.length;
  if (isAll) {
    openConfirm({
      title: t('confirm_all_conversions'),
      enablePrompts: true,
      content: t('all_convert_confirmation', {
        a: pawnPrice,
        x: goodsTotal,
      }),
      onConfirm: () => convertAll(data),
    });
    return;
  }
  openConfirm({
    promptKey: 'convert',
    title: t('confirm_conversion'),
    content: t('skin_convert_confirmation', {
      a: pawnPrice,
    }),
    onConfirm: () => convert(data),
  });
};

// 获取饰品接口筛选参数
const getDataParams = () => {
  const params = {
    page: page.value,
    page_size: pageSize.value,
    ...formState,
  };
  return pickBy(params, (e) => e !== null);
};

const getBackpackData = async () => {
  try {
    loading.value = true;
    const params = getDataParams();
    const res = await backpackApi.getBackpackList(params);
    if (res.data.value.code === 0) {
      backpackListData.value = formatSteaminventoryList(res.data.value.data);
      total.value = res.data.value.data.total || 0;
      page.value = res.data.value.data.page || 1;
    }
  } catch (error) {
  } finally {
    loading.value = false;
    isPageLoading.value = false;
  }
};

const refreshData = async () => {
  await getBackpackData();
  backpackStore.getBackpackStatistics();
  appStore.getMetadata();
};

const { handleUpdate } = useFormUpdate(formState, () => {
  page.value = 1;
  refreshData();
});

onMounted(() => {
  refreshData();
});
</script>
