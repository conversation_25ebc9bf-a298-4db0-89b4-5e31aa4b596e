<template>
  <div>
    <FilterComponetsFormItem
      class="relative sm:absolute mt-md sm:mt-0 top-0 right-0 w-auto sm:w-[338px]"
      :field="formSchema.search"
      :field-name="'search'"
      :horizontal="false"
      :model-value="formState.search"
      @update:model-value="(value: string) => handleUpdate('search', value)"
    />
    <div class="mt-lg">
      <BackpackTable
        :columns="columns"
        :data="tableData"
        :bordered="false"
        row-class-name="bg-[#151A29] "
        :pagination="pagination"
        :emptytext="emptyTableText"
        :loading="tableLoading"
        @update:page="updatePage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { createFormSchema, createColumns } from '~/models/extractRecordModel';
import { useAppStore } from '~/stores/modules/app';
import { useBackpackStore } from '~/stores/modules/backpack';
import type { ExtractItemWithGoods } from '~/types/backpack';

definePageMeta({
  layout: 'backpack-extract',
  middleware: ['auth'],
});

const { backpackApi } = useApi();
const appStore = useAppStore();
const { formatSteaminventoryList } = useFormatGoods();
const backpackStore = useBackpackStore();

const columns = ref<any[]>([]);
const { formSchema, formState } = createFormSchema();
const tableData = ref<ExtractItemWithGoods[] | undefined>([]);
const pagination = reactive<any>({
  pageSize: 10,
  page: 1,
  total: 0,
});
const tableLoading = ref(true);

const orderListParams = computed(() => {
  const params = {};
  return params;
});

/** 暂无数据文案 */
const emptyTableText = computed(() => {
  if (Object.keys(orderListParams.value).length) {
    return 'no_record_found';
  }
  return 'no_record_found';
});

const updatePage = (updateData: any) => {
  pagination.page = updateData.page;
  refreshData();
};

// 提取纪录-提取中列表
const getTableData = async () => {
  tableLoading.value = true;
  try {
    const res = await backpackApi.getExtractProgress({
      page: pagination.page,
      page_size: pagination.pageSize,
      list_type: 2,
      search: formState.search,
    });
    if (res.data.value.code === 0) {
      tableData.value = formatSteaminventoryList(res.data.value.data);
      pagination.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

const refreshData = async () => {
  await getTableData();
  backpackStore.getBackpackStatistics();
  appStore.getMetadata();
};

const { handleUpdate } = useFormUpdate(formState, () => {
  pagination.page = 1;
  refreshData();
});

onMounted(() => {
  columns.value = createColumns();
  refreshData();
});
</script>
