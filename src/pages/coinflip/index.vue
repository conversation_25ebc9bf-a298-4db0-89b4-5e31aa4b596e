<template>
  <div>
    <!-- create game -->
    <CoinflipCreateGame class="mb-[15px] sm:mb-[35px]" />
    <!-- 我的游戏 -->
    <div v-if="userInfo?.uid" class="mb-10 relative">
      <h2 class="text-[20px] sm:text-[24px] font-bold absolute leading-[43px]">
        {{ $t('my_games') }}
        <span class="text-theme-color ml-2 sm:ml-4">{{ myListTotal }}</span>
      </h2>
      <n-tabs
        :key="currentLang"
        animated
        justify-content="end"
        size="medium"
        class="w-full"
        tabClass="font-bold"
        :value="myGameTab"
        :theme-overrides="themeOverrides"
        @update:value="changeMyGameTab"
      >
        <n-tab-pane
          :tab="$t('current')"
          name="current"
          class="min-h-[192px] max-sm:pt-0"
        >
          <CoinflipCardModule
            :list="coinflipStore.myList"
            :moduleInfo="modules.my"
            :pageLoading="pageLoading"
            moduleId="my"
            @final-game="finalGame"
          />
        </n-tab-pane>
        <n-tab-pane :tab="$t('history')" name="history" class="min-h-[192px]">
          <CoinflipCardModule
            :list="coinflipStore.historyList"
            :moduleInfo="modules.history"
            :pageLoading="pageLoading"
            moduleId="history"
            @update:page="handlePage"
          />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 开放游戏 -->
    <div class="flex items-center mb-6 flex-wrap gap-y-3">
      <h2 class="text-[20px] sm:text-[24px] font-bold leading-[33px] grow">
        {{ $t('open_games') }}
        <span class="text-theme-color ml-2 sm:ml-4">{{ openListTotal }}</span>
      </h2>

      <CoinflipFilter :filter-show="filterShow" @update-filter="changeFilter" />
      <div class="flex items-center">
        <div
          class="flex items-center lg:hidden text-[#7D90CA]"
          :class="{ 'text-theme-color': filterShow }"
          @click="filterShow = !filterShow"
        >
          <BaseIcon name="info-outlined" class="mr-2"></BaseIcon>
          {{ $t('filters') }}
        </div>
        <SoundSwitch class="ml-6 max-sm:ml-3" />
      </div>
    </div>
    <CoinflipCardModule
      :list="coinflipStore.openList"
      :moduleInfo="modules.open"
      :pageLoading="pageLoading"
      moduleId="open"
      @final-game="finalGame"
    />
  </div>
</template>
<script lang="ts" setup>
import type { TabsProps } from 'naive-ui';
import { useCoinflipStore } from '~/stores/modules/coinflip';
import { useSettingStore } from '~/stores/modules/setting';
import { useAppStore } from '~/stores/modules/app';
import type { RoomInfoType } from '~/types/coinflip';
type TabOverrides = NonNullable<TabsProps['themeOverrides']>;
const coinflipStore = useCoinflipStore();
const settingStore = useSettingStore();
const filterShow = ref(false);
const myGameTab = ref('current');
// 页面Loading
const pageLoading = ref(true);
const appStore = useAppStore();

const currentLang = computed(() => appStore.currentLang);
const modules = computed(() => coinflipStore.modules);
const userInfo = computed(() => settingStore.userInfo);
const themeOverrides: TabOverrides = {
  tabTextColorActiveBar: '#fff',
  tabTextColorBar: '#7D90CA',
  panePaddingMedium: '16px 0 0 ',
  tabFontWeight: '600',
};
const myListTotal = computed(() => coinflipStore.myList.length);
const openListTotal = computed(() => coinflipStore.modules.open.total);

const openParams = computed(() => {
  const filter = coinflipStore.formFilter.formState;
  const minmax = filter.price_range.split(',');
  const params = {
    page_size: filter.page_size,
    price_min: Number(minmax[0]),
    price_max: Number(minmax[1]),
    amount_key_desc: filter.amount_key_desc,
  };
  return params;
});
const { y } = useWindowScroll();
// 页码切换
const handlePage = async (pageInfo: { page: number }) => {
  coinflipStore.modules.history.page = pageInfo.page;
  await coinflipStore.getHistoryList();
  y.value = 0;
};
// 初始化
const init = () => {
  coinflipStore.getOpenList(openParams.value).finally(() => {
    if (!modules.value.my.loading) pageLoading.value = false;
  });
  coinflipStore.getMyList().finally(() => {
    if (!modules.value.open.loading) pageLoading.value = false;
  });
};
init();

// my game tab切换
const changeMyGameTab = (tab: string) => {
  if (tab !== myGameTab.value) {
    myGameTab.value = tab;
    if (tab === 'current') {
      coinflipStore.getMyList();
    } else {
      coinflipStore.getHistoryList();
    }
  }
};
// open game 过滤器
const changeFilter = async (
  _data: any,
  key: string,
  preVal: number | string,
  curVal: number | string,
) => {
  if (key === 'page_size') {
    coinflipStore.changePageSize(curVal as number, preVal as number);
  } else {
    await coinflipStore.getOpenList(openParams.value);
  }
};
// 抛硬币结束
const finalGame = (item: RoomInfoType) => {
  const uid = userInfo.value?.uid;
  // 当前结束的是自己的游戏,并且当前在历史,刷新历史数据
  const isSelfJoin = item.creator_uid === uid || item.challenger_uid === uid;
  if (isSelfJoin || myGameTab.value === 'history') {
    coinflipStore.getHistoryList();
  }
};

// socket战报监听
useSocketListener('coinflip', {
  onCreate: coinflipStore.socketCreate,
  onResult: coinflipStore.socketResult,
  onCancel: coinflipStore.socketCancel,
  onChallenge: coinflipStore.socketChallenge,
});
</script>
<style lang="scss" scoped>
:deep(.n-tabs.n-tabs--flex .n-tabs-nav) {
  margin-bottom: 14px;
}
</style>
