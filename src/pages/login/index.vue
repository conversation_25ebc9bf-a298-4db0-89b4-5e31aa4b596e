<template>
  <div></div>
</template>
<script lang="ts" setup>
const router = useRouter();
onBeforeMount(async () => {
  const deviceId = await getDeviceId.getFinger();
  const breakpoints = useBreakpoints({
    mobile: 0,
    tablet: 640,
    laptop: 1024,
    desktop: 1280,
  });
  const activeBreakpoint = breakpoints.active();
  if (window) {
    const inviteCode = router.currentRoute.value.query?.invite_code;
    const platform = router.currentRoute.value.query?.platform;
    const event = router.currentRoute.value.query?.event;
    const uid = router.currentRoute.value.query?.uid;
    const url = new URL('/api/v1/login/steam/', location.origin);
    url.searchParams.set('back_url', '%2Flogin');
    url.searchParams.set('device_id', deviceId);
    if (inviteCode) {
      url.searchParams.set('invite_code', inviteCode as string);
    }
    if (platform) {
      url.searchParams.set('platform', platform as string);
    }
    if (event) {
      url.searchParams.set('event', event as string);
    }
    if (uid) {
      url.searchParams.set('uid', uid as string);
    }
    window.location.replace(url.toString());
    const loginState = router.currentRoute.value.query?.r;
    const bindState = router.currentRoute.value.query?.bind;
    if (bindState) {
      localStorage.setItem('bind_status', bindState as string);
    }
    if (loginState || bindState) {
      if (
        activeBreakpoint.value === 'mobile' ||
        activeBreakpoint.value === 'tablet'
      ) {
        window.location.href = location.origin;
      } else {
        window.close();
      }
    }
  }
});
definePageMeta({
  layout: 'empty',
});
onMounted(() => {
  const { $CC } = useNuxtApp();
  $CC.hide();
});
</script>
<style lang="scss" scoped>
body {
  background-color: #000;
}
</style>
