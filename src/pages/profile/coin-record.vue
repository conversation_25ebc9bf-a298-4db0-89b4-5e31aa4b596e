<template>
  <div class="w-full">
    <FilterForm
      class="mt-lg flex justify-between items-center flex-wrap gap-7 max-md:gap-3"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <template v-for="item in formSchema" :key="item.key">
        <FilterComponetsFormItem
          :class="{
            'w-[300px]': item?.key === 'search',
          }"
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          @update:model-value="handleUpdate(item.key, $event)"
        />
        <div v-if="item.key === 'search'" class="grow"></div>
      </template>
    </FilterForm>
    <div class="bg-[#151A29] mt-lg rounded-md">
      <Table
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :pagination="pagination"
        :emptytext="emptyTableText"
        :loading="tableLoading"
        :theme-overrides-cover="{
          thColor: '#20263B',
          thTextColor: '#fff',
        }"
        @update:page="updatePage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { createColumns, createFormSchema } from '~/models/coinRecordModel';

definePageMeta({
  layout: 'coin-purse',
  middleware: ['auth'],
});

const { depositApi } = useApi();
const { formSchema, formState } = createFormSchema();
const columns = ref<any[]>([]);
const tableData = ref<any[]>([]);
const tableLoading = ref(true);
const emptyTableText = ref('no_record_found');
const pagination = reactive<any>({
  pageSize: 10,
  page: 1,
  total: 0,
});

const updatePage = (updateData: any) => {
  pagination.page = updateData.page;
  getTableData();
};

const getTableData = async () => {
  tableLoading.value = true;
  try {
    const res = await depositApi.getTransactions({
      page: pagination.page,
      ...formState,
    });
    if (res.data.value.code === 0) {
      tableData.value = res.data.value.data.list;
      pagination.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

const { handleUpdate } = useFormUpdate(formState, () => {
  pagination.page = 1;
  getTableData();
});

onMounted(() => {
  columns.value = createColumns();
  getTableData();
});
</script>
