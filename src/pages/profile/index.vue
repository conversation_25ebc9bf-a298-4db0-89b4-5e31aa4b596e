<template>
  <div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3">
      <p class="text-white font-bold mb-[14px]">{{ t('user_info') }}</p>
      <div class="row-info">
        <div class="col-name">{{ t('avatar', 'Avatar') }}</div>
        <div class="col-content">
          <n-avatar :size="42" :src="userInfo?.user_avatar" />
        </div>
        <MainButton
          type="tertiary"
          class="w-[180px] uppercase"
          @click="handleAvatarEdit"
        >
          {{ $t('change') }}
        </MainButton>
      </div>
      <div class="row-info">
        <div class="col-name">{{ t('username') }}</div>
        <div class="col-content text-white">{{ userInfo?.user_name }}</div>
        <MainButton
          type="tertiary"
          class="w-[180px] uppercase"
          @click="handleUsernameEdit"
        >
          {{ $t('change') }}
        </MainButton>
      </div>
    </div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3 mt-2">
      <p class="text-white font-bold mb-[14px]">
        {{ t('account_security', 'Account Security') }}
      </p>
      <div class="row-info">
        <div class="col-name">
          <BaseIcon name="profileEmail"></BaseIcon>
          {{ t('email') }}
        </div>
        <div
          class="col-content"
          :class="userInfo?.email ? 'text-white' : 'text-[#5D688A]'"
        >
          {{
            userInfo?.email
              ? formatEmail(userInfo?.email)
              : $t('bind_email_login', 'Bind your email to enable email login')
          }}
        </div>
        <MainButton
          v-if="!userInfo?.email"
          type="tertiary"
          class="w-[180px] uppercase"
          @click="handleSendEmailDialog"
        >
          {{ $t('bind_email') }}
        </MainButton>
      </div>

      <div class="row-info">
        <div class="col-name">
          <BaseIcon name="profilePassword"></BaseIcon>
          {{ t('password', 'Password') }}
        </div>
        <div
          class="col-content"
          :class="userInfo?.is_set_password ? 'text-white' : 'text-[#5D688A]'"
        >
          {{
            userInfo?.is_set_password
              ? $t('configured', 'Configured')
              : $t('not_set', 'Not set')
          }}
        </div>
        <MainButton
          type="tertiary"
          class="w-[180px] uppercase"
          @click="handleSetPasswordDialog"
        >
          {{
            userInfo?.is_set_password
              ? $t('reset_password', 'Reset password')
              : $t('set_password', 'Set password')
          }}
        </MainButton>
      </div>

      <div class="row-info">
        <div class="col-name">
          <BaseIcon name="profileSteam"></BaseIcon>
          {{ t('connect_steam', 'Connect Steam') }}
        </div>
        <div
          class="col-content"
          :class="userInfo?.steam64_id ? 'text-white' : 'text-[#5D688A]'"
        >
          {{
            userInfo?.steam64_id
              ? userInfo?.steam64_id
              : $t('not_linked', 'Not linked')
          }}
        </div>
        <MainButton
          type="tertiary"
          class="w-[180px] uppercase"
          @click="handleConnectSteam"
        >
          {{
            userInfo?.steam64_id
              ? $t('view_steam', 'View Steam')
              : $t('go_to_link', 'Go to Link')
          }}
        </MainButton>
      </div>

      <div class="row-info">
        <div class="col-name">
          <BaseIcon name="profileLink"></BaseIcon>
          {{ t('steam_trade_link') }}
        </div>
        <ProfileInput
          v-model:value="tradeUrl"
          :placeholder="t('enter_your_link')"
        >
          <template #suffix>
            <n-button text class="text-[#F8B838]" @click="handleGetTradeUrl">
              {{ t('get_link') }}
            </n-button>
          </template>
        </ProfileInput>
        <MainButton
          type="tertiary"
          class="w-[180px] shrink-0 uppercase"
          @click="handleApplyTradeUrl"
        >
          {{ t('apply') }}
        </MainButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgoEditUser from '~/assets/icons/edit-user.svg';
import { DOMAIN_CONFIG } from '~/constants';
import { useSettingStore } from '~/stores/modules/setting';
import Confirm from '~/components/dialog/Confirm.vue';
definePageMeta({
  layout: 'profile',
  middleware: ['auth'],
});

const { t } = useI18n();
const settingStore = useSettingStore();
const { settingApi } = useApi();
const toast = useAppToast();
const dialog = useAppDialog();
const tradeUrl = ref('');
const userInfo = computed(() => settingStore.userInfo);

watch(
  userInfo,
  () => {
    tradeUrl.value = userInfo.value?.trade_url || '';
  },
  { immediate: true },
);
const { openEditDialog, openAvatarDialog } = useProfileEditDialog();

const handleAvatarEdit = () => {
  openAvatarDialog({
    defaultValue: userInfo.value?.user_avatar,
    width: '436px',
    style: {
      padding: '20px',
    },
  });
};

const handleUsernameEdit = () => {
  const regUserName = /^[a-zA-Z0-9._-]{1,30}$/;
  openEditDialog({
    title: t('change_username'),
    description: t('username_rule'),
    inputPlaceholder: t('please_enter_your_username'),
    defaultValue: userInfo.value?.user_name,
    maxlength: 30,
    onInput: (value: string, callback: (newValue: string) => void) => {
      if (!regUserName.test(value)) {
        callback(value.replace(/[^a-zA-Z0-9._-]/g, ''));
      }
    },
    validateFn: (value?: string) => {
      if (!value) {
        toast.error({ content: t('please_enter_your_new_nickname') });
        return false;
      }
      return true;
    },
    submitFn: (username: string) => {
      return settingApi.userNameUpdate({
        username,
      });
    },
    onSubmit: () => {
      toast.success({ content: t('application_successful') });
      settingStore.getUserInfo();
      dialog.hideByKey();
    },
    width: '500px',
    style: {
      padding: '20px',
    },
    icon: h(SvgoEditUser, { class: 'w-[50px] h-[50px]' }),
  });
};

const handleGetTradeUrl = () => {
  window.open(
    DOMAIN_CONFIG.TRADE_URL(userInfo.value?.steam64_id ?? ''),
    '_blank',
  );
};

const passwordDialogRef = ref();
const handleSetPasswordDialog = () => {
  if (userInfo.value?.is_set_password === 1 && userInfo.value.email) {
    passwordDialogRef.value = dialog.open(Confirm, {
      class: 'max-w-[90%] xl:max-w-5xl rounded-xl bg-[#1B1E23]',
      contentProps: {
        title: t('reset_password_prompt', 'Password reset prompt'),
        content: t(
          'reset_password_dialog_content',
          'Your password has been reset successfully. Please log in again to continue. Confirm to proceed?',
        ),
        onConfirm: () => {
          passwordDialogRef.value?.destroy();
          settingStore.openResetPasswordModal();
        },
      },
    });
  } else {
    settingStore.openBindEmailModal();
  }
};

const handleConnectSteam = () => {
  if (userInfo.value?.steam64_id) {
    // 新建标签页打开对应steam用户的主页
    window.open(
      DOMAIN_CONFIG.STEAM_USER_PROFILE(userInfo.value?.steam64_id ?? ''),
      '_blank',
    );
  } else {
    // 显示Steam授权登录弹窗
    settingStore.signin({
      event: 'bind',
    });
  }
};

const handleSendEmailDialog = () => {
  settingStore.openBindEmailModal();
};

const handleApplyTradeUrl = async () => {
  if (!tradeUrl.value) {
    return;
  }
  try {
    const res = await settingApi.setTradeUrl({ trade_url: tradeUrl.value });
    if (res.data.value.code === 0) {
      toast.success({
        content: t('application_successful'),
      });
      settingStore.getUserInfo();
    }
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  settingStore.getUserInfo();
});
</script>

<style lang="scss" scoped>
.row-info {
  @apply text-[#7D90CA] font-bold flex items-center py-[10px] gap-2 max-sm:flex-wrap;
}
.col-name {
  @apply w-[150px] shrink-0 flex items-center gap-x-2;
}
.col-content {
  @apply grow min-w-[150px];
}
</style>
