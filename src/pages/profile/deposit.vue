<template>
  <div>
    <div class="pb-[24px]">
      <!-- 加密货币 -->
      <div v-if="cryptoMethods.length">
        <div class="w-full flex items-center justify-between">
          <Heading size="sm" class="mb-lg mt-lg text-[#7D90CA] font-bold">
            {{ $t('crypto') }}
          </Heading>
        </div>
        <div
          class="grid grid-cols-[repeat(auto-fill,minmax(174px,1fr))] gap-md"
        >
          <div
            v-for="(method, methodIndex) in cryptoMethods"
            :key="methodIndex"
          >
            <DepositMethodCard
              :method="method"
              :country="selectedCountry"
              @select="(method) => handleOpenCryptoDialog(method)"
            />
          </div>
        </div>
      </div>
      <!-- 银行卡 -->
      <div>
        <div class="w-full flex items-center justify-between">
          <Heading size="sm" class="mb-lg mt-lg text-[#7D90CA] font-bold">
            {{ $t('bank_deposit') }}
          </Heading>
        </div>
        <div
          class="grid grid-cols-[repeat(auto-fill,minmax(174px,1fr))] gap-md"
        >
          <div v-for="(method, methodIndex) in g2aMethods" :key="methodIndex">
            <DepositMethodCard
              :method="method"
              @select="(method) => handleDepositDialog(method)"
            />
          </div>
        </div>
      </div>
      <!-- 礼品卡 -->
      <div v-if="giftCardMethods.length">
        <div class="w-full flex items-center justify-between">
          <Heading size="sm" class="mb-lg mt-lg text-[#7D90CA] font-bold">
            {{ $t('gift_card') }}
          </Heading>
        </div>
        <div
          class="grid grid-cols-[repeat(auto-fill,minmax(174px,1fr))] gap-md"
        >
          <div
            v-for="(method, methodIndex) in giftCardMethods"
            :key="methodIndex"
          >
            <DepositMethodCard
              :method="method"
              :country="selectedCountry"
              @select="(method) => handleOpenGiftDialog(method)"
            />
          </div>
        </div>
      </div>
    </div>
    <Teleport v-if="isMounted" to="#coin-purse-footer">
      <div class="bg-[#151A29] mt-2 px-lg max-sm:px-3 rounded-lg">
        <div class="pt-[20px] mb-[30px]">{{ $t('deposit_record') }}</div>
        <Table
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :pagination="pagination"
          :emptytext="emptyTableText"
          :loading="tableLoading"
          :theme-overrides-cover="{
            thColor: '#20263B',
            thTextColor: '#fff',
          }"
          @update:page="updatePage"
        />
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import DepositCryptoCurrencyModal from '~/components/dialog/deposit/CryptoCurrency.vue';
import DepositCurrencyModal from '~/components/dialog/deposit/Currency.vue';
import DepositGitCardModal from '~/components/dialog/deposit/Gift.vue';
import DepositVisaMasterRechargeModal from '~/components/dialog/deposit/VisaMasterRecharge.vue';
import ProviderSelector from '~/components/dialog/deposit/Provider.vue';
import { createColumns } from '~/models/depositModel';
import { useDepositStore } from '~/stores/modules/deposit';

definePageMeta({
  layout: 'coin-purse',
  middleware: ['auth'],
});

const { depositApi } = useApi();
const depositStore = useDepositStore();
const { getPaymentMethodsForCountry } = storeToRefs(depositStore);
const dialog = useAppDialog();
const columns = ref<any[]>([]);
const isMounted = ref(false);
const tableData = ref<ApiV1TradeDepositRecordsGet200ResponseDataItemsInner[]>(
  [],
);
const tableLoading = ref(true);
const emptyTableText = ref('no_record_found');
const pagination = reactive<any>({
  pageSize: 10,
  page: 1,
  total: 0,
});
const selectedCountry = ref('');
const selectedProvider = ref<any>(null);

const cryptoMethods = computed(() => {
  return depositStore.cryptoMethods;
});

const giftCardMethods = computed(() => {
  return depositStore.giftCardMethods;
});

const g2aMethods = computed(() => {
  return getPaymentMethodsForCountry.value;
});

const updatePage = (updateData: any) => {
  pagination.page = updateData.page;
  getTableData();
};

// 加密货币弹窗
const handleOpenCryptoDialog = (
  method: ApiBffInterfaceV1PaymentMethodsData,
) => {
  depositStore.fetchCryptoExchangeRateData();
  depositStore.fetchCryptoDepositData(method.key ?? '');
  dialog.open(DepositCryptoCurrencyModal, {
    style: {
      width: '880px',
    },
    contentProps: {
      method,
    },
  });
};

const handleDepositDialog = (method: ApiBffInterfaceV1PaymentMethodsData) => {
  if (method?.configurations?.length === 1) {
    if ((method?.configurations[0] as any)?.mode === 2) {
      handleOpenVisaMasterRechargeDialog(method, method.configurations[0]);
    } else {
      handleOpenBankDialog(method, method.configurations[0]);
    }
    return;
  }
  openProviderDialog(method);
};

// 支付渠道选择
const openProviderDialog = (method: ApiBffInterfaceV1PaymentMethodsData) => {
  dialog.open(ProviderSelector, {
    style: {
      width: '568px',
    },
    contentProps: {
      method,
    },
    onConfirm: (
      provider: ApiBffInterfaceV1PaymentMethodsDataProvider & {
        configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration;
        mode?: number;
      },
    ) => {
      selectedProvider.value = provider;
      dialog.hideByKey();
      if (provider?.mode === 2) {
        handleOpenVisaMasterRechargeDialog(method, provider.configuration);
      } else {
        handleOpenBankDialog(method, provider.configuration);
      }
    },
  });
};
// 法币充值弹窗
const handleOpenBankDialog = (
  method: ApiBffInterfaceV1PaymentMethodsData,
  configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration,
) => {
  const freeConfig = depositStore.getFee({
    configId: configuration.id,
    type: method.type,
  });
  dialog.open(DepositCurrencyModal, {
    style: {
      width: '880px',
    },
    contentProps: {
      method,
      configuration,
      freeConfig,
    },
    onConfirm: (result: boolean) => {
      if (result) {
        getTableData();
      }
    },
  });
};

// 礼品卡充值
const handleOpenGiftDialog = (method: ApiBffInterfaceV1PaymentMethodsData) => {
  dialog.open(DepositGitCardModal, {
    style: {
      width: '880px',
    },
    contentProps: {
      method,
    },
    onConfirm: (result: boolean) => {
      if (result) {
        getTableData();
      }
    },
  });
};

// visa/master充值
const handleOpenVisaMasterRechargeDialog = (
  method: ApiBffInterfaceV1PaymentMethodsData,
  configuration: ApiBffInterfaceV1PaymentMethodsDataConfiguration,
) => {
  const freeConfig = depositStore.getFee({
    configId: configuration.id,
    type: method.type,
  });

  dialog.open(DepositVisaMasterRechargeModal, {
    style: {
      width: '1100px',
    },
    contentProps: {
      method,
      configuration,
      freeConfig,
    },
    onConfirm: (result: boolean) => {
      if (result) {
        getTableData();
      }
    },
  });
};

const getTableData = async () => {
  tableLoading.value = true;
  try {
    const res = await depositApi.getDepositRecords({
      page: pagination.page,
    });
    if (res.data.value.code === 0) {
      tableData.value = res.data.value.data.items;
      pagination.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

onMounted(() => {
  nextTick(() => {
    isMounted.value = true;
  });
  depositStore.fetchPaymentMethods();
  columns.value = createColumns();
  getTableData();
});
</script>
