<template>
  <div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3">
      <div class="flex justify-between items-center">
        <div class="font-bold text-[#fff] leading-none">
          {{ t('my_level') }}
        </div>
      </div>
      <div class="w-full pt-6">
        <div
          class="mb-[16px] flex justify-between leading-none flex-wrap gap-2"
        >
          <div class="text-white flex gap-3 items-center">
            {{ $t('current_level') }}
            <UserLevel :level="userInfo?.level_info?.now_level_id" />
          </div>
          <div
            class="text-white max-sm:order-last max-sm:w-full max-sm:text-center"
          >
            {{ levelProgression.exp }}/{{ levelProgression.step }} ({{
              userInfo?.level_info?.percent
            }}%)
          </div>
          <div
            v-if="
              userInfo?.level_info?.next_level_id !==
              userInfo?.level_info?.now_level_id
            "
            class="text-white flex gap-3 items-center"
          >
            {{ $t('next_level') }}
            <UserLevel :level="userInfo?.level_info?.next_level_id" />
          </div>
        </div>
        <n-progress
          type="line"
          :show-indicator="false"
          color="#F8B838"
          rail-color="#28314A"
          :percentage="userInfo?.level_info?.percent"
        />
      </div>
    </div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3 mt-lg">
      <div class="font-bold text-[#fff] leading-none">
        {{ t('bonus_cases') }}
      </div>
      <div
        class="pt-[16px] grid grid-cols-[repeat(auto-fill,minmax(140px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(222px,1fr))] gap-2"
      >
        <BonusCasesCard
          v-for="item in bonusCases"
          :key="item.slug"
          :item="item"
          @open-case="openCase"
        />
      </div>
    </div>
    <div class="bg-[#151A29] rounded-lg px-lg max-sm:px-3 mt-lg">
      <div
        class="flex flex-wrap gap-2 justify-between items-center leading-none pt-[20px] mb-[30px]"
      >
        <div class="text-lg font-bold text-white">
          {{ t('xp_breakdown') }}
        </div>
        <div class="text-[#7D90CA]">
          {{ t('xp_breakdown_description') }}
        </div>
      </div>
      <div>
        <Table
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :pagination="pagination"
          :emptytext="emptyTableText"
          :loading="tableLoading"
          :theme-overrides-cover="{
            thColor: '#20263B',
            thTextColor: '#fff',
          }"
          @update:page="updatePage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { createColumns } from '~/models/bonusCasesModel';
import { useAppStore } from '~/stores/modules/app';
import { useSettingStore } from '~/stores/modules/setting';

definePageMeta({
  layout: 'profile',
  middleware: ['auth'],
});
const { t } = useI18n();
const { casesApi } = useApi();
const router = useRouter();
const columns = ref<any[]>([]);
const bonusCases = ref<V1CasesItem[]>([]);
const tableLoading = ref(true);
const emptyTableText = ref('no_record_found');
const pagination = reactive<any>({
  pageSize: 10,
  page: 1,
  total: 0,
});
const settingStore = useSettingStore();
const appStore = useAppStore();
const userInfo = computed<ApiV1UserInfoGet200ResponseData | null | undefined>(
  () => settingStore.userInfo,
);
const levelProgression = computed(() => settingStore.levelProgression);

const tableData = computed<V1LevelConfigItem[]>(
  () => appStore.levelConfig?.filter((item) => item.level_id) ?? [],
);

const updatePage = (updateData: any) => {
  pagination.page = updateData.page;
};

// 获取等级宝箱
const getBonusCases = async () => {
  tableLoading.value = true;
  try {
    const res = await casesApi.getCasesList({
      page: 1,
      page_size: 20,
      type: 2,
    });
    if (res.data.value.code === 0) {
      bonusCases.value = res.data.value.data.list ?? [];
    }
  } catch (error) {
    console.error(error);
  } finally {
    tableLoading.value = false;
  }
};

const openCase = (item: V1CasesItem) => {
  router.push(`/cases/${item.slug}`);
};

onMounted(() => {
  appStore.getLevelConfig();
  settingStore.getUserInfo();
  columns.value = createColumns();
  getBonusCases();
});
</script>
