<template>
  <div>
    <div class="py-lg">
      <div>
        <div class="w-full flex items-center justify-between">
          <Heading size="sm" class="mb-lg mt-lg">{{ $t('crypto') }}</Heading>
        </div>
        <div
          class="grid grid-cols-[repeat(auto-fill,minmax(174px,1fr))] gap-md"
        >
          <div
            v-for="(method, methodIndex) in cryptoMethods"
            :key="methodIndex"
          >
            <DepositMethodCard
              :method="method"
              :country="selectedCountry"
              @select="(method) => handleOpenCryptoDialog(method)"
            />
          </div>
        </div>
      </div>
    </div>
    <Teleport v-if="isMounted" to="#coin-purse-footer">
      <div class="bg-[#151A29] mt-2 px-lg max-sm:px-3 pb-[12px] rounded-md">
        <div class="pt-[20px] mb-[30px]">{{ $t('withdraw_record') }}</div>
        <Table
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :pagination="pagination"
          :emptytext="emptyTableText"
          :loading="tableLoading"
          :theme-overrides-cover="{
            thColor: '#20263B',
            thTextColor: '#fff',
          }"
          @update:page="updatePage"
        />
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import WithdrawCryptoCurrencyModal from '~/components/dialog/withdraw/CryptoCurrency.vue';
import { createColumns } from '~/models/withdrawModel';
import { useDepositStore } from '~/stores/modules/deposit';

definePageMeta({
  layout: 'coin-purse',
  middleware: ['auth'],
});

const { depositApi } = useApi();
const depositStore = useDepositStore();

const dialog = useAppDialog();
const columns = ref<any[]>([]);
// const dialogRef = ref()
const isMounted = ref(false);
const tableData = ref<ApiV1TradeDepositRecordsGet200ResponseDataItemsInner[]>(
  [],
);
const tableLoading = ref(true);
const emptyTableText = ref('no_record_found');
const pagination = reactive<any>({
  pageSize: 10,
  page: 1,
  total: 0,
});
const selectedCountry = ref('');

const cryptoMethods = computed(() => {
  return depositStore.cryptoMethods;
});

const updatePage = (updateData: any) => {
  pagination.page = updateData.page;
};

// 加密货币弹窗
const handleOpenCryptoDialog = (
  method: ApiV1TradePaymentMethodsGet200ResponseDataDataInner,
) => {
  depositStore.fetchCryptoExchangeRateData();
  depositStore.fetchCryptoDepositData(method.key);
  dialog.open(WithdrawCryptoCurrencyModal, {
    style: {
      width: '880px',
    },
    contentProps: {
      method,
    },
  });
};

const getTableData = async () => {
  tableLoading.value = true;
  try {
    const res = await depositApi.getWithdrawRecords({
      page: pagination.page,
    });
    if (res.data.value.code === 0) {
      tableData.value = res.data.value.data.items;
      pagination.total = res.data.value.data.total;
    }
  } finally {
    tableLoading.value = false;
  }
};

onMounted(() => {
  nextTick(() => {
    isMounted.value = true;
  });
  depositStore.fetchPaymentMethods();
  columns.value = createColumns();
  getTableData();
});
</script>
