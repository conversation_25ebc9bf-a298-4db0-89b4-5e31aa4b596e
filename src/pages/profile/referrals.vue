<template>
  <div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3">
      <div class="font-bold">
        {{ t('affiliates') }}
      </div>
      <div class="mt-[32px]">
        {{
          t('invitation_link_description', {
            a: commissionRebateDay,
            b: commissionRebateRate,
          })
        }}
      </div>
      <div
        class="text-[#fff] font-bold px-4 h-[42px] rounded flex items-center justify-between bg-[#20263B] mt-4 mb-2 cursor-pointer"
        @click="copyAddress"
      >
        <ClientOnly>
          <div class="flex-1">{{ inviteUrl }}</div>
        </ClientOnly>
        <svgo-copy class="text-[#7D90CA] w-[20px] h-[20px]"></svgo-copy>
      </div>
    </div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3 mt-lg pb-[24px]">
      <div class="font-bold">
        {{ t('overview') }}
      </div>
      <div class="grid grid-cols-3 mt-2">
        <div class="flex flex-col items-center">
          <n-ellipsis class="w-full text-center text-[#7D90CA]">
            {{ t('affiliates') }}
          </n-ellipsis>
          <p class="mt-2.5 text-[16px]">{{ commissionOverview.affiliates }}</p>
        </div>
        <div class="flex flex-col items-center">
          <n-ellipsis class="w-full text-center text-[#7D90CA]">
            {{ t('total_earnings') }}
          </n-ellipsis>
          <p class="mt-2.5 text-[16px]">
            {{ commissionOverview.total_earning }}
          </p>
        </div>
        <div class="flex flex-col items-center">
          <n-ellipsis class="w-full text-center text-[#3EFF95]">
            {{ t('available_earnings') }}
          </n-ellipsis>
          <Currency
            class="mt-2 text-[16px]"
            :amount="commissionOverview.available_earning"
          />
          <MainButton
            v-if="Number(commissionOverview.available_earning) > 0"
            class="mt-4"
            @click="claimCommission"
          >
            {{ t('claim') }}
          </MainButton>
        </div>
      </div>
    </div>
    <div class="bg-[#151A29] rounded-lg p-lg max-sm:p-3 mt-lg">
      <ul class="flex gap-xl sm:gap-sm mb-4">
        <li
          v-for="tab in tabs"
          :key="tab.value"
          class="tab-item"
          :class="getTabClass(tab)"
          @click="handleSelectTab(tab)"
        >
          {{ t(tab.title) }}
        </li>
      </ul>
      <component :is="`referrals-${tabs[selectedTab].component}`" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '~/stores/modules/setting';
import { useAppStore } from '~/stores/modules/app';

definePageMeta({
  layout: 'profile',
  middleware: ['auth'],
});

const { t } = useI18n();
const { copy } = useCopy();
const { settingApi } = useApi();
const commissionOverview = ref<ApiV1UserCommissionOverviewGet200ResponseData>({
  affiliates: 0,
  total_earning: '0',
  available_earning: '0',
});
const tabs = [
  {
    title: 'commission_details',
    value: 0,
    component: 'commissionDetails',
  },
  {
    title: 'your_referred_users',
    value: 1,
    component: 'referredUsers',
  },
];

const selectedTab = ref(0);
const toast = useAppToast();
const settingStore = useSettingStore();
const appStore = useAppStore();

const commissionRebateDay = computed(() => {
  return appStore?.metadata?.commission_rebate_day ?? 0;
});
const commissionRebateRate = computed(() => {
  return appStore?.metadata?.commission_rebate_rate ?? '0';
});

const inviteUrl = computed(() => {
  if (import.meta.server) return '';
  return settingStore.userInfo?.invite_code;
});
const getTabClass = (tab: (typeof tabs)[number]) => ({
  'border-b-2 border-[#F8B838] text-white': selectedTab.value === tab.value,
  'text-[#7D90CA]': selectedTab.value !== tab.value,
});

const handleSelectTab = (tab: (typeof tabs)[number]) => {
  selectedTab.value = tab.value;
};

const copyAddress = () => {
  if (!inviteUrl.value) return;
  copy(inviteUrl.value);
  toast.success({
    content: t('replicating_success'),
  });
};

// 获取用户推荐总览
const getCommissionOverview = async () => {
  try {
    const res = await settingApi.getCommissionOverview();
    if (res.data.value.code === 0) {
      commissionOverview.value = res.data.value.data;
    }
  } catch (error) {
    console.log(error);
  }
};
// 领取推荐收益
const claimCommission = async () => {
  try {
    const res = await settingApi.claimCommission();
    if (res.data.value.code === 0) {
      toast.success({
        content: t('claim_success'),
      });
      getCommissionOverview();
    }
  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  getCommissionOverview();
});
</script>

<style lang="scss" scoped>
.tab-item {
  @apply px-[4px] py-[8px]  font-bold cursor-pointer;
}
</style>
