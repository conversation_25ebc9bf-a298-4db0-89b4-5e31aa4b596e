<template>
  <div class="flex-layout">
    <BaseCategoryTopTab
      ref="categoryTab"
      :category-list="exchangeStore.categories"
      :active="activeCategory"
      :is-page-loading="isPageLoading"
      @update:active-category="handleCategory"
    ></BaseCategoryTopTab>
    <FilterForm
      class="flex justify-between items-center flex-wrap my-[20px] gap-7 max-sm:gap-2"
      :form-schema="formSchema"
      :form-state="formState"
    >
      <template v-for="item in formSchema" :key="item.key">
        <FilterComponetsFormItem
          :field="item"
          :field-name="item.key"
          :model-value="formState[item.key]"
          @update:model-value="(value: any) => handleUpdate(item.key, value)"
        />
        <div v-if="item.key === 'quality'" class="grow"></div>
      </template>
    </FilterForm>
    <div class="flex-layout justify-between min-h-max">
      <ExchangeCard
        :items="exchangeListData"
        :loading="loading"
        :is-page-loading="isPageLoading"
        :action-loading="exchangeLoading"
        @exchange="handleExchangeDialog"
      ></ExchangeCard>
      <ListPaginator
        :total="total"
        :page-size="pageSize"
        :page="page"
        @update:page="handlePage"
      ></ListPaginator>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { pickBy } from 'lodash-es';
import ExchangeTip from '@/components/exchange/ExchangeTip.vue';
import { useExchangeStore } from '~/stores/modules/exchange';
import { createFormSchema } from '~/models/exchangeFilterModel';
const categoryTab = ref<any>(null);
const categoryTopTabParams = ref<any>({}); // 顶部筛选值
const activeCategory = ref<any[]>([]); // 顶部饰品选中值
const exchangeListData = ref<ApiV1ShopListPost200ResponseData['items']>([]);

const exchangeStore = useExchangeStore();
const { exchangeApi } = useApi();
const { y } = useWindowScroll();
const { openConfirm } = useDialogPromptsConfirm('exchange');
const toast = useAppToast();
const { t } = useI18n();
const { $td } = useNuxtApp();
const router = useRouter();
const { formSchema, formState } = createFormSchema();

const total = ref<number>(0);
const pageSize = ref<number>(24);
const page = ref<number>(1);
const loading = ref<boolean>(false);
const dialog = useAppDialog();
const exchangeLoading = ref(false);
const isPageLoading = ref(true);

/** 更新顶部饰品分类筛选 */
const handleCategory = (_params: any) => {
  categoryTopTabParams.value = _params;
  page.value = 1;
  getExchangeData();
};
// 获取饰品接口筛选参数
const getDataParams = () => {
  const { exterior, quality, ...rest } = formState;
  return pickBy(
    {
      page: page.value,
      page_size: pageSize.value,
      ...categoryTopTabParams.value,
      ...rest,
      exterior: exterior ? [exterior] : null,
      quality: quality ? [quality] : null,
    },
    (value) => value !== null && value !== '',
  );
};

// 分页
const handlePage = (pageInfo: { page: number }) => {
  y.value = 0;
  if (checkLogin()) {
    page.value = pageInfo.page;
    getExchangeData();
  } else if (pageInfo.page > 1) {
    page.value = 1;
  }
};
// 饰品获取接口
const getExchangeData = async () => {
  loading.value = true;
  try {
    const params = getDataParams();
    const res = await exchangeApi.getShopList(params);
    exchangeListData.value = res.data.value.data.items || [];
    total.value = res.data.value.data.total || 0;
    page.value = res.data.value.data.page || 1;
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
    isPageLoading.value = false;
  }
};

const checkExchange = async (data: ApiV1ShopCheckExchangePostRequest) => {
  try {
    const res = await exchangeApi.checkExchange(data);
    if (res.data.value.code === 0) {
      return true;
    }
  } catch (error) {
    return false;
  }
};

const handleExchangeDialog = async (
  item: ApiV1ShopListPost200ResponseData['items'][number],
) => {
  if (!checkLogin()) return;
  exchangeLoading.value = true;
  if (
    !(await checkExchange({
      goods_id: item.goods_info?.goods_id,
      price: Number(item.price),
    }))
  ) {
    exchangeLoading.value = false;
    return;
  }
  exchangeLoading.value = false;
  dialog.open(ExchangeTip, {
    title: t('confirm_exchange'),
    style: {
      width: '880px',
    },
    contentProps: {
      iconUrl: item.goods_info?.icon_url || '',
      marketName: $td(item.goods_info?.market_hash_name || ''),
      price: item.price,
      tags: item.goods_info?.tags || {},
      goodsId: item.goods_info?.goods_id,
      loading: exchangeLoading,
      styleName: item.goods_info?.style_name,
    },
    onConfirm: async (data?: V1ShopExchangeGoodsRequest) => {
      exchangeLoading.value = true;
      try {
        await handleExchange({
          goods_id: data?.goods_id,
          price: Number(data?.price),
        });
      } finally {
        exchangeLoading.value = false;
      }
    },
  });
};

const handleExchange = async (data: V1ShopExchangeGoodsRequest) => {
  try {
    const res = await exchangeApi.exchangeGoods({
      goods_id: data?.goods_id,
      price: Number(data.price),
    });
    if (res.data.value.code === 0) {
      toast.success({ content: t('exchange_successful') });
      openConfirm({
        promptKey: 'exchange',
        enablePrompts: true,
        title: t('exchange_successful'),
        content: t('check_backpack'),
        confirmText: t('go'),
        onConfirm: () => {
          dialog.hideByKey();
          router.push('/backpack/skin');
        },
      });
      getExchangeData();
      dialog.hideByKey();
    }
  } catch (error) {}
};

const { handleUpdate } = useFormUpdate(
  formState,
  {
    order_type: {
      transform: (value: any) => {
        return Number(value);
      },
    },
  },
  {
    onUpdate: () => {
      page.value = 1;
      getExchangeData();
    },
  },
);
onMounted(() => {
  exchangeStore.getExchangeFilterData();
  getExchangeData();
});
</script>
