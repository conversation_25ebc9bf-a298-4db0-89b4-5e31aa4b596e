<template>
  <div class="pb-6">
    <FairnessInfo />
    <ClientOnly>
      <n-tabs
        type="bar"
        class="w-full"
        animated
        :default-value="activeTab"
        :theme-overrides="{
          tabGapMediumBar: '8px',
        }"
        tabClass="font-bold"
        @update:value="goPage"
      >
        <template v-for="menu in menus" :key="menu.key">
          <n-tab-pane
            v-if="!(menu.restrictIP && !appStore.ipPermission)"
            :tab="menu.label"
            :name="menu.key"
          >
            <FairnessPane
              :label="menu.label"
              :column="menu.columns"
              :paginator="menu.paginator"
              :data="menu.data"
              :loading="loading"
              @update:page="updatePage"
            >
              <component :is="menu.component"></component>
            </FairnessPane>
          </n-tab-pane>
        </template>
      </n-tabs>
    </ClientOnly>
  </div>
</template>
<script lang="ts" setup>
import { createMenuSchema } from '~/models/fairnessTableModel';
import { useAppStore } from '~/stores/modules/app';
import { useSettingStore } from '~/stores/modules/setting';
const $router = useRouter();
const { menus } = createMenuSchema();
const { fairnessApi } = useApi();
const appStore = useAppStore();
const settingStore = useSettingStore();
const loading = ref(false);

const activeTab = computed(() => {
  if (!appStore.ipPermission) {
    return 'rollroom';
  }
  const tab = $router.currentRoute.value.query.tab as string;
  return tab || 'cases';
});

type TabKeyType = 'cases' | 'case-battles' | 'coinflip' | 'rollroom';
type ApiNameType =
  | 'getCasesList'
  | 'getBattlesList'
  | 'getCoinflipList'
  | 'getRollList';
// 获取数据
const getData = async (tabName?: string) => {
  if (!settingStore.userInfo?.uid) return;
  const tab = menus[(tabName || activeTab.value) as TabKeyType];
  const api = tab.api as ApiNameType;
  loading.value = true;
  const { data: req } = await fairnessApi[api]({
    page: tab.paginator.page,
    page_size: tab.paginator.page_size,
  });
  loading.value = false;
  const data = req.value?.data;
  if (data) {
    if (tab.key === 'cases') {
      const casesData = data as V1GetCasesMyReply;
      const list = casesData.list || [];
      const goodsMap = new Map(
        (casesData.goods || []).map((item) => [item.goods_id, item]),
      );
      const casesMap = new Map(
        (casesData.cases || []).map((item) => [item.slug, item]),
      );
      tab.data = list.map((el) => {
        return {
          ...el,
          ...goodsMap.get(el.goods_id),
          ...casesMap.get(el.slug),
        };
      });
    } else if (tab.key === 'case-battles') {
      const casesData = data as V1GetBattleListReply;
      const list = casesData.list || [];
      const goodsMap = new Map(
        (casesData.goods || []).map((item) => [item.goods_id, item]),
      );
      const casesMap = new Map(
        (casesData.cases || []).map((item) => [item.slug, item]),
      );
      const usersMap = new Map(
        (casesData.user || []).map((item) => [item.uid, item]),
      );
      tab.data = list.map((el) => {
        return {
          ...el,
          goods: goodsMap,
          cases: casesMap,
          users: usersMap,
        };
      });
    } else {
      tab.data = data.list || [];
    }
    tab.paginator.total = data.total || 0;
  }
};
// 更新页码
const updatePage = (page: number) => {
  const tab = menus[activeTab.value as TabKeyType];
  tab.paginator.page = page;
  getData();
};
// 切换tab
const goPage = (tab: string) => {
  $router.push(`/fairness?tab=${tab}`);
  getData(tab);
};
onMounted(() => {
  getData();
});

// const getButtonProps = (tab: Tab): ButtonProps => {
//   if (selectedTab.value === tab.value) {
//     return {
//       color: '#F8B838',
//       ghost: true,
//     };
//   }
//   return {
//     color: '#151A29',
//     textColor: '#7D90CA',
//   };
// };
</script>
<style lang="scss" scoped>
:deep(.n-tabs-tab) {
  background: #151a29;
  color: #7d90ca;
  padding: 12px;
  border-radius: 4px;
  line-height: 1;
  transition: none;
  border: solid 1px #151a29;
}
:deep(.n-tabs-tab:hover) {
  background: rgba(58, 63, 75, 1);
  color: #7d90ca !important;
}
:deep(.n-tabs-tab:active) {
  border: solid 1px #f8b838 !important;
}

:deep(.n-tabs-tab--active) {
  background: none;
  color: #f8b838 !important;
  border: solid 1px #f8b838;
}
:deep(.n-tabs-tab--active:hover) {
  background: none;
  color: #f8b838 !important;
}
:deep(.n-tabs-bar) {
  display: none;
}
</style>
