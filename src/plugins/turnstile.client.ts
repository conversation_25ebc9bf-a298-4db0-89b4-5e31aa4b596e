export default defineNuxtPlugin(() => {
  const turnstileScript = document.createElement('script');
  turnstileScript.src =
    'https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit';
  turnstileScript.async = true;
  turnstileScript.defer = true;
  document.head.appendChild(turnstileScript);

  return {
    provide: {
      renderTurnstile: (container: any, options: any) => {
        return new Promise((resolve) => {
          const intervalId = setInterval(() => {
            if (window.turnstile) {
              clearInterval(intervalId);
              const widgetId = window.turnstile.render(container, {
                ...options,
                callback: (token: any) => {
                  if (options.callback) options.callback(token);
                  resolve({ token, widgetId });
                },
              });
            }
          }, 100);
        });
      },
      resetTurnstile: (widgetId: any) => {
        if (window.turnstile) {
          window.turnstile.reset(widgetId);
        }
      },
    },
  };
});
