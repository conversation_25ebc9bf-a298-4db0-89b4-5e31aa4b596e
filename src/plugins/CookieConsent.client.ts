import 'vanilla-cookieconsent/dist/cookieconsent.css';
import * as CookieConsent from 'vanilla-cookieconsent';
import type { CookieConsentConfig, CookieValue } from 'vanilla-cookieconsent';
import { DOMAIN_CONFIG } from '~/constants';

// 排除路由
const EXCLUDED_ROUTES = {
  exact: ['/login'] as string[],
  prefix: ['/live-mng'] as string[],
} as const;

const shouldExcludeCurrentRoute = () => {
  const currentPath = window.location.pathname;

  return (
    EXCLUDED_ROUTES.exact.includes(currentPath) ||
    EXCLUDED_ROUTES.prefix.some((route) => currentPath.startsWith(route))
  );
};

const config: CookieConsentConfig = {
  guiOptions: {
    consentModal: {
      layout: 'box',
      position: 'bottom left',
    },
    preferencesModal: {
      layout: 'box',
    },
  },

  onFirstConsent: ({ cookie }: { cookie: CookieValue }) => {
    console.log('onFirstAction fired', cookie);
  },

  onConsent: () => {
    console.log('onConsent fired ...');
  },

  onChange: ({ cookie }: { cookie: <PERSON>ieValue }) => {
    console.log('onChange fired ...', cookie);
  },

  categories: {
    necessary: {
      readOnly: true,
      enabled: true,
    },
    analytics: {
      autoClear: {
        cookies: [
          {
            name: /^(_lksk)/,
          },
        ],
      },
    },
    ads: {},
  },

  language: {
    default: 'en',

    translations: {
      en: {
        consentModal: {
          title: "Hello, it's cookie time!",
          description:
            'Our website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only after consent.',
          acceptAllBtn: 'Accept',
          acceptNecessaryBtn: 'Reject',
          showPreferencesBtn: 'Manage preferences',
          // footer: `
          //   <a href="#link">Privacy Policy</a>
          //   <a href="#link">Impressum</a>
          // `,
        },
        preferencesModal: {
          title: 'Cookie preferences',
          acceptAllBtn: 'Accept all',
          acceptNecessaryBtn: 'Reject all',
          savePreferencesBtn: 'Save preferences',
          sections: [
            {
              title: 'Cookie usage',
              description:
                'We use cookies for functional, analytical and personalisation purposes.. Cookies are used to personalise content and ads, provide social features and analyse traffic. We may share information with our social media, advertising and analytics partners.',
            },
            {
              title: 'Strictly necessary cookies',
              description:
                'Necessary cookies help make a website usable by enabling basic functions like page navigation and access to secure areas of the website. The website cannot function properly without these cookies.',
              linkedCategory: 'necessary',
            },
            {
              title: 'Statistics',
              description:
                'Statistic cookies help website owners to understand how visitors interact with websites by collecting and reporting information anonymously.',
              linkedCategory: 'analytics',
              cookieTable: {
                headers: {
                  name: 'Cookie',
                  domain: 'Domain',
                  desc: 'Description',
                },
                body: [
                  {
                    name: '_ga',
                    domain: DOMAIN_CONFIG.DOMAIN,
                    desc: '...',
                  },
                  {
                    name: '_gid',
                    domain: DOMAIN_CONFIG.DOMAIN,
                    desc: '...',
                  },
                ],
              },
            },
            // {
            //   title: 'Advertisement and targeting cookies',
            //   description:
            //     'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
            //   linkedCategory: 'ads',
            // },
            // {
            //   title: 'More information',
            //   description:
            //     'For any queries in relation to our policy on cookies and your choices, please <a class="cc__link" href="#yourdomain.com">contact me</a>.',
            // },
          ],
        },
      },
    },
  },
};

export default defineNuxtPlugin(async () => {
  if (!shouldExcludeCurrentRoute()) {
    await CookieConsent.run(config);
  }
  return {
    provide: {
      CC: CookieConsent,
    },
  };
});
