export default defineNuxtPlugin(() => {
  if (import.meta.client) {
    const config = useRuntimeConfig();
    const src = `${config.public.CHATWOOT_BASE_URL}/packs/js/sdk.js`;
    loadScript(src, { async: true, defer: true }).then(() => {
      window.chatwootSettings = {
        hideMessageBubble: true,
        position: 'right',
        locale: 'en',
        type: 'standard',
        launcherTitle: '在线客服',
      };
      // 初始化
      if (window.chatwootSDK) {
        window.chatwootSDK.run({
          websiteToken: config.public.CHATWOOT_TOKEN,
          baseUrl: config.public.CHATWOOT_BASE_URL,
        });
      }
    });
  }
});
