import { defineNuxtPlugin } from '#app';
import type { LocaleMessageValue } from 'vue-i18n';
import {
  loadRemoteDecorationLanguage,
  loadRemoteLanguage,
  clearLanguageCache,
  loadRemoteDecorationFullNameLanguage,
} from '~/i18n';
import { useAppStore } from '~/stores/modules/app';
import type { Locale } from '~/vue-i18n';
import { STORAGE_KEYS } from '~/constants';

// 定义语言消息类型
interface I18nMessages {
  [key: string]: any;
}

// 扩展Nuxt Payload类型
declare module '#app' {
  interface NuxtPayload {
    i18nMessages?: I18nMessages;
    i18nAvailableLocales?: string[];
  }
  interface NuxtApp {
    $td: (key: string, defaultValue?: string, options?: any) => string;
  }
}

// 加载语言数据
async function loadAndSetLanguage(
  $i18n: any,
  langCode: Locale,
  logPrefix = '',
): Promise<any> {
  try {
    const [messages, decorationMessages] = await Promise.all([
      loadRemoteLanguage(langCode),
      loadRemoteDecorationLanguage(langCode),
    ]);

    const combinedMessages = {
      ...messages,
      ...decorationMessages,
    };

    $i18n.setLocaleMessage(langCode, combinedMessages as any);
    $i18n.locale.value = langCode;
    return combinedMessages;
  } catch (error) {
    console.error(`${logPrefix}加载语言失败: ${langCode}`, error);
    return null;
  }
}

// 加载饰品全称语言
async function loadAndMergeFullNameLanguage(
  $i18n: any,
  langCode: Locale,
  logPrefix = '',
) {
  try {
    const fullNameMessages =
      await loadRemoteDecorationFullNameLanguage(langCode);
    if (fullNameMessages && Object.keys(fullNameMessages).length > 0) {
      const currentMessages = $i18n.getLocaleMessage(langCode);
      const mergedMessages = { ...currentMessages, ...fullNameMessages };
      $i18n.setLocaleMessage(langCode, mergedMessages);
    }
  } catch (error) {
    console.error(`${logPrefix}加载语言失败: ${langCode}`, error);
  }
}

async function fetchAvailableLocales(): Promise<string[]> {
  try {
    const metadata = useState<any>('app-metadata', () => null);
    if (metadata.value && metadata.value.language_list) {
      return metadata.value.language_list.map(
        (lang: any) => lang.upload_filename,
      );
    }
    const { BASE_URL: baseUrl } = useRuntimeConfig().public;
    const response = await fetch(`${baseUrl}/api/v1/metadata`);
    if (!response.ok) {
      throw new Error(`获取可用语言失败: ${response.status}`);
    }
    const result = await response.json();
    metadata.value = result.data;
    return result.data.language_list.map((lang: any) => lang.upload_filename);
  } catch (error) {
    return ['en'];
  }
}

function parseAcceptLanguage(
  acceptLanguage: string,
): { code: string; q: number }[] {
  if (!acceptLanguage) {
    return [];
  }
  return acceptLanguage
    .split(',')
    .map((langEntry) => {
      const parts = langEntry.trim().split(';');
      const code = parts[0].trim();
      let q = 1.0;
      if (parts[1]) {
        const qPart = parts[1].trim();
        if (qPart.startsWith('q=')) {
          q = parseFloat(qPart.substring(2)) || 0;
        }
      }
      return { code, q };
    })
    .filter((lang) => lang.q > 0)
    .sort((a, b) => b.q - a.q); // 权重排序
}

function detectUserLanguage(
  $i18n: any,
  acceptLanguage: string = '',
  availableLocales: string[] = [],
): Locale {
  const defaultLocale = ($i18n.locale.value || $i18n.defaultLocale) as Locale;
  if (!availableLocales.length || !acceptLanguage) {
    return defaultLocale;
  }
  const parsedLanguages = parseAcceptLanguage(acceptLanguage);
  const specialMappings: Record<string, string> = {
    'zh-CN': 'zh-Hans',
    'zh-TW': 'zh-Hant',
    'zh-HK': 'zh-Hant',
  };
  for (const { code: preferredLang } of parsedLanguages) {
    if (availableLocales.includes(preferredLang)) {
      return preferredLang as Locale;
    }
    const mappedLang = specialMappings[preferredLang];
    if (mappedLang && availableLocales.includes(mappedLang)) {
      return mappedLang as Locale;
    }
    const baseLang = preferredLang.split('-')[0];
    if (baseLang === 'zh') {
      if (availableLocales.includes('zh-Hans')) return 'zh-Hans' as Locale;
      if (availableLocales.includes('zh-Hant')) return 'zh-Hant' as Locale;
      if (availableLocales.includes('zh')) return 'zh' as Locale;
    } else if (
      baseLang !== preferredLang &&
      availableLocales.includes(baseLang)
    ) {
      return baseLang as Locale;
    } else {
      const regionalMatch = availableLocales.find((loc) =>
        loc.startsWith(baseLang + '-'),
      );
      if (regionalMatch) {
        return regionalMatch as Locale;
      }
    }
  }

  return (
    availableLocales.includes($i18n.defaultLocale)
      ? $i18n.defaultLocale
      : availableLocales[0] || 'en'
  ) as Locale;
}

export default defineNuxtPlugin(async (nuxtApp) => {
  const { $i18n, $getCookie } = useNuxtApp();
  // 服务端处理
  if (import.meta.server) {
    const headers = useRequestHeaders();
    const acceptLanguage = headers['accept-language'] || '';
    const availableLocales = await fetchAvailableLocales();
    nuxtApp.payload.i18nAvailableLocales = availableLocales;
    const cookieLocale = $getCookie(STORAGE_KEYS.COOKIES.I18N_REDIRECTED);
    let langCode: Locale;
    if (!cookieLocale?.value) {
      langCode = detectUserLanguage($i18n, acceptLanguage, availableLocales);
    } else {
      langCode = cookieLocale?.value as Locale;
    }
    const messages = await loadAndSetLanguage($i18n, langCode, '服务端');
    $i18n.setLocaleCookie(langCode);
    if (messages) {
      nuxtApp.payload.i18nMessages = {
        [langCode]: messages,
      };
    }
    loadAndMergeFullNameLanguage($i18n, langCode, '服务端');
  }

  // 客户端处理
  if (import.meta.client) {
    const { $i18n, $getCookie } = useNuxtApp();
    if (!$i18n) return;

    const { locale } = $i18n;
    const appStore = useAppStore();
    const langCookie = $getCookie(STORAGE_KEYS.COOKIES.I18N_REDIRECTED);
    const cookieLocale = langCookie?.value as Locale | undefined;
    const availableLocales = nuxtApp.payload.i18nAvailableLocales || [
      $i18n.defaultLocale as string,
    ];

    let langCode: Locale;

    if (cookieLocale && availableLocales.includes(cookieLocale)) {
      langCode = cookieLocale;
    } else {
      const browserLanguages = navigator.languages || [navigator.language];
      const browserAcceptLanguage = browserLanguages.join(',');
      langCode = detectUserLanguage(
        $i18n,
        browserAcceptLanguage,
        availableLocales,
      );
    }
    $i18n.locale.value = langCode;
    appStore.setCurrentLang(langCode);

    // 检查服务端是否已经加载了语言消息
    if (
      nuxtApp.payload.i18nMessages &&
      nuxtApp.payload.i18nMessages[langCode]
    ) {
      // 使用服务端传递过来的语言数据，不需要重新请求
      $i18n.setLocaleMessage(langCode, nuxtApp.payload.i18nMessages[langCode]);
      locale.value = langCode;
    } else {
      // 服务端未加载该语言，需要客户端请求
      await loadAndSetLanguage($i18n, langCode, '客户端');
    }
    loadAndMergeFullNameLanguage($i18n, langCode, '客户端');

    // 语言切换处理
    $i18n.onBeforeLanguageSwitch = async (
      oldLocale: Locale,
      newLocale: Locale,
    ) => {
      const appStore = useAppStore();

      try {
        const combinedMessages = await loadAndSetLanguage(
          $i18n,
          newLocale,
          '语言切换',
        );
        if (combinedMessages) {
          appStore.setCurrentLang(newLocale);
          loadAndMergeFullNameLanguage($i18n, newLocale, '语言切换');
          return newLocale;
        } else {
          throw new Error('语言加载失败');
        }
      } catch (error) {
        locale.value = oldLocale;
        appStore.setCurrentLang(oldLocale);
        return oldLocale;
      }
    };
    const originalI18n = $i18n;
    (originalI18n as any).clearLanguageCache = clearLanguageCache;
  }

  const tdMethod = function (
    key: string,
    defaultValue: string = '',
    options: any = {},
  ): string {
    if (key?.includes('|')) {
      const messages = $i18n.getLocaleMessage($i18n.locale.value) as Record<
        string,
        LocaleMessageValue
      >;
      const possibleKeys = Object.keys(messages).filter((k) => k === key);
      if (possibleKeys.length > 0) {
        const bestMatch = possibleKeys.reduce((a, b) =>
          a.length > b.length ? a : b,
        );
        const message = messages[bestMatch];
        return message ? String(message) : key;
      }
    }
    const hasKey = $i18n.te(key);
    const result = hasKey ? $i18n.t(key, options) : defaultValue || key;
    return String(result);
  };
  nuxtApp.vueApp.config.globalProperties.$td = tdMethod;
  nuxtApp.provide('td', tdMethod);
});
