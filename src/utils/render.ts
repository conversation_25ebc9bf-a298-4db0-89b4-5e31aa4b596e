import type { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import { h } from 'vue';
import BaseIcon from '~/components/BaseIcon.vue';

interface RenderHtmlOptions {
  option: SelectBaseOption;
  label?: string;
  showIcon?: boolean;
  iconType?: 'gold';
  iconClass?: string;
}

export const renderSelectOption = ({
  option,
  label = '',
  showIcon = false,
  iconType = 'gold',
  iconClass = 'text-theme-color mr-1.5 text-xl',
}: RenderHtmlOptions) => {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        alignItems: 'center',
      },
    },
    [
      label && h('div', { class: 'mr-1.5' }, label),
      showIcon &&
        h(BaseIcon, {
          name: iconType,
          class: iconClass,
          filled: true,
          fontControlled: false,
        }),
      option.label as string,
    ],
  );
};
