interface ScriptLoaderOption extends Partial<HTMLScriptElement> {
  partytown?: boolean;
  src?: string;
}

export const loadScript = (
  source: string,
  options: ScriptLoaderOption = {},
) => {
  return new Promise<void>((resolve, reject) => {
    const head = document.head || document.getElementsByTagName('head')[0];
    const script = document.createElement('script');

    const {
      src,
      type = options.partytown ? 'text/partytown' : 'text/javascript',
      defer = false,
      async = false,
      ...restAttrs
    } = options;

    script.type = type;
    script.defer = defer;
    script.async = async;
    script.src = src || source;

    Object.keys(restAttrs).forEach((key) => {
      (script as any)[key] = (restAttrs as any)[key];
    });

    script.onload = () => resolve();
    script.onerror = () =>
      reject(new Error(`Failed to load script: ${script.src}`));

    head.appendChild(script);
  });
};
