/**
 * 格式化GMT时间为本地时间字符串
 * @param gmtStr GMT时间字符串
 * @param format 格式化模板，默认：'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的本地时间字符串
 */
export function formatDate(
  gmtStr: string | Date,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string {
  const reg = /^\d{10}$/;
  const dateStr = gmtStr instanceof Date ? gmtStr.toISOString() : gmtStr;
  const date = reg.test(dateStr)
    ? new Date(Number(dateStr) * 1000)
    : new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 获取GMT(格林尼治)时间戳
 * @param date Date对象
 * @returns GMT时间戳(毫秒)
 */
export function getGMTTimestamp(date: Date): number {
  return Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds(),
    date.getUTCMilliseconds(),
  );
}

/**
 * 将时间字符串转换为GMT时间戳
 * @param dateStr 时间字符串，如: "2024-12-20 10:54:44"
 * @returns GMT时间戳(毫秒)
 */
export function parseToGMTTimestamp(dateStr: string): number {
  const date = new Date(dateStr);
  return getGMTTimestamp(date);
}

/**
 * GMT时间字符串转换为GMT时间戳
 * @param gmtStr GMT时间字符串，如: "Thu, 20 Dec 2024 02:54:44 GMT"
 * @returns GMT时间戳(毫秒)
 */
export function gmtStrToTimestamp(gmtStr: string): number {
  return new Date(gmtStr).getTime();
}

/**
 * 获取当前时间的时间戳（考虑本地时区-零时区）
 * @returns 当前时间的时间戳(毫秒)
 */
export function getLocalTimeAsUTCTimestamp(): number {
  const now = new Date();
  const offset = now.getTimezoneOffset();
  return now.getTime() + offset * 60 * 1000;
}
