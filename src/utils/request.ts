import type { UseFetchOptions } from 'nuxt/app';
import UAParser from 'ua-parser-js';
import { useSettingStore } from '~/stores/modules/setting';
import { delay } from '~/utils';
import { STORAGE_KEYS } from '~/constants';

const useGlobalSpin = useAppSpin();

export interface ApiRequestData {
  [key: string]: string | any;
}

export interface ApiResponseData<T = any> {
  code: number;
  data: T;
  message: string;
  timestamp: number; // 服务器时间戳
  extra: T;
}

export interface FetchAPIResponseData<T = any> extends Response {
  _data: ApiResponseData<T>;
  data: Ref<ApiResponseData<T>>;
  message: string;
  success: boolean;
  pending: Ref<boolean>;
  refresh: (opts?: any) => Promise<T>;
  execute: (opts?: any) => Promise<T>;
  error: Ref<Error | null>;
}

export type UrlType =
  | string
  | Request
  | Ref<string | Request>
  | (() => string | Request);

export interface RequestConfig<T = any> extends UseFetchOptions<T> {
  // 开启apifox本地调试
  mock?: any;
  // TODO: 暂时未实现
  ignoreCatch?: boolean;
  // IMPORTANT: 用于忽略默认的提示
  ignoreGlobalErrorMessage?: boolean;
  // 是否开启服务端请求 server调用方式 同 ❓https://nuxt.com/docs/api/composables/use-fetch
  server?: boolean;
  // IMPORTANT: 用于忽略取消请求 ❓是否需要开启
  ignoreCancel?: boolean;
  // 全局spin遮罩
  globalSpin?: boolean;
  // 延时时间
  delay?: number;
}

// API 响应
export enum ApiResponseCode {
  SUCCESS = 0,
  TOKEN_EXPIRED = 11006,
  // 用户注销
  USER_CLOSE = 102304,
  // 交易链接未绑定
  TRADE_URL_NOT_BIND = 22038,
  // 余额不足
  BALANCE_NOT_ENOUGH = 26201,
  // 主播后台登陆无权限
  LIVEMNG_NOT_AUTHORIZED = 22044,
}

// HTTP响应
enum HttpStatusCode {
  // Success codes
  SUCCESS = 200,

  // Client error codes
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,

  // Server error codes
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}

// const decryptData = (data: any) => {
//     return data;
// };

const getCookie = (key: string) => {
  const { $getCookie } = useNuxtApp();

  return $getCookie(key);
};

const redirectToLogin = () => {
  if (import.meta.client) {
    const $router = useRouter();
    const $route = useRoute();
    // 是否是主播后台
    const isLive = $router.currentRoute.value.path.includes('live-mng');
    if (isLive) {
      $router.push('/live-mng/login');
      return;
    }
    const hasAuthMiddleware =
      Array.isArray($route.meta?.middleware) &&
      $route.meta.middleware.includes('auth');
    if (!hasAuthMiddleware) {
      return;
    }
    if (location.pathname !== '/') {
      $router.push('/');
    } else {
      nextTick(() => {
        const settingStore = useSettingStore();
        settingStore.signin();
      });
    }
  }
};

const showAlert = (code: string, message?: string) => {
  if (import.meta.server) {
    showError({ message, statusCode: 500 });
  } else {
    const { $i18n } = useNuxtApp();
    const { t } = $i18n;
    const basicToast = useAppToast();
    basicToast.error({ content: t(code, message || '') });
  }
};

const requestConfigurator = async (options: RequestConfig<any>) => {
  const userAuth = getCookie(STORAGE_KEYS.COOKIES.TOKEN);
  const { $i18n } = useNuxtApp();
  let headers: HeadersInit = {};
  const parser = new UAParser();
  let result;
  if (import.meta.client) {
    result = parser.getResult();
    headers['x-md-device-id'] = await getDeviceId.getFinger();
  } else {
    const serverHeader = useRequestHeaders(['user-agent']);
    parser.setUA(serverHeader['user-agent'] ?? '');
    result = parser.getResult();
    headers['x-md-frontend-server'] = `${Math.random().toString().slice(-12)}`;
  }
  headers = {
    ...headers,
    'x-md-device-name': result.browser.name ?? 'Unkonwn',
    'x-md-ua-platform': result.os.name ?? 'Unkonwn',
    'x-md-ua-mobile': '0',
    'x-md-ua-language': $i18n.locale.value || 'en',
  };
  if (userAuth.value) {
    headers.Authorization = `Bearer ${userAuth.value}`;
  }
  options.headers = headers;
};

const handleHttpResponseError = (response: FetchAPIResponseData) => {
  const { status } = response;
  switch (status) {
    case HttpStatusCode.FORBIDDEN:
    case HttpStatusCode.NOT_FOUND:
    case HttpStatusCode.BAD_REQUEST:
    case HttpStatusCode.UNAUTHORIZED:
      showAlert('request_error');
      break;
    case HttpStatusCode.INTERNAL_SERVER_ERROR:
    case HttpStatusCode.BAD_GATEWAY:
    case HttpStatusCode.SERVICE_UNAVAILABLE:
    case HttpStatusCode.GATEWAY_TIMEOUT:
      showAlert('server_exception');
      break;
    default:
      return response;
  }
  return response;
};

const responseErrorInterceptor = (
  response: FetchAPIResponseData,
  options: RequestConfig<any>,
) => {
  const { code, message } = response._data;
  if (
    code === ApiResponseCode.TOKEN_EXPIRED ||
    code === ApiResponseCode.USER_CLOSE ||
    code === ApiResponseCode.LIVEMNG_NOT_AUTHORIZED
  ) {
    clearLoginInfo();
    if (import.meta.client) {
      redirectToLogin();
    }
    if (code === ApiResponseCode.LIVEMNG_NOT_AUTHORIZED && import.meta.client) {
      const { showUnauthorizedDialog } = useLiveMngAuthDialog();
      showUnauthorizedDialog();
    }
    return response;
  }
  if (import.meta.server) return response;
  const { handleSteamBindSettingDialog } = useSteamSettingsDialogs();

  switch (code) {
    case ApiResponseCode.TRADE_URL_NOT_BIND:
      handleSteamBindSettingDialog();
      break;
    case ApiResponseCode.BALANCE_NOT_ENOUGH:
      useBalanceNotEnoughDialog();
      break;
    default:
      if (!options.ignoreGlobalErrorMessage) {
        showAlert(`${code}`, message);
      }
      return response;
  }
  return response;
};

/// 清除登录态
export const clearLoginInfo = () => {
  if (import.meta.server) {
    return;
  }
  const settingStore = useSettingStore();
  settingStore.setToken(null);
  settingStore.clearErrorCookies();
  useTurnstileVerification().resetVerification();
  settingStore.userInfo = undefined;
  getDeviceId.clearFinger();
};

const middleInterceptors = (options: RequestConfig) => {
  const { globalSpin } = options;
  if (globalSpin && import.meta.client) {
    useGlobalSpin?.open();
  }
};

const fetchService = async <T>(
  url: UrlType,
  params: ApiRequestData | any = {},
  options: RequestConfig<T> = {},
): Promise<FetchAPIResponseData<T> | any> => {
  try {
    const { BASE_URL: baseURL, LIVE_BASE_URL: liveBaseURL } =
      useRuntimeConfig().public;
    const { method = 'get', server = false, mock, ...otherOptions } = options;
    const serverRequest = server && import.meta.server;

    if (import.meta.dev && !serverRequest) {
      url = `${mock ? '/mock' : ''}${url}`;
    }

    const abortController = new AbortController();
    middleInterceptors(options);

    if (options.delay) {
      await delay(options.delay);
    }

    let bodyData = params;
    if (method !== 'get') {
      bodyData = params instanceof FormData ? params : JSON.stringify(params);
    } else {
      bodyData = undefined;
      for (const key in params) {
        if (Array.isArray(params[key])) {
          params[key].forEach((item: any, index: number) => {
            params[`${key}[${index}]`] = item;
          });
          delete params[key];
        }
      }
    }
    const $router = useRouter();
    const isLive = $router.currentRoute.value.fullPath.includes('/live-mng');

    const mergeBaseUrl = isLive ? liveBaseURL : baseURL;
    const requestConfig = {
      baseURL: mock ? '' : mergeBaseUrl,
      method,
      params: method === 'get' ? params : undefined,
      body: bodyData,
      timeout: 10000,
      ...otherOptions,
      signal: abortController.signal,
      onRequest({ options }: any) {
        requestConfigurator(options as RequestConfig);
      },
      onRequestError({ request, error }: any) {
        console.log('[fetch request error]', request, error);
      },
      onResponse: ({ response }: any) => {
        const { code } = response._data;
        if (code) {
          responseErrorInterceptor(response, options);
        }
      },
      onResponseError({ response, _error }: any) {
        handleHttpResponseError(response as unknown as FetchAPIResponseData);
      },
    };

    try {
      const key = typeof url === 'string' ? url : url.toString();

      if (options.server === false || (import.meta.client && !options.server)) {
        const response = await $fetch(url as any, requestConfig as any);
        return {
          data: ref(response),
          _data: response,
        };
      }

      const { data, error } = await useAsyncData(
        key,
        () => $fetch(url as any, requestConfig as any),
        {
          server: serverRequest,
          lazy: options.lazy ?? false,
          immediate: options.immediate ?? true,
        },
      );

      if (error.value) {
        throw error.value;
      }

      return {
        data,
        _data: unref(data),
      };
    } finally {
      if (options.globalSpin && import.meta.client) {
        useGlobalSpin?.destroy();
      }
    }
  } catch (error) {
    console.error('Request error:', error);
    throw error;
  }
};

export default fetchService;
