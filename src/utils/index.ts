import { isEqual, uniqWith, isObject, isEmpty, every } from 'lodash-es';
import { load } from '@fingerprintjs/fingerprintjs';
import BigNumberDefault from 'bignumber.js';
/*
 * 合并唯一对象
 * @param array
 * @param excludeKeys 排除的key值
 * @returns
 */
export const uniqueObjectsWithCount = (array: any[], excludeKeys: string[]) => {
  const comparator = (obj1: any, obj2: any) => {
    const cleanedObj1 = excludeKeys.reduce((acc, key) => {
      /* eslint-disable @typescript-eslint/no-unused-vars */
      const { [key]: omitted, ...rest } = acc;
      return rest;
    }, obj1);

    const cleanedObj2 = excludeKeys.reduce((acc, key) => {
      /* eslint-disable @typescript-eslint/no-unused-vars */
      const { [key]: omitted, ...rest } = acc;
      return rest;
    }, obj2);

    return isEqual(cleanedObj1, cleanedObj2);
  };

  const uniqueObjects = uniqWith(array, comparator);

  const counts = uniqueObjects.map(
    (uniqueObj) => array.filter((obj) => comparator(obj, uniqueObj)).length,
  );

  return uniqueObjects.map((obj, index) => ({
    ...obj,
    count: counts[index],
  }));
};

export const BigNumberCalc = {
  add: function (
    a: string | number,
    b: string | number,
    decimalPlaces: number = 2,
  ) {
    return new BigNumberDefault(a)
      .plus(b)
      .decimalPlaces(decimalPlaces, BigNumberDefault.ROUND_DOWN)
      .toString();
  },
  subtract: function (
    a: string | number,
    b: string | number,
    decimalPlaces: number = 2,
  ) {
    return new BigNumberDefault(a)
      .minus(b)
      .decimalPlaces(decimalPlaces, BigNumberDefault.ROUND_DOWN)
      .toString();
  },
  multiply: function (
    a: string | number,
    b: string | number,
    decimalPlaces: number = 2,
  ) {
    return new BigNumberDefault(a)
      .multipliedBy(b)
      .decimalPlaces(decimalPlaces, BigNumberDefault.ROUND_DOWN)
      .toString();
  },
  divide: function (
    a: string | number,
    b: string | number,
    decimalPlaces: number = 2,
  ) {
    return new BigNumberDefault(a)
      .dividedBy(b)
      .decimalPlaces(decimalPlaces, BigNumberDefault.ROUND_DOWN)
      .toString();
  },
};

/**
 * 获取url参数
 * @param name
 * @returns
 */
export const getQuery = (name: string = '') => {
  if (typeof window === 'undefined') return null;
  let { search } = window.location;
  search = decodeURI(search) || '';
  if (!search) {
    return {};
  }
  const queryObj = search
    .replace('?', '')
    .split('&')
    .reduce((pre: any, curr: any) => {
      const [name, value] = curr.split('=');
      pre[name] = value === undefined ? true : value;
      return pre;
    }, {});

  // queryObj.env = 'stage';
  // console.log(name, '<====name======')
  return name ? queryObj[name] || null : queryObj;
};

/**
 *  替换字符串中非数字
 *
 * @param {string} input
 * @return {string}
 */
export const enforceSingleDecimalFormat = (input: string) => {
  let cleaned = input.replace(/[^\d.]/g, '');
  const dotCount = (cleaned.match(/\./g) || []).length;

  if (dotCount > 1) {
    const firstDotIndex = cleaned.indexOf('.');
    cleaned =
      cleaned.substring(0, firstDotIndex + 1) +
      cleaned.substring(firstDotIndex + 1).replace(/\./g, '');
  }
  cleaned = cleaned.replace(/^0+/, '0');

  if (cleaned.startsWith('.')) {
    cleaned = '0' + cleaned;
  }
  if (cleaned === '0' && !input.startsWith('0')) {
    cleaned = '';
  }

  return cleaned;
};

/**
 *  判断对象是否为空
 * @param object
 * @returns
 */
export const isDeeplyEmpty: (object: any) => boolean = (object: any) => {
  return (
    isEmpty(object) ||
    every(object, (value) => {
      return isObject(value) ? isDeeplyEmpty(value) : isEmpty(value);
    })
  );
};

/// 获取设备id
export class getDeviceId {
  static async getFinger() {
    const getBrowserType = () => {
      const ua = navigator.userAgent;
      if (ua.includes('Chrome')) return 'cr';
      if (ua.includes('Firefox')) return 'ff';
      if (ua.includes('Safari') && !ua.includes('Chrome')) return 'sf';
      if (ua.includes('Edge') || ua.includes('Edg')) return 'ed';
      if (ua.includes('MSIE') || ua.includes('Trident')) return 'ie';
      if (ua.includes('Opera') || ua.includes('OPR')) return 'op';
      return 'ot';
    };
    const browserType = getBrowserType();
    const fingerprintId = localStorage.getItem('deviceId');
    if (fingerprintId) return fingerprintId;
    const fpPromise = load();
    const fp = await fpPromise;
    const result = await fp.get();

    const generateSimpleHash = (str: string): string => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(36);
    };
    const { getCurrentDomain } = useSiteName();
    const browserFingerprint = generateSimpleHash(
      navigator.userAgent +
        navigator.language +
        navigator.hardwareConcurrency +
        screen.colorDepth +
        getCurrentDomain(),
    );
    const combinedId = `${result.visitorId.substring(0, 16)}${browserType}${browserFingerprint}`;
    localStorage.setItem('deviceId', combinedId);
    return combinedId;
  }

  static clearFinger() {
    localStorage.removeItem('deviceId');
  }
}

// 延时方法
export const delay = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const ClientSeed = {
  create(length: number = 32) {
    let seed = '';
    // 生成自定义seed
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      seed += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return seed;
  },
  get(length: number = 32) {
    let seed = localStorage.getItem('clientSeed');
    if (seed) {
      return seed;
    }
    seed = ClientSeed.create(length);
    ClientSeed.save(seed);
    return seed;
  },
  valide(seed: string, length: number = 32) {
    const regex = new RegExp(`^[A-Za-z0-9]{${length}}$`);
    return regex.test(seed);
  },
  save(seed: string) {
    localStorage.setItem('clientSeed', seed);
  },
};
