import pkg from 'i18n-iso-countries';

// 同步导入项目支持的语言包
import bgLocale from 'i18n-iso-countries/langs/bg.json';
import csLocale from 'i18n-iso-countries/langs/cs.json';
import daLocale from 'i18n-iso-countries/langs/da.json';
import deLocale from 'i18n-iso-countries/langs/de.json';
import elLocale from 'i18n-iso-countries/langs/el.json';
import enLocale from 'i18n-iso-countries/langs/en.json';
import esLocale from 'i18n-iso-countries/langs/es.json';
import fiLocale from 'i18n-iso-countries/langs/fi.json';
import frLocale from 'i18n-iso-countries/langs/fr.json';
import huLocale from 'i18n-iso-countries/langs/hu.json';
import itLocale from 'i18n-iso-countries/langs/it.json';
import jaLocale from 'i18n-iso-countries/langs/ja.json';
import koLocale from 'i18n-iso-countries/langs/ko.json';
import nlLocale from 'i18n-iso-countries/langs/nl.json';
import noLocale from 'i18n-iso-countries/langs/no.json';
import plLocale from 'i18n-iso-countries/langs/pl.json';
import ptLocale from 'i18n-iso-countries/langs/pt.json';
import roLocale from 'i18n-iso-countries/langs/ro.json';
import ruLocale from 'i18n-iso-countries/langs/ru.json';
import svLocale from 'i18n-iso-countries/langs/sv.json';
import thLocale from 'i18n-iso-countries/langs/th.json';
import trLocale from 'i18n-iso-countries/langs/tr.json';
import ukLocale from 'i18n-iso-countries/langs/uk.json';
import viLocale from 'i18n-iso-countries/langs/vi.json';
import zhLocale from 'i18n-iso-countries/langs/zh.json';

const { registerLocale } = pkg;

// 语言包映射表（仅包含项目支持的语言）
const localeMap: Record<string, any> = {
  bg: bgLocale,
  cs: csLocale,
  da: daLocale,
  de: deLocale,
  el: elLocale,
  en: enLocale,
  es: esLocale,
  fi: fiLocale,
  fr: frLocale,
  hu: huLocale,
  it: itLocale,
  ja: jaLocale,
  ko: koLocale,
  nl: nlLocale,
  no: noLocale,
  pl: plLocale,
  pt: ptLocale,
  ro: roLocale,
  ru: ruLocale,
  sv: svLocale,
  th: thLocale,
  tr: trLocale,
  uk: ukLocale,
  vi: viLocale,
  zh: zhLocale,

  // 中文变体映射
  'zh-Hans': zhLocale,
  'zh-Hant': zhLocale,
  'zh-CN': zhLocale,
  'zh-TW': zhLocale,
  'zh-HK': zhLocale,

  // 英语变体映射
  'en-US': enLocale,
  'en-GB': enLocale,
  'en-CA': enLocale,
  'en-AU': enLocale,
  'en-NZ': enLocale,
  'en-ZA': enLocale,
  'en-IN': enLocale,

  // 西班牙语变体映射
  'es-ES': esLocale,
  'es-MX': esLocale,
  'es-AR': esLocale,
  'es-CO': esLocale,
  'es-CL': esLocale,
  'es-PE': esLocale,
  'es-VE': esLocale,
  'es-EC': esLocale,
  'es-GT': esLocale,
  'es-CU': esLocale,
  'es-BO': esLocale,
  'es-DO': esLocale,
  'es-HN': esLocale,
  'es-PY': esLocale,
  'es-SV': esLocale,
  'es-NI': esLocale,
  'es-CR': esLocale,
  'es-PA': esLocale,
  'es-UY': esLocale,

  // 法语变体映射
  'fr-FR': frLocale,
  'fr-CA': frLocale,
  'fr-BE': frLocale,
  'fr-CH': frLocale,
  'fr-LU': frLocale,
  'fr-MC': frLocale,

  // 德语变体映射
  'de-DE': deLocale,
  'de-AT': deLocale,
  'de-CH': deLocale,
  'de-LU': deLocale,
  'de-LI': deLocale,

  // 意大利语变体映射
  'it-IT': itLocale,
  'it-CH': itLocale,
  'it-SM': itLocale,
  'it-VA': itLocale,

  // 葡萄牙语变体映射
  'pt-BR': ptLocale,
  'pt-PT': ptLocale,
  'pt-AO': ptLocale,
  'pt-MZ': ptLocale,

  // 俄语变体映射
  'ru-RU': ruLocale,
  'ru-BY': ruLocale,
  'ru-KZ': ruLocale,
  'ru-KG': ruLocale,

  // 荷兰语变体映射
  'nl-NL': nlLocale,
  'nl-BE': nlLocale,
  'nl-SR': nlLocale,

  // 挪威语变体映射
  'no-NO': noLocale, // 挪威语（通用）

  // 韩语变体映射
  'ko-KR': koLocale,
  'ko-KP': koLocale,

  // 日语变体映射
  'ja-JP': jaLocale,

  // 泰语变体映射
  'th-TH': thLocale,

  // 越南语变体映射
  'vi-VN': viLocale,

  // 土耳其语变体映射
  'tr-TR': trLocale,
  'tr-CY': trLocale,

  // 希腊语变体映射
  'el-GR': elLocale,
  'el-CY': elLocale,

  // 波兰语变体映射
  'pl-PL': plLocale,

  // 捷克语变体映射
  'cs-CZ': csLocale,

  // 匈牙利语变体映射
  'hu-HU': huLocale,

  // 罗马尼亚语变体映射
  'ro-RO': roLocale,
  'ro-MD': roLocale,

  // 保加利亚语变体映射
  'bg-BG': bgLocale,

  // 芬兰语变体映射
  'fi-FI': fiLocale,

  // 瑞典语变体映射
  'sv-SE': svLocale,
  'sv-FI': svLocale,

  // 丹麦语变体映射
  'da-DK': daLocale,

  // 乌克兰语变体映射
  'uk-UA': ukLocale,
};

// 已注册的语言包缓存
const registeredLocales = new Set<string>();

/**
 * 语言代码映射到 i18n-iso-countries 支持的格式
 */
export const getI18nCountriesLocale = (locale: string): string => {
  const localeMap: Record<string, string> = {
    // 中文变体
    'zh-Hans': 'zh',
    'zh-Hant': 'zh',
    'zh-CN': 'zh',
    'zh-TW': 'zh',
    'zh-HK': 'zh',
    'zh-MO': 'zh',
    'zh-SG': 'zh',

    // 葡萄牙语变体
    'pt-BR': 'pt',
    'pt-PT': 'pt',
    'pt-AO': 'pt',
    'pt-MZ': 'pt',

    // 英语变体
    'en-US': 'en',
    'en-GB': 'en',
    'en-CA': 'en',
    'en-AU': 'en',
    'en-NZ': 'en',
    'en-ZA': 'en',
    'en-IN': 'en',

    // 西班牙语变体
    'es-ES': 'es',
    'es-MX': 'es',
    'es-AR': 'es',
    'es-CO': 'es',
    'es-CL': 'es',
    'es-PE': 'es',
    'es-VE': 'es',
    'es-EC': 'es',
    'es-GT': 'es',
    'es-CU': 'es',
    'es-BO': 'es',
    'es-DO': 'es',
    'es-HN': 'es',
    'es-PY': 'es',
    'es-SV': 'es',
    'es-NI': 'es',
    'es-CR': 'es',
    'es-PA': 'es',
    'es-UY': 'es',

    // 法语变体
    'fr-FR': 'fr',
    'fr-CA': 'fr',
    'fr-BE': 'fr',
    'fr-CH': 'fr',
    'fr-LU': 'fr',
    'fr-MC': 'fr',

    // 德语变体
    'de-DE': 'de',
    'de-AT': 'de',
    'de-CH': 'de',
    'de-LU': 'de',
    'de-LI': 'de',

    // 意大利语变体
    'it-IT': 'it',
    'it-CH': 'it',
    'it-SM': 'it',
    'it-VA': 'it',

    // 俄语变体
    'ru-RU': 'ru',
    'ru-BY': 'ru',
    'ru-KZ': 'ru',
    'ru-KG': 'ru',

    // 荷兰语变体
    'nl-NL': 'nl',
    'nl-BE': 'nl',
    'nl-SR': 'nl',

    // 挪威语变体
    'no-NO': 'no',

    // 韩语变体
    'ko-KR': 'ko',
    'ko-KP': 'ko',

    // 日语变体
    'ja-JP': 'ja',

    // 泰语变体
    'th-TH': 'th',

    // 越南语变体
    'vi-VN': 'vi',

    // 土耳其语变体
    'tr-TR': 'tr',
    'tr-CY': 'tr',

    // 希腊语变体
    'el-GR': 'el',
    'el-CY': 'el',

    // 波兰语变体
    'pl-PL': 'pl',

    // 捷克语变体
    'cs-CZ': 'cs',

    // 匈牙利语变体
    'hu-HU': 'hu',

    // 罗马尼亚语变体
    'ro-RO': 'ro',
    'ro-MD': 'ro',

    // 保加利亚语变体
    'bg-BG': 'bg',

    // 芬兰语变体
    'fi-FI': 'fi',

    // 瑞典语变体
    'sv-SE': 'sv',
    'sv-FI': 'sv',

    // 丹麦语变体
    'da-DK': 'da',

    // 乌克兰语变体
    'uk-UA': 'uk',
  };

  return localeMap[locale] || locale || 'en';
};

/**
 * 注册指定语言的国家语言包
 */
export const registerCountryLocale = (localeCode: string): boolean => {
  try {
    const mappedLocale = getI18nCountriesLocale(localeCode);

    // 已注册过
    if (registeredLocales.has(mappedLocale)) {
      return true;
    }

    const localeData = localeMap[mappedLocale];
    if (localeData) {
      registerLocale(localeData);
      registeredLocales.add(mappedLocale);
      return true;
    } else {
      if (!registeredLocales.has('en')) {
        registerLocale(enLocale);
        registeredLocales.add('en');
      }
      return true;
    }
  } catch (error) {
    if (!registeredLocales.has('en')) {
      registerLocale(enLocale);
      registeredLocales.add('en');
    }
    return false;
  }
};

/**
 * 检查语言包是否已注册
 */
export const isLocaleRegistered = (localeCode: string): boolean => {
  const mappedLocale = getI18nCountriesLocale(localeCode);
  return registeredLocales.has(mappedLocale);
};

/**
 * 获取已注册的语言包列表
 */
export const getRegisteredLocales = (): string[] => {
  return Array.from(registeredLocales);
};

/**
 * 初始化常用语言包
 */
export const initializeCommonLocales = (): void => {
  const commonLocales = ['en', 'zh'];
  commonLocales.forEach((locale) => {
    registerCountryLocale(locale);
  });
};
