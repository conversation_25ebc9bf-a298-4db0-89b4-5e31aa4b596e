/**
 *  违禁	Contraband
 *  隐秘	Covert
 *  保密	Classified
 *  受限	Restricted
 *  军规级	Mil-Spec Grade
 *  工业级	Industrial Grade
 *  消费级	Consumer Grade
 *  非凡	Extraordinary
 *  卓越	Remarkable
 *  奇异	Exotic
 *  高级	High Grade
 *  普通级	Base Grade
 *  高级	Distinguished
 *  卓越	Exceptional
 *  非凡	Superior
 *  大师	Master
 */
const qualityLevel = {
  contraband: 6,
  covert: 5,
  extraordinary: 5,
  master: 5,
  classified: 4,
  exotic: 4,
  superior: 4,
  restricted: 3,
  remarkable: 3,
  exceptional: 3,
  milspecgrade: 2,
  highgrade: 2,
  distinguished: 2,
  industrialgrade: 1,
  consumergrade: 0,
  basegrade: 0,
};
const qualityColor = {
  e4ae39: 6,
  eb4b4b: 5,
  d32ce6: 4,
  '8847ff': 3,
  '4b69ff': 2,
  '5e98d9': 1,
  b0c3d9: 0,
};

interface RarityTags {
  title?: string;
  color?: string;
}

/**
 * 获取物品品质等级
 * @param tags 物品标签信息
 * @returns 品质等级 0-6
 */
export function getQualityLevel(tags?: { rarity?: RarityTags }): number {
  const rarity = tags?.rarity?.title || '';
  const rarityColor = tags?.rarity?.color || '';

  if (!rarity && !rarityColor) return 0;

  let level = 0;
  if (rarity) {
    const rarityTitle = rarity
      .toLowerCase()
      .replaceAll(' ', '')
      .replaceAll('-', '');
    level = qualityLevel[rarityTitle as keyof typeof qualityLevel] || 0;
  } else {
    const colorFormat = rarityColor.toLowerCase();
    level = qualityColor[colorFormat as keyof typeof qualityColor] || 0;
  }

  return level;
}
