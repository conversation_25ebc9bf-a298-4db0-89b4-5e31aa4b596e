import { DOMAIN_CONFIG } from '~/constants';
import { RARITY_RANGES } from '@/constants/skin';
// 常量定义
const WEAR_LEVELS = {
  'Factory New': '[FN]',
  'Minimal Wear': '[MW]',
  'Field-Tested': '[FT]',
  'Well-Worn': '[WW]',
  'Battle-Scarred': '[BS]',
};

export const getRarityColor = (value: any): string => {
  const rarity = RARITY_RANGES.find(
    (range: any) => value >= range.min && value <= range.max,
  );
  return rarity ? rarity.color : RARITY_RANGES[0].color;
};

// 稀有度对应的颜色
const RARITY_COLORS = {
  'Consumer Grade': '#b0c3d9',
  'Industrial Grade': '#5e98d9',
  'Mil-Spec Grade': '#4b69ff',
  Restricted: '#8847ff',
  Classified: '#d32ce6',
  Covert: '#eb4b4b',
  Contraband: '#e4ae39',
};

// 获取物品稀有度颜色
export function getItemRarityColor(item: {
  rarity_name?: keyof typeof RARITY_COLORS;
}): string {
  if (!item?.rarity_name) return RARITY_COLORS['Consumer Grade'];
  return RARITY_COLORS[item.rarity_name] || RARITY_COLORS['Consumer Grade'];
}

// 获取物品磨损显示
export function getWearName(wear: string) {
  return wear && `[${WEAR_LEVELS[wear as keyof typeof WEAR_LEVELS]}]`;
}

// 获取物品名称
export function getItemRarityName(item: any) {
  if (!item) return '';

  const star = item.has_star ? '★ ' : '';
  const stattrak = item.is_stattrak ? 'StatTrak™ ' : '';
  const souvenir = item.is_souvenir ? 'Souvenir ' : '';
  const itemType = item.item_type ? item.item_type : '';

  return `${star}${stattrak}${souvenir}${itemType}`;
}

// 获取物品图片URL
export function getItemImageUrl(
  isCustom: boolean,
  imageUrl: string,
  size = '85x68',
) {
  if (!imageUrl) return '';
  return isCustom
    ? `${DOMAIN_CONFIG.CLOUDFLARE_CDN_URL}${imageUrl}/x300`
    : `${DOMAIN_CONFIG.STEAM_CDN_URL}${imageUrl}/${size}`;
}

// 获取物品发光样式
export function getItemGlowStyle(price: number) {
  if (price >= 1000) {
    return 'radial-gradient(50% 50% at 50% 50%, #EB4B4B 0%, rgba(235, 75, 75, 0) 100%)';
  }

  if (price >= 500) {
    return 'radial-gradient(50% 50% at 50% 50%, #8847FF 0%, rgba(136, 71, 255, 0) 100%)';
  }

  if (price >= 100) {
    return 'radial-gradient(50% 50% at 50% 50%, #4B69FF 0%, rgba(75, 105, 255, 0) 100%)';
  }

  if (price >= 50) {
    return 'radial-gradient(50% 50% at 50% 50%, #8847FF 0%, rgba(136, 71, 255, 0) 100%)';
  }

  if (price >= 10) {
    return 'radial-gradient(50% 50% at 50% 50%, #D32CE6 0%, rgba(211, 44, 230, 0) 100%)';
  }

  return 'radial-gradient(50% 50% at 50% 50%, #B0C3D9 0%, rgba(176, 195, 217, 0) 100%)';
}

export default {
  getWearName,
  getItemRarityName,
  getItemImageUrl,
  getItemGlowStyle,
  getItemRarityColor,
  getRarityColor,
};
