import BigNumber from 'bignumber.js';

/**
 * 格式化数字
 * @param value - 要格式化的数值
 * @param useGrouping - 是否使用千分位分组（默认为true）
 * @param fractionDigits - 小数位数（默认为2）
 * @param locale - 区域设置（可选）
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (
  value: number,
  useGrouping: boolean = true,
  fractionDigits: number = 2,
  locale?: string,
): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: fractionDigits,
    maximumFractionDigits: fractionDigits,
    useGrouping,
  }).format(value ?? 0);
};

/**
 * 处理数字字符串,移除多余的分隔符并转换为数字
 * @param value - 要处理的数字字符串
 * @returns 转换后的数字
 */
export const handleNumberString = (value: string | number): number => {
  if (!value) return 0;

  let str = value.toString();

  // 移除末尾的非数字字符
  str = str.replace(/\D+$/, '');

  // 处理多个小数点或逗号的情况
  if (str.split('.').length >= 3 || str.split(',').length >= 3) {
    str = str.replace(/[.,](?!.{2,})/, '');
  }

  // 最后一个分隔符的位置
  const separators = [',', '.'];
  let lastSepIndex = -1;
  for (let i = 0; i < separators.length; i++) {
    const index = str.lastIndexOf(separators[i]);
    if (index > lastSepIndex) {
      lastSepIndex = index;
    }
  }

  if (lastSepIndex === -1) {
    lastSepIndex = str.length;
  }

  // 分割整数和小数部分
  let integerPart = str.substring(0, lastSepIndex);
  let decimalPart = str.substring(lastSepIndex);

  // 移除所有分隔符
  for (let i = 0; i < separators.length; i++) {
    integerPart = integerPart.replace(separators[i], '');
    decimalPart = decimalPart.replace(separators[i], '.');
  }

  str = integerPart + decimalPart;

  // 处理前导零的情况
  if (/^0[0-9]*.$/.test(str)) {
    str = str.split('0').join('');
    str = `.${str}`;
  }

  const num = parseFloat(str);
  return isNaN(num) ? 0 : num;
};

export const formatCurrencyAmount = (
  amount: number | string,
  roundingMode: BigNumber.RoundingMode = BigNumber.ROUND_HALF_UP,
): number => {
  return roundNumber(amount, 2, roundingMode);
};

export const roundNumber = (
  value: number | string,
  precision: number,
  mode: BigNumber.RoundingMode = BigNumber.ROUND_DOWN,
): number => {
  return new BigNumber(value).dp(precision, mode).toNumber();
};

/**
 * 对数字进行舍入处理
 * @param value - 要舍入的数值
 * @param precision - 精确到小数点后几位
 * @returns 舍入后的数值
 */
// export function roundNumber(value: number, precision: number = 2): number {
//   if (!value) return 0;
//   const multiplier = Math.pow(10, precision);
//   return Math.round(value * multiplier) / multiplier;
// }

/**
 * 货币转换工具对象
 */
export const CurrencyConverter = {
  /**
   * 将金额字符串转换为货币对象
   * @param amount - 金额字符串
   * @param currency - 货币类型
   * @returns 包含金额和货币类型的对象
   */
  fromAmount(amount: string, currency: string) {
    return {
      amount: parseFloat(amount),
      currency,
    };
  },
};

/**
 * 获取汇率转换器
 * @param { base, rates }   base 基准货币, rates 汇率映射
 * @returns
 */
export const getExchangeRateConverter = ({
  base,
  rates,
}: {
  base: string;
  rates: Record<string, number>;
}) => {
  const rateMap = { ...rates };

  const getRate = (currency: string) => {
    if (rateMap[currency] === undefined) {
      throw new Error(`不可用的货币: ${currency}`);
    }
    return currency === base ? 1 : rateMap[currency];
  };

  return (
    amount: { amount: number; currency: string },
    targetCurrency: string,
  ) => {
    return {
      amount:
        (amount.amount / getRate(amount.currency)) * getRate(targetCurrency),
      currency: targetCurrency,
    };
  };
};
