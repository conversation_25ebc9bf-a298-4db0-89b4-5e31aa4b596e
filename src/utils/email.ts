/**
 * 格式化邮箱地址，隐藏字符
 *
 * - 格式：1*** 或 12*** 或 123***@domain.com
 *
 * @param email 原始邮箱地址
 * @returns 格式化后的邮箱地址
 *
 *
 **/
export function formatEmail(email: string): string {
  if (!email || !email.includes('@')) {
    return email;
  }
  const [localPart, domain] = email.split('@');
  if (localPart.length <= 3) {
    return `${localPart.charAt(0)}***@${domain}`;
  } else {
    return `${localPart.substring(0, 3)}***@${domain}`;
  }
}
