export function colorTextRender(
  text?: string | number,
  opt?: {
    color?: string;
    style?: object;
    className?: string;
    disabled?: boolean;
    onClick?: () => void;
  },
) {
  const { color, style, className, onClick, disabled } = opt || {};

  const cursor = disabled ? 'not-allowed' : onClick ? 'pointer' : null;
  return h(
    'span',
    {
      style: {
        color: disabled ? '#A0AEC0' : color,
        cursor,
        ...style,
      },
      class: className,
      onClick: (e: MouseEvent) => {
        if (disabled) {
          e.stopPropagation();
          e.preventDefault();
          return;
        }
        onClick?.();
      },
    },
    text || '--',
  );
}
