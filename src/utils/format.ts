// 格式化价格
export function formatPrice(price: number): string {
  if (!price && price !== 0) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price / 100);
}

// 格式化时间
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 货币相关类型定义
export interface CurrencyAmount {
  amount: number;
  currency: string;
}

export interface ExchangeRates {
  base: string;
  rates: Record<string, number>;
}

export class CurrencyUtils {
  // 格式化金额显示
  static formatAmount(
    amount: number | string,
    currency: string,
    locale = 'en-US',
  ): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(parseFloat(String(amount)));
  }

  // 货币转换
  static convert(
    amount: number | string,
    fromCurrency: string,
    targetCurrency: string,
    rates: ExchangeRates,
  ): CurrencyAmount {
    const getRate = (currency: string): number => {
      if (currency === rates.base) return 1;
      const rate = rates.rates[currency];
      if (rate === undefined) {
        throw new Error(`不支持的货币类型: ${currency}`);
      }
      return rate;
    };

    const amountValue = parseFloat(String(amount));
    return {
      amount: (amountValue / getRate(fromCurrency)) * getRate(targetCurrency),
      currency: targetCurrency,
    };
  }
}

// 将文本转换为驼峰命名
export const toCamelCase = (text: string) => {
  const parts = text.split(/(\W+)/);
  let wordCount = 0;
  for (let i = 0; i < parts.length; i++) {
    if (!parts[i].trim()) continue;
    if (/^\W+$/.test(parts[i])) continue;

    wordCount++;

    parts[i] = parts[i].toLowerCase();

    if (wordCount === 1) {
      parts[i] = parts[i].charAt(0).toUpperCase() + parts[i].slice(1);
    } else {
      parts[i] = parts[i].charAt(0).toUpperCase() + parts[i].slice(1);
    }
  }
  return parts.join('');
};
