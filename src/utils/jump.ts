let popupWindow: Window | null = null;

const popupOpenLink = (url: string) => {
  popupWindow = window.open(
    url,
    'NewWindow',
    'width=800,height=1000,left=0,top=0',
  );
  return popupWindow;
};

const closePopupWindow = () => {
  if (popupWindow && !popupWindow.closed) {
    popupWindow.close();
    popupWindow = null;
  }
  return true;
};

const openNewPage = (url: string) => {
  return window.open(url, 'NewWindow');
};

export { popupOpenLink, openNewPage, closePopupWindow };
