export function byteLength(str?: string) {
  if (!str) return 0;
  return new Blob([str]).size;
}

export function cuteToBytes(str?: string, length?: number) {
  if (!length || !str) return str;
  let bytes = 0;
  let result = '';
  for (const char of str) {
    // 计算字符所占字节数（UTF-8）
    const charCode = new Blob([char]).size; // 支持处理 Unicode 扩展字符
    // 如果当前字节数超出限制，停止
    if (bytes + charCode > length) break;

    // 累加字节数，拼接字符
    bytes += charCode;
    result += char;
  }
  return result;
}
