import type { NavigationGuard, RouteLocationNormalized } from 'vue-router';

enum EmailVerificationStatus {
  SUCCESS = '#SUCCESS',
  TIMEOUT = '#TIMEOUT',
}

const STATUS_MESSAGES = {
  [EmailVerificationStatus.SUCCESS]: {
    type: 'success',
    content: 'email_address_verification_successful',
  },
  [EmailVerificationStatus.TIMEOUT]: {
    type: 'error',
    content: 'the_link_is_no_longer_valid',
  },
};

export default defineNuxtRouteMiddleware(
  (
    _to: RouteLocationNormalized,
    _from: RouteLocationNormalized,
  ): ReturnType<NavigationGuard> => {
    if (import.meta.client) {
      const hash = window.location.hash;
      const toast = useAppToast();
      const { $i18n } = useNuxtApp();
      const { t } = $i18n;
      const statusInfo = STATUS_MESSAGES[hash as EmailVerificationStatus];
      if (statusInfo) {
        toast[statusInfo.type as keyof typeof toast]({
          content: t(statusInfo.content),
          duration: 3000,
        });
        setTimeout(() => {
          window.history.replaceState({}, '', window.location.pathname);
        });
      }
    }
    return true;
  },
);
