{"verification_failed": "Verification failed, please refresh the page and try again", "default_sorting": "Default sorting", "insufficient_balance": "Insufficient balance", "recharge_confirmation": "Insufficient balance, do you want to recharge?", "back": "BACK", "open": "OPEN", "provably_fair": "Provably Fair", "sound_on": "Sound On", "sound_off": "Sound Off", "experiment": "EXPERIMENT", "convert": "CONVERT", "convert_all": "CONVERT ALL", "converted": "CONVERTED", "search": "Search", "confirm_conversion": "Confirm Conversion", "confirm_activation": "Confirm Activation", "case_offline": "This case is offline and cannot be opened", "skin_convert_confirmation": "Are you sure to change the skin pack to <span class='text-theme-color'>{a}</span> coins? After conversion, you will lose the skin.", "open_case_confirmation": "Are you sure you want to consume <span class='text-theme-color'>{a}</span> to open the case?", "open_case_with_key_confirmation": "Are you sure you want to consume <span class='text-theme-color'>1 {b} Key</span> to open the case?", "choose_your_spot": "Choose your spot", "lucky_items": "LUCKY ITEMS", "case_contains": "CASE CONTAINS", "no_corresponding_case_found": "No corresponding case found", "insufficient_keys": "Operation failed, insufficient number of keys", "chance": "CHANCE", "range": "RANGE", "show_games": "Show Games", "price_range": "Price Range", "my_games": "MY GAMES", "open_games": "OPEN GAMES", "side": "Side", "create": "CREATE", "one_game": "1 GAME | {a} GAMES", "game": "GAME | GAMES", "clear": "CLEAR", "history": "HISTORY", "amount": "Amount", "cancel": "CANCEL", "confirm": "Confirm", "join_game": "JOIN GAME", "join_bot": "JOIN BOT", "replicating_success": "Replicating Success", "share_link_copied_successfully": "Share link copied successfully", "spend_coinflip_confirmation": "Are you sure you want to consume <span class='text-theme-color'>{price}</span> to coinflip?", "round_id": "Round ID", "coinflip_fairness": "Coinflip uses a provably fair system in which the public seed is not known until after a coinflip game has started.", "filters": "Filters", "select_bet_side": "Please choose the heads or tails for the bet.", "enter_bet": "Please enter the betting amount.", "min_bet": "The minimum bet amount is 1.", "exchange": "Exchange", "my_skin": "My Skin", "extracting": "Extracting", "backpack_record": "Backpack Record", "no_more": "No more", "buy": "Buy", "exchange_successful": "Exchange successful", "check_backpack": "Do you want to check or retrieve your backpack?", "go": "Go", "confirm_exchange": "Confirm exchange", "exchange_item_confirm": "Are you sure to exchange this item?After successful redemption, you can go to the backpack to retrieve it.", "not_displaying_prompts_within_1_day": "Not displaying prompts within 1 day", "confirm_extraction": "Confirm Extraction", "check_steam_connection": "When extracting items, please ensure that the Steam trading connection is correct!", "withdraw": "WITHDRAW", "waiting_for_shipment": "Waiting for shipment", "extraction_successful": "Extraction successful", "extraction_failed": "Extraction failed", "cancel_successful": "Cancel successful", "waiting_for_acceptance_of_quotation": "Waiting for acceptance of quotation", "obtain_records": "Obtain Records", "lost_records": "Lost Records", "extracting_progress": "Extracting Progress", "extracting_records": "Extracting Records", "enter_skin_name_to_search": "Enter skin name to search", "precautions": "Precautions", "i_understand": "I Understand", "steam_settings": "Steam Settings", "update_steam_trade_link": "Update Steam trade link", "trade_link_required": "Without the correct link, you can't trade, sell, or withdraw items because we cannot send you trade offers.", "enter_link": "Enter link", "enter_skin_name_or_order_id_to_search": "Enter skin name or order ID to search", "enter_skin_name_or_id_to_search": "Enter skin name or ID to search", "you_refused_the_quotation": "You refused the quotation", "this_skin_is_currently_out_of_stock": "This skin is currently out of stock", "steam_account_abnormal": "Your Steam account status is abnormal", "steam_account_error": "Your Steam account status is abnormal, causing us to be unable to send you quotes. Please check the trading connection you have set up!", "quotation_expired": "You did not accept the quotation in a timely manner", "skin": "Skin", "type": "Type", "date": "Date", "describe": "Describe", "access": "Access", "no_record_found": "No Record Found", "extraction_sent": "Extraction request sent successfully, please keep an eye on the order status at all times!", "convertible": "Convertible", "exterior": "Exterior", "remaining_quantity": "Remaining Quantity", "recently_obtained": "Recently obtained", "high_value": "High value", "low_value": "Low value", "profile": "Profile", "bonus_cases": "Bonus Cases", "referrals": "Referrals", "deposit": "DEPOSIT", "coin_record": "COIN RECORD", "my_coin_quantity": "My Coin Quantity", "user_info": "User Info", "username": "Username", "steam_id": "Steam ID", "steam_trade_link": "Steam trade link", "get_link": "Get link", "email": "Email", "apply": "Apply", "enter_your_email": "Enter your Email", "please_enter_your_username": "Please enter your username", "please_enter_your_new_nickname": "Please enter your new nickname", "application_successful": "Application successful!", "username_rule": "Within 30 bytes in length, it can contain alphanumeric characters(a-z, A-Z, 0-9), underscores(_),hyphens(-),and periods(.)", "email_rule": "Please enter the email address you need to set up, and the verification email will be sent to that email address.", "the_email_address_entered_is_incorrect": "The email address entered is incorrect", "email_sent": "The email has been sent to this email address, please check it carefully.", "the_link_is_no_longer_valid": "The link is no longer valid.", "email_address_verification_successful": "Email address verification successful.", "set_up_email": "Set up Email", "change_username": "Change Username", "my_level": "My Level", "xp_breakdown": "Xp Breakdown", "xp_breakdown_description": "Get 1 experience point for every 0.01 coins used", "tier": "TIER", "level": "LEVEL", "total_xp": "Total XP", "level_xp": "Level XP", "reward": "<PERSON><PERSON>", "deposit_receive_info": "You will receive balance autimatically after sending {a} to the address displayed below.", "verify_deposit_min": "Please verify your deposit address before each transaction.The current minimum deposit for {a} is {b}", "sign_in": "Sign in", "please_log_in_and_use": "Please log in and use!", "sign_in_with_steam": "SIGN IN WITH STEAM", "do_you_wish_to_log_out": "Do you wish to log out?", "logout_from_all_devices": "Logout from all devices", "round": "Round", "battles": "Battles", "battle_value": "Battle Value", "highest_price_first": "Highest price first", "lowest_price_first": "Lowest price first", "popular_sorting": "Popular sorting", "newest_first": "Newest first", "oldest_first": "Oldest first", "create_battle": "Create Battle", "standard_battle": "Standard Battle", "a_of_b": "{a} of {b}", "view_battle": "View Battle", "join_battle": "Join Battle", "finished": "Finished", "recreate_battle": "Recreate Battle", "winning_value": "Winning Value", "modify_battle": "Modify Battle", "waiting_for_a_player": "Waiting for a player", "bot": "Bot", "cases_selected": "Cases selected", "add_cases": "Add Cases", "uno_reverse_mode": "Uno Reverse Mode", "private_battle": "Private Battle", "private_link_only": "Only players with the link are able to join/view", "reverse_luck_rule": "Reverse your luck. Whoever unboxes the lowest total value of items gets to win it all!", "add": "Add", "category": "Category", "total_cost": "Total Cost", "created_successfully": "Created successfully！", "ready": "Ready", "awaiting_player": "Awaiting Player...", "join_successfully": "Join successfully!", "cancel_the_battle": "Cancel the battle", "exit": "Exit", "exit_successful": "Exit successful!", "game_starting": "Game Starting", "empty_battles_description": "Your case battles will appear here. Start by creating one!", "cancelled": "Cancelled", "my_battles": "My Battles", "active_battles": "Active Battles", "back_to_active_battles": "Back to Active Battles", "server_seed": "Server Seed", "private_seed": "Private Seed", "eos_block_seed": "EOS Block Seed", "server_seed_hash": "Server Seed <PERSON>", "battle_id": "Battle ID", "eos_block_id": "EOS Block ID:", "public_eos_seed": "Public EOS seed", "public_hash": "Public hash", "this_battle_was_cancelled": "This battle was cancelled", "cancel_battle_confirm": "Are you sure to cancel this battle? If other players have joined, they will also be cancelled at the same time.", "exit_battle_confirm": "Are you sure you want to withdraw from this battle? After exiting, you can choose to join again.", "confirm_cancel": "Confirm Cancel", "confirm_exit": "Confirm Exit", "confirm_join": "Confirm Join", "join_battle_confirm": "Are you sure to spend <span class='text-theme-color'>{a}</span> coins to join this battle game?", "share_battle": "Share Battle", "click_to_copy_to_clipboard": "Click to copy to clipboard", "conversion_successful_obtain_a_coins": "Conversion successful, obtain {a} coins", "battle_cancel_success": "The battle has been successfully cancelled!", "joinable": "Joinable", "please_select_the_cases_for_the_battle": "Please select the cases for the battle.", "broadcast": "Broadcast", "new": "NEW", "hot": "HOT", "case": "CASE", "more": "MORE", "abreast_of_the_times": "Abreast of the times", "the_most_valuable": "The most valuable", "enter_room_id_or_name_to_search": "Enter room ID or name to search", "all": "All", "current": "CURRENT", "ended": "ENDED", "joined": "JOINED", "unstart": "UnStart", "have_joined": "HAVE JOINED", "join": "JOIN", "conditions": "CONDITIONS", "end_time": "END TIME", "giveaway_id": "Giveaway ID", "public": "PUBLIC", "join_free_competition": "Join free competition", "no_data": "No Data", "day": "Day | Days", "hour": "Hour | Hours", "min": "Min", "sec": "Sec", "participants": "PARTICIPANTS", "view": "VIEW", "message": "Message", "unread": "Unread", "mark_all_as_read": "MARK ALL AS READ", "operation_successful": "Operation successful", "no_unread_messages": "There are currently no unread messages", "no_news": "No news at the moment", "free_case": "Free case", "fairness": "Fairness", "about": "About", "language": "Language", "home": "HOME", "cases": "CASES", "case_battles": "CASE BATTLES", "coinflip": "COINFLIP", "roll": "ROLL", "notifications": "Notifications", "backpack": "Backpack", "coin_purse": "Coin Purse", "log_out": "Log out", "info": "Info", "about_us": "About us", "support": "Support", "agreement_terms": "Agreement Terms", "terms_of_service": "Terms Of Service", "refund_policy": "Refund Policy", "privacy_policy": "Privacy Policy", "client_seed": "<PERSON><PERSON> Seed", "save": "SAVE", "regenerate": "REGENERATE", "nonce": "<PERSON><PERSON>", "result": "Result", "win": "Win", "game_id": "Game ID", "bet": "Bet", "you_side": "You Side", "public_seed": "Public Seed", "public_seed_hash": "Public Seed Hash", "private_seed_hash": "Private Seed Hash", "lose": "Lose", "details": "Details", "case_battle_details": "Case Battle Details", "user": "User", "item": "<PERSON><PERSON>", "value": "Value", "tiebreaker": "Tiebreaker", "ticket_quantity": "ticketQuantity", "ticket": "Ticket", "prnumber": "PRNumber", "plnumber": "PLNumber", "your_location": "You Location", "view_roll": "View ROLL", "crypto": "CRYPTO", "gift_card": "GIFT CARD", "bank_deposit": "BANK DEPOSIT", "success": "Success", "failed": "Failed", "perform": "Perform", "withdraw_record": "Withdraw Record", "all_method": "All Method", "crypto_deposit": "Crypto Deposit", "gift_card_deposit": "Gift Card Deposit", "crypto_withdraw": "Crypto Withdraw", "other": "Other", "enter_wallet_address": "Please enter the {a} wallet address you want the withdrawal to be sent to.All {a} withdrawals are sent instantly.", "exchange_rate_info": "The exchange rate shown above is an estimate. The final rate is determined by the time of the transaction", "receiving_crypto_address": "Receiving {a} Address", "deposit_value_in_crypto": "Deposit Value in {a}", "paste_your_withdrawal_address_here": "Paste your withdrawal address here.", "withdrawal_of_coins": "Withdrawal of coins", "enter_withdrawal_amount": "Please enter the number of coins for withdrawal", "coin_value": "Coin value", "network_fee": "Network Fee", "approximate_total_to_receive": "Approximate Total to Receive", "withdraw_limits_description": "Please confirm your withdrawal address before making a withdrawal. The minimum for a single withdrawal is 10 coins, and the maximum is 1000 coins.", "request_withdrawal": "Request Withdrawal", "method": "Method", "coins": "Coins", "balance": "Balance", "state": "State", "invitation_link_description": "After registering through your invitation link, you will receive {b}% coins as a reward when the inviter recharges and places bets within {a} days! Copy the invitation address and send it to your friends immediately!", "affiliates": "Affiliates", "overview": "Overview", "total_earnings": "Total Earnings", "available_earnings": "Available Earnings", "claim": "<PERSON><PERSON><PERSON>", "commission_details": "Commission Details", "your_referred_users": "Your Referred Users", "name": "Name", "commission": "Commission", "time": "Time", "affiliates_time": "Affiliates Time", "total_commission": "Total Commission", "claim_success": "<PERSON>lai<PERSON>", "current_level": "Current Level", "next_level": "Next Level", "fairness?": "How can I know that the game is fair?", "fairness_p_1": "The game results have been generated BEFORE you even place your start, and most importantly, we can prove it.", "fairness_p_2": "Before each round, we actually give you the round result in a hashed format.", "fairness_p_3": "Before the round, we give you the hash of the secret seed which the round result is based on. After the round ends, we publish the secret seed, which you can then compare to the hash to make sure that they match. With this system we can prove that the results were fair and pre-generated.", "fairness_block_p_1": "For each verifiable game, a client seed, a server seed and a nonce are used as the input parameters.", "fairness_block_clinet_seed_info": "This is a passphrase or a randomly generated string that is determined by the player or their browser. This can be edited and changed regularly by yourself.", "randomly_new": "Randomly new", "successfully_saved": "Successfully saved", "client_seed_length": "Please enter 32 valid characters.", "client_seed_rule": "Only uppercase and lowercase letters and numbers can be entered.", "fairness_lock_server_seed_info": "To reveal the hashed server seed, the seed must be rotated by the player, which triggers the replacement with a newly generated seed. From this point you are able to verify any bets made with the previous server seed to verify both the legitimacy of the server seed with the encrypted hash that was provided.", "log_in_to_view_server_seed": "<span class=\"cursor-pointer text-theme-color\">Log in</span> to view Server Seed", "fairness_block_server_seed_validate": "You can validate hashed server seed using this <span class=\"cursor-pointer text-theme-color\">Golang Code</span>. The hashed server seed is a SHA-256 hash of the seed so after you unhash it, you can check that it matches with the hashed version.", "obtained_a_new_server_seed": "Obtained a new server seed.", "cases_technical_details": "Cases technical details", "fairness_case_info": "The result for each case opening is generated by the following logic.", "fairness_case_server_seed": "<span class=\"text-white\">Server Seed：</span>The server seed is a cryptographically secure pseudo-randomly generated string.", "fairness_case_nonce": "<span class=\"text-white\">Nonce：</span>The nonce is a number that is incremented every time the server seed is used.", "fairness_case_client_seed": "<span class=\"text-white\">Client Seed：</span>The client seed is a user-defined (or random by default) string. You can modify or re randomize on this page.", "fairness_case_result_rule": "The pre-result is calculated based on unhashed server seed, client seed and nonce.Then we pick a result between 1 and qty, where the qty is the total number of tickets (determined by the chosen case). Every item in the case has its own number range, which is used to pick the winning item based on the result.", "fairness_case_golang_code": "You can replicate any past case-opening result yourself using this <span class=\"cursor-pointer text-theme-color\">Golang Code</span>. Please note that you should use the <b class=\"text-[#1A9DFF] font-normal\">unhashed</b> (not <b class=\"text-[#FF5555] font-normal\">hashed</b>) server seed with the script.", "fairness_case_regenerate_server_seed": "You can get the unhashed server seed by tab and clicking \"Regenerate\" under Server Seed. When a new Server Seed is generated, the nonce resets back to 1.", "cases_record": "Cases Record", "log_in_to_view_records": "<span class=\"cursor-pointer text-theme-color mr-1\">Log in</span> to view records", "hashed_server_seed": "Hashed server seed", "unhashed_server_seed": "Unhashed server seed", "case_battles_technical_details": "Case battles technical details", "fairness_battle_info": "CaseBattles uses a provably fair system in which the public seed is not known until after a battle game has started. The result for each round is generated using the SHA-256 hash of 4 separate inputs.", "cases_battles_record": "Cases Battles Record", "fairness_battle_server_seed": "<span class=\"text-white\">Server Seed：</span>The server seed is a securely random value generated when a battle is created. The SHA-256 hash of the server seed is immediately shown to all players after the battle creation. Players can verify the server seed revealed following the battle result matches this SHA-256 hash.", "fairness_battle_public_seed": "<span class=\"text-white\">Public Seed：</span>The public seed is the ID of an EOS block, chosen to be generated after the final challenger joins a battle.", "fairness_battle_EOS": "Once the last player joins, our system selects an EOS blockchain block number that will be produced in the near future. The ID of this block serves as the public seed. This ensures that neither the players nor our system can predict the data that will determine the items that are pulled from each case in the battle rounds until all players have committed their bets.", "fairness_battle_round_number": "<span class=\"text-white\">Round Number：</span>The rounds of the battle.", "fairness_battle_player_position": "<span class=\"text-white\">Player Position：</span>Player\"s position- (1 to 4).Please note that the order of the players shown on the table is the same as in the battle page when you joined the battle, and that indicates the player position for the seed.", "fairness_battle_tie": "In the event of a tie, the system initiates an additional Tiebreaker round.", "fairness_battle_tiebreaker": "The Tiebreaker round\"s result is calculated using the SHA-256 hash of 3 distinct inputs:<span class=\"text-white\">The \"server seed\"、The \"public seed\"、Last Round Number + 1</span>", "fairness_battle_tickets": "And the number of tickets is determined by how many people are participating on the tiebreaker.", "fairness_battle_position": "Your position on the tiebreaker is in ascending order. As an example, if 3 people are in a tiebreaker, the assigned ticket goes from left to right. This means that if players in position 2 and 3 are in a tiebreaker, the ticket number 1 is assigned to player 2 and the ticket number 2 is assigned to player 3.", "fairness_game_golang_code": "You can replicate any past result yourself using this <span class=\"cursor-pointer text-theme-color\">Golang Code</span>.", "coinflip_technical_details": "Coinflip technical details", "fairness_coinflip_info": "Coinflip uses a provably fair system in which the public seed is not known until after a coinflip game has started. The result for each round is generated using the SHA-256 hash of 3 separate inputs.", "fairness_coinflip_private_seed": "<span class=\"text-white\">Private Seed：</span>The private seed is a securely random value, generated when a round is created. The SHA-256 hash of the private seed is displayed to all players immediately after a round is created. Players can check that the private seed revealed after the coinflip result is made known matches this SHA-256 hash.", "fairness_coinflip_public_seed": "<span class=\"text-white\">Public Seed：</span>The public seed is the ID of an EOS block, which is to be generated after a round is joined by a challenger. When a round is joined, our system chooses a block number on the EOS blockchain that will be generated in the near future. The ID of this block is what will be used as the public seed. This way, neither the players nor our system know what data will be used to generate the coinflip result until after both players have committed their bets.", "fairness_coinflip_game_ID": "<span class=\"text-white\">Game ID：</span>Unique ID for Coinflip game.", "coinflip_record": "Coinflip Record", "roll_technical_details": "Roll technical details", "fairness_roll_info": "ROLL uses a provable fairness system, in which a SHA-256 hash is generated at the beginning of the game to generate a Public Seed hash, and at the end of the game, each user\"s number is generated based on the Public Seed and user joining order. If the user\"s number is the same as the prize number, they win.", "fairness_roll_public_seed": "<span class=\"text-white\">Public Seed：</span>The safe random value generated at the beginning of the ROLL game. The SHA-256 hash value of Public Seed is immediately displayed to all players at the beginning of the game. Players can check whether the published Public Seed matches this SHA-256 hash value after the ROLL ends.", "fairness_roll_prize_ID": "<span class=\"text-white\">Prize ID：</span>The ID of the prize is displayed to all players at the beginning of the game.", "fairness_roll_PRNumber": "<span class=\"text-white\">PRNumber：</span>The total number of prizes.", "fairness_roll_location": "<span class=\"text-white\">Location：</span>Depending on the order in which players join the ROLL, starting from 1.", "fairness_roll_PLNumber": "<span class=\"text-white\">PLNumber：</span>The total number of players who have successfully participated in ROLL.", "fairness_roll_win": "At the end of the ROLL, the system will generate a number for each user based on PublicSeed, location, PRNumber, or PLNumber (whichever is greater). When this number matches the prize number, the user will win.", "fairness_roll_rule": "The same player in each ROLL can win at most once. If the number of participating players is less than the number of ROLL prizes, some prizes may not have winning users.", "roll_record": "Roll Record", "enter_your_link": "Enter your link", "copy_failed": "Co<PERSON> failed", "enter_trading_link": "Please enter your Steam trading link.", "deposit_record": "Deposit Record", "obtain_coins": "<PERSON><PERSON>ain Coins", "consuming_coins": "Consuming Coins", "orders_id": "Orders ID", "cases_record_id": "Cases Record ID", "cases_battles_id": "Cases Battles ID", "roll_id": "ROLL ID", "new_case": "New case", "hot_case": "Hot case", "recommend_case": "Recommend case", "do_not_cancel": "Do not cancel", "verify": "Verify", "no_open_coinflip_games": "No open Coinflip games at the moment.", "empty_coinflip_description": "Your Coinflip games will appear here. Start by creating one!", "not_installed": "Not installed", "open_cases": "Open Cases", "create_cases_battle": "Create Cases Battle", "join_cases_battle": "Join <PERSON><PERSON>", "cancel_cases_battle": "Cancel Cases Battle", "exit_cases_battle": "Exit Cases Battle", "create_coinflip": "Create Coinflip", "join_coinflip": "Join <PERSON>", "coinflip_win": "Coinflip Win", "cancel_coinflip": "Cancel Coinflip", "exchange_skin": "Exchange Skin", "open_free_case": "Open Free Case", "open_bonus_cases": "Open Bonus Cases", "precautions_description": "", "coinflip_cancel_success": "Cancellation successful, coins have been returned to your account.", "highest_amount_first": "Highest amount first", "lowest_amount_first": "Lowest amount first", "wearcategory0": "Factory New", "wearcategory1": "<PERSON><PERSON>", "wearcategory2": "Field-Tested", "wearcategory3": "Well-Worn", "wearcategory4": "Battle-<PERSON><PERSON>red", "wearcategoryna": "Not Painted", "knife": "Knives", "gloves": "Gloves", "rifle": "Rifles", "pistol": "Pistols", "smg": "SMGs", "heavy": "Heavy", "stickers": "Stickers", "agents": "Agent", "others": "Other", "total_unboxed": "Total Unboxed", "convert_skin": "Convert Skin", "0": "Success", "11006": "Token valid failed", "11015": "The link is no longer valid.", "11016": "Verification code error", "11017": "The email address entered is incorrect", "22034": "The Steam transaction connection you entered is incorrect, please re-enter.", "22035": "The Steam transaction connection you entered is not consistent with the steam account, please re-enter.", "22036": "Your account has been banned and cannot perform this operation.", "22037": "The nickname format entered is incorrect, please re-enter.", "22038": "Trade url is not bind", "22039": "There are no remaining coins.", "22040": "The nickname has been taken.", "22041": "The avatar cannot be empty.", "22043": "The nickname format you entered contains sensitive content, please re-enter.", "22044": "You are not authorized to log in.", "26201": "The coin balance is insufficient.", "26203": "Wallet data exception", "26204": "This gift code is invalid.", "26208": "This gift code is invalid.", "26209": "Recharge failed, please try again later", "30001": "Skin prices have been updated. Please refresh the page.", "30003": "The accessory is out of stock.", "30005": "skin does not exist", "30006": "Conversion failed, skin not found.", "30007": "Skin does not support cancel extract", "30008": "Operation failed, the status of the skin is incorrect. Please refresh the page and try again.", "33001": "Failed to query EOS block information, please try again later", "33002": "Failed to use server seed.", "401001": "found fail", "401002": "record not found", "401003": "Create fail, please try again.", "401004": "Someone else has already challenged this coinflip.", "401005": "The coinflip has already canceled.", "401008": "The coinflip cannot be cancelled.", "401009": "Challenge fail, game status error.", "401010": "You cannot join the room you have created yourself", "401011": "<PERSON><PERSON> failed, game status error.", "401012": "Failed to join BOT, game status error.", "401013": "The minimum bet amount is1.", "401014": "The highest bet amount is99999.99.", "401016": "The room num create too more.", "401101": "found fail", "401102": "record not found", "401103": "Room off-line", "401104": "The status of the ROLL room is incorrect. Please refresh and try again.", "401105": "Cannot join, you have not met the joining criteria.", "401106": "You have already joined, there is no need to join again.", "401107": "Join Failed.Please refresh and try again.", "401415": "An unknown error has occurred, please refresh and try again.", "403002": "You have already joined the battle!", "403003": "Battle not found.", "403004": "The battle status is abnormal. Please refresh the page and try again.", "403005": "There arena is full, please choose another arena.", "403006": "Join failed, insufficient coin balance!", "403007": "Failed to create EOS block info.", "403008": "Failed to add goods to backpack.", "403009": "The number of players is not enough.", "403010": "The tiebreaker result is abnormal.", "403011": "You do not have the authority to perform this operation.", "403012": "Please select the cases for the battle.", "403013": "There are already players at this location, please choose another location.", "403014": "Case not found", "403015": "<PERSON> is busy, please try again later.", "403016": "Cases type error，please re-select the chest.", "403017": "Cases with abnormal status, please check and retry.", "403018": "Experience level does not match.", "403019": "Operation failed, insufficient number of keys", "403020": "Deduction keys are abnormal.", "403021": "The maximum number of cases that can be selected for the battle is 50.", "request_error": "Request error, please try again later.", "server_exception": "Server exception, please try again later.", "select_all": "Select All", "all_convert_success": "Successfully converted <span class='text-theme-color'>{x}</span> items and obtained <span class='text-theme-color'>{a}</span> coins.", "partial_convert_success": "Successfully converted <span class='text-theme-color'>{x}</span> items and obtained <span class='text-theme-color'>{a}</span> coins. <span class='text-theme-color'>{s}</span> items failed to convert — please check item status", "confirm_all_conversions": "Confirm All Conversions", "all_convert_confirmation": "Are you sure you want to exchange <span class='text-theme-color'>{x}</span> items for <span class='text-theme-color'>{a}</span> coins？", "conversion_results": "Conversion Results", "open_case_speed_ordinary": "ordinary", "open_case_speed_fast": "fast", "open_case_speed_skip": "skip", "open_case_enter": "Or press <span class='text-theme-color'>Enter</span> to open", "confirm_join_guild": "Confirm Joining The Guild", "guild_invites_you": "{a} Guild invites you to join as a promotion anchor. After joining, you can enjoy guild promotion rebates. Please enter the inviter's UID below and confirm your participation.", "before_join_guild_notify": "Before joining, please review the collaboration terms between you and the guild. You may also leave the guild later through your anchor dashboard.", "enter_UID_guild_inviter": "Please enter the UID of the guild inviter", "join_guild_success": "Successfully joined the guild.", "avatar": "Avatar", "password": "Password", "change": "Change", "account_security": "ACCOUNT SECURITY", "bind_email": "Bind <PERSON><PERSON>", "bind_email_login": "Bind your email to enable email login", "set_password": "Set password", "reset_password": "Reset password", "not_set": "Not set", "configured": "Configured", "connect_steam": "Connect Steam", "not_linked": "Not linked", "go_to_link": "Go to Link", "view_steam": "View Steam", "change_avatar": "Upload a new avatar", "choose_avatar": "Choose your preferred avatar from the list", "reset_password_prompt": "Password reset prompt", "reset_password_dialog_content": "Your password has been reset successfully. Please log in again to continue. Confirm to proceed?", "gift_redeem": "gift redeem", "redeem_code": "Promo Code", "redeem_placeholder": "Please enter your redeem code", "code_instruction": "Please enter your redeem code", "congratulations_obtain": "Success! Your prize includes:"}