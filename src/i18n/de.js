export default {
  回合: '回合',
  返回: '返回',
  打开: 'Open',
  // case battles
  对战价值: '对战价值',
  最高价格优先: '最高价格优先',
  最低价格优先: '最低价格优先',
  热门排序: '热门排序',
  最新上架: '最新上架',
  最新创建: '最新创建',
  返回对战: '返回对战',
  创建对战: '创建对战',
  标准对战: '标准对战',
  共享对战: '共享对战',
  团队对战: '团队对战',
  '第{current}轮，共{total}轮': '第{current}轮，共{total}轮',
  关闭: '关闭',
  查看对战: '查看对战',
  加入对战: '加入对战',
  已赢取的金额: '已赢取的金额',
  已损失的金额: '已损失的金额',
  可证明公平性: '可证明公平性',
  声音开启: '声音开启',
  声音关闭: '声音关闭',
  试玩: '试玩',
  '打开 {count} 个': '打开 {count} 个',
  转换: '转换',
  转换全部: '转换全部',
  已转换: '已转换',
  搜索: '搜索',
  未找到相应宝箱: '未找到相应宝箱',
  对战结束: '对战结束',
  重新创建对战: '重新创建对战',
  获胜总金额: '获胜总金额',
  修改对战: '修改对战',
  等待一名玩家: '等待一名玩家',
  机器人: '机器人',
  vs: 'vs',
  铜牌: '铜牌',
  锈: '锈',
  青铜: '青铜',
  银: '银',
  金: '金',
  钻石: '钻石',
  铀: '铀',
  牢不可破: '牢不可破',
  '等级 {rank} {lvl}': '等级 {rank} {lvl}',
  '已选宝箱 {qty}': '已选宝箱 {qty}',
  添加宝箱: '添加宝箱',
  Uno逆转模式: 'Uno逆转模式',
  私人对战: '私人对战',
  '只有拥有链接的玩家才能加入/查看': '只有拥有链接的玩家才能加入/查看',
  '逆转您的运气。开出物品总价值最低的人将赢得全部!':
    '逆转您的运气。开出物品总价值最低的人将赢得全部!',
  '宝箱已下架，无法开启': '宝箱已下架，无法开启',
  确认转换: '确认转换',
  确认开启: '确认开启',
  '您确定要将饰品兑换成{price}金币吗? 兑换后您将失去该饰品。':
    '您确定要将饰品兑换成 <span class="text-theme-color">{price}</span> 金币吗? 兑换后您将失去该饰品。',
  '您确定要花费{price}金币开箱吗?':
    '您确定要花费 <span class="text-theme-color">{price}</span> 金币开箱吗?',
  '您确定要消耗1{name}Key开箱吗?':
    '您确定要消耗<span class="text-theme-color"> 1 {name} Key</span> 开箱吗?',
  '1天内不提示': '1天内不提示',
  添加: '添加',
  类别: '类别',
  排序: '排序',
  幸运物品: 'Lucky items',
  宝箱物品: 'Case contains',

  /** coinflip */
  展示游戏数量: '展示游戏数量',
  价格区间: '价格区间',
  我的游戏: '我的游戏',
  开放游戏: '开放游戏',
  一面: '一面',
  创建: '创建',
  '{count}个游戏': '{count}个游戏',
  游戏: '游戏',
  清除: '清除',
  最大: '最大',
  当前: '当前',
  历史: '历史',
  金额: '金额',
  取消: '取消',
  确认: '确认',
  加入游戏: '加入游戏',
  加入机器人: '加入机器人',
  复制成功: '复制成功',
  '您确定要花费{price}金币抛硬币吗?':
    '您确定要花费 <span class="text-theme-color">{price}</span> 金币抛硬币吗?',
  回合ID: '回合ID',
  区块ID: '区块ID',
  '抛硬币使用可证明公平的系统，其中公共种子在抛硬币游戏开始后才会被知晓。':
    '抛硬币使用可证明公平的系统，其中公共种子在抛硬币游戏开始后才会被知晓。',

  /** 兑换 */
  兑换: '兑换',
  我的饰品: '我的饰品',
  提取: '提取',
  背包记录: '背包记录',
  兑换成功: '兑换成功',

  /** 背包 */
  '你确定要将这个物品转换为{qty}个硬币吗？':
    '你确定要将这个物品转换为{qty}个硬币吗？',
  '1天内不再显示提醒': '1天内不再显示提醒',
  转换成功: '转换成功',
  确认提取: '确认提取',
  '提取物品时，请确保Steam交易连接正确！':
    '提取物品时，请确保Steam交易连接正确！',
  取回: '取回',
  等待发货: '等待发货',
  等待接受报价: '等待接受报价',
  获得记录: '获得记录',
  失去记录: '失去记录',
  提取进度: '提取进度',
  提取记录: '提取记录',
  输入饰品名称搜索: '输入饰品名称搜索',
  /** 筛选 */
  最近获得: '最近获得',
  高价值优先: '高价值优先',
  低价值优先: '低价值优先',

  /** 用户 */
  用户: '用户',
  钱包: '钱包',
  宝箱奖励: '宝箱奖励',
  推荐奖励: '推荐奖励',
  充值: '充值',
  提现: '提现',
  硬币记录: '硬币记录',
  我的硬币数量: '我的硬币数量',
  用户信息: '用户信息',
  用户名: '用户名',
  'Steam ID': 'Steam ID',
  'Steam trade link': 'Steam trade link',
  获取链接: '获取链接',
  Email: '邮箱',
  编辑: '编辑',
  提交: '提交',
  验证: '验证',
  请输入邮箱: '请输入邮箱',
  请输入验证码: '请输入验证码',
  请输入用户名: '请输入用户名',
  用户名规则:
    '在30字符内, 可以包含字母数字字符(a-z, A-Z, 0-9)、下划线(_)、连字符(-)和句点(.)',
  我的等级: '我的等级',
  经验明细: '经验明细',
  经验明细说明: '每消耗0.01个硬币获得1点经验',

  /** 充值 */
  您将在发送BTC后自动收到余额: '您将在发送BTC后自动收到余额',

  /** 对战 */
  '回合 {n}': '回合 {n}',
  准备: '准备',
  '等待玩家...': '等待玩家...',
  取消对战: '取消对战',
  退出: '退出',
  退出成功: '退出成功',
  游戏开始: '游戏开始',
  开箱总数: '开箱总数',
  /** 首页 */
  战报: '战报',
  最新: '最新',
  热门: '热门',
  推荐: '推荐',
  宝箱: '宝箱',
  Roll房推荐: 'Roll房',
  更多: '更多',
  宝箱对战: '宝箱对战',

  /** rollroom */
  时间从大到小: 'Abreast of the times',
  ROLL房价值从大到小: 'The most valuable',
  输入房间ID或名称搜索: '输入房间ID或名称搜索',
  全部: '全部',
  进行中: '进行中',
  已结束: '已结束',
  已加入: '已加入',
  未开始: '未开始',
  已经加入: '已加入',
  加入: '加入',
  参与条件: '参与条件',
  结束时间: '结束时间',
  房间ID: '房间ID',
  公共种子: '公共种子',
  无参与条件: '无参与条件',
  暂无数据: '暂无数据',
  天: '天',
  时: '时',
  分: '分',
  秒: '秒',
  奖品列表: '奖品列表',
  参与用户: '参与用户',

  /** notifications */
  消息: 'Messages',
  未读: 'Unread',
  全部标记为已读: 'Mask all as read',

  /** layout */
  免费宝箱: '免费宝箱-de',
  等级宝箱: '等级宝箱-de',
  头部推荐: '推荐-de',
  公正性: '公正性-de',
  关于我们: '关于我们-de',
  语言: '语言-de',
  首页: '首页-de',
  轮盘: '轮盘-de',
  开箱: '开箱-de',
  开箱对战: '开箱对战-de',
  猜硬币: '猜硬币-de',
  ROLL房: 'ROLL房-de',
  头部兑换: '兑换-de',
  通知: '通知-de',
  背包: '背包-de',
  用户余额: '用户余额-de',
  头部登录: 'Steam登录-de',
  账号详情: '账号详情-de',
  硬币钱包: '硬币钱包-de',
  popover等级宝箱: '等级宝箱-de',
  退出账号: '退出账号-de',
  信息: '信息-de',
  底部关于我们: '关于我们-de',
  支持: '支持-de',
  协议条款: '协议条款-de',
  服务条款: '服务条款-de',
  退款政策: '退款政策-de',
  隐私政策: '隐私政策-de',

  /** 公平性页面 */
  随机新种子: '随机新种子',
  客户端种子: '客户端种子',
  服务器种子: '服务器种子',
  服务器种子小写: '服务器种子',
  服务器种子哈希: '服务器种子哈希',
  保存: '保存',
  重新生成: '重新生成',
  '公平性-登录': '登录',
  '公平性-开箱': '开箱',
  '公平性-开箱对战': '开箱对战',
  '公平性-猜硬币': '猜硬币',
  '公平性-ROLL房': 'Roll房',
  技术细节: '技术细节',
  记录: '记录',
  日期: '日期',
  未哈希: '未哈希',
  哈希: '哈希',
  随机数: '随机数',
  结果: '结果',
  获胜: '获胜',
  'EOS 区块编号': 'EOS 区块编号',
  '公共 EOS 种子': '公共 EOS 种子',
  游戏ID: '游戏ID',
  '公平性-金额': '金额',
  私有种子: '私有种子',
  私有种子哈希: '私有种子哈希',
  你的选择: '选择',
  标题: '标题',
  '公平性-公共种子': '公共种子',
  '公平性-公共种子哈希': '公共种子哈希',
  输: '输',
  赢: '赢',
  详情: '详情',
  等待开奖: '等待开奖',
  开箱对战详情: '开箱对战详情',
  '回合 {count}': '回合 {count}',
  玩家: '玩家',
  饰品: '饰品',
  价值: '价值',
  决胜局: '决胜局',
  参与人数: '参与人数',
  票据: '票据',
  奖品数量: '奖品数量',
  成功用户数量: '成功用户数量',
  位置ID: '位置ID',
  查看Roll房详情: '查看详情',
  '公平性-暂无记录': '暂无记录',
  跳至: '跳至',
  页: '页',

  /** 用户相关新增 */
  链接已成功复制: '链接已成功复制',
  取回成功: '取回成功',
  取回失败: '取回失败',
  Steam交易链接: 'Steam交易链接',
  邮箱规则: '请输入您需要设置的邮箱地址，验证邮件将发送至该邮箱。',
  邮箱验证规则:
    '修改邮箱需要先进行验证，验证码已发送至您的邮箱XXX，请在下方输入邮箱验证码进行验证。',
  设置电子邮件: '设置电子邮件',
  更改用户名: '更改用户名',
  登出: '登出',

  /** 登录/退出登录 */
  登录: '登录',
  请登录并使用: '请登录并使用',
  Steam登录: 'Steam登录',
  退出登录: '退出登录',
  是否退出登录: '是否退出登录?',
  登出所有设备: '登出所有设备',

  /** 对战相关新增 */
  加入战斗成功: '加入战斗成功',
  取消战斗成功: '取消战斗成功',
  '您的宝箱对战将在此处显示,先从创建一个对战开始吧':
    '您的宝箱对战将在此处显示,先从创建一个对战开始吧',
  结束: '结束',
  已取消: '已取消',
  我的对战: '我的对战',
  活跃对战: '活跃对战',
  返回活跃对战: '返回活跃对战',
  '提交于:': '提交于:',
  服务器私有种子: '服务器私有种子',
  'EOS 区块种子': 'EOS 区块种子',
  '生成于:': '生成于:',
  '对战 ID': '对战 ID',
  EOS区块ID: 'EOS区块ID',
  EOS区块种子: 'EOS区块种子',
  'EOS区块种子 ({id})': 'EOS区块种子 ({id})',
  这场对战已被取消: '这场对战已被取消',
  '您确定要取消这场对战吗? 如果其他玩家已经加入, 他们也会同时被取消。':
    '您确定要取消这场对战吗? 如果其他玩家已经加入, 他们也会同时被取消。',
  确认取消: '确认取消',
  确认加入: '确认加入',
  '您确定要花费{c}枚金币来加入这场战斗游戏吗？':
    '您确定要花费{c}枚金币来加入这场战斗游戏吗？',
  分享对战: '分享对战',
  点击复制: '点击复制',

  /** 其他新增 */
  选择你的位置: '选择你的位置',
  对战公平性弹窗描述:
    'CaseBattles使用可证明公平的系统，在战斗游戏开始之前公共种子是未知的。每轮的结果是使用4个独立输入的SHA-256哈希生成的。',
  最早上架: '最早上架',

  /** 等级宝箱 */
  current_level: 'Current Level',
  next_level: 'Next Level',
  /** 公平性文档 */
  'fairness?': 'How can I know that the game is fair?',
  fairness_p_1:
    'The game results have been generated BEFORE you even place your start, and most importantly, we can prove it.',
  fairness_p_2:
    'Before each round, we actually give you the round result in a hashed format.',
  fairness_p_3:
    'Before the round, we give you the hash of the secret seed which the round result is based on. After the round ends, we publish the secret seed, which you can then compare to the hash to make sure that they match. With this system we can prove that the results were fair and pre-generated.',
  fairness_block_p_1:
    'For each verifiable game, a client seed, a server seed and a nonce are used as the input parameters.',
  fairness_block_clinet_seed_info:
    'This is a passphrase or a randomly generated string that is determined by the player or their browser. This can be edited and changed regularly by yourself.',
  randomly_new: 'Randomly new',
  successfully_saved: 'Successfully saved',
  client_seed_length: 'Please enter 32 valid characters.',
  client_seed_rule:
    'Only uppercase and lowercase letters and numbers can be entered.',
  fairness_lock_server_seed_info:
    'To reveal the hashed server seed, the seed must be rotated by the player, which triggers the replacement with a newly generated seed. From this point you are able to verify any bets made with the previous server seed to verify both the legitimacy of the server seed with the encrypted hash that was provided.',
  log_in_to_view_server_seed:
    '<span class="cursor-pointer text-theme-color">Log in</span> to view Server Seed',
  regenerate: 'Regenerate',
  fairness_block_server_seed_validate:
    'You can validate hashed server seed using this <span class="cursor-pointer text-theme-color">Golang Code</span>. The hashed server seed is a SHA-256 hash of the seed so after you unhash it, you can check that it matches with the hashed version.',
  obtained_a_new_server_seed: 'Obtained a new server seed.',
  // 开箱
  cases_technical_details: 'Cases technical details',
  fairness_case_info:
    'The result for each case opening is generated by the following logic.',
  fairness_case_server_seed:
    '<span class="text-white">Server Seed：</span>The server seed is a cryptographically secure pseudo-randomly generated string.',
  fairness_case_nonce:
    '<span class="text-white">Nonce：</span>The nonce is a number that is incremented every time the server seed is used.',
  fairness_case_client_seed:
    '<span class="text-white">Client Seed：</span>The client seed is a user-defined (or random by default) string. You can modify or re randomize on this page.',
  fairness_case_result_rule:
    'The pre-result is calculated based on unhashed server seed, client seed and nonce.Then we pick a result between 1 and qty, where the qty is the total number of tickets (determined by the chosen case). Every item in the case has its own number range, which is used to pick the winning item based on the result.',
  fairness_case_golang_code:
    'You can replicate any past case-opening result yourself using this <span class="cursor-pointer text-theme-color">Golang Code</span>. Please note that you should use the <b class="text-[#1A9DFF] font-normal">unhashed</b> (not <b class="text-[#FF5555] font-normal">hashed</b>) server seed with the script.',
  fairness_case_regenerate_server_seed:
    'You can get the unhashed server seed by tab and clicking "Regenerate" under Server Seed. When a new Server Seed is generated, the nonce resets back to 1.',
  cases_record: 'Cases Record',
  log_in_to_view_records:
    '<span class="cursor-pointer text-theme-color mr-1">Log in</span> to view records',
  hashed_server_seed: 'Hashed server seed',
  unhashed_server_seed: 'Unhashed server seed',
  // 对战
  case_battles_technical_details: 'Case battles technical details',
  fairness_battle_info:
    'CaseBattles uses a provably fair system in which the public seed is not known until after a battle game has started. The result for each round is generated using the SHA-256 hash of 4 separate inputs.',
  cases_battles_record: 'Cases Battles Record',
  fairness_battle_server_seed:
    '<span class="text-white">Server Seed：</span>The server seed is a securely random value generated when a battle is created. The SHA-256 hash of the server seed is immediately shown to all players after the battle creation. Players can verify the server seed revealed following the battle result matches this SHA-256 hash.',
  fairness_battle_public_seed:
    '<span class="text-white">Public Seed：</span>The public seed is the ID of an EOS block, chosen to be generated after the final challenger joins a battle.',
  fairness_battle_EOS:
    'Once the last player joins, our system selects an EOS blockchain block number that will be produced in the near future. The ID of this block serves as the public seed. This ensures that neither the players nor our system can predict the data that will determine the items that are pulled from each case in the battle rounds until all players have committed their bets.',
  fairness_battle_round_number:
    '<span class="text-white">Round Number：</span>The rounds of the battle.',
  fairness_battle_player_position:
    "<span class='text-white'>Player Position：</span>Player's position- (1 to 4).Please note that the order of the players shown on the table is the same as in the battle page when you joined the battle, and that indicates the player position for the seed.",
  fairness_battle_tie:
    'In the event of a tie, the system initiates an additional Tiebreaker round.',
  fairness_battle_tiebreaker: `The Tiebreaker round's result is calculated using the SHA-256 hash of 3 distinct inputs:<span class="text-white">The "server seed"、The "public seed"、Last Round Number + 1</span
  >`,
  fairness_battle_tickets:
    'And the number of tickets is determined by how many people are participating on the tiebreaker.',
  fairness_battle_position:
    'Your position on the tiebreaker is in ascending order. As an example, if 3 people are in a tiebreaker, the assigned ticket goes from left to right. This means that if players in position 2 and 3 are in a tiebreaker, the ticket number 1 is assigned to player 2 and the ticket number 2 is assigned to player 3.',
  fairness_game_golang_code:
    'You can replicate any past result yourself using this <span class="cursor-pointer text-theme-color">Golang Code</span>.',
  // 猜硬币
  coinflip_technical_details: 'Coinflip technical details',
  fairness_coinflip_info:
    'Coinflip uses a provably fair system in which the public seed is not known until after a coinflip game has started. The result for each round is generated using the SHA-256 hash of 3 separate inputs.',
  fairness_coinflip_private_seed:
    '<span class="text-white">Private Seed：</span>The private seed is a securely random value, generated when a round is created. The SHA-256 hash of the private seed is displayed to all players immediately after a round is created. Players can check that the private seed revealed after the coinflip result is made known matches this SHA-256 hash.',
  fairness_coinflip_public_seed:
    '<span class="text-white">Public Seed：</span>The public seed is the ID of an EOS block, which is to be generated after a round is joined by a challenger. When a round is joined, our system chooses a block number on the EOS blockchain that will be generated in the near future. The ID of this block is what will be used as the public seed. This way, neither the players nor our system know what data will be used to generate the coinflip result until after both players have committed their bets.',
  fairness_coinflip_game_ID:
    '<span class="text-white">Game ID：</span>Unique ID for Coinflip game.',
  coinflip_record: 'Coinflip Record',
  // roll
  roll_technical_details: 'Roll technical details',
  fairness_roll_info: `ROLL uses a provable fairness system, in which a SHA-256 hash is generated at the beginning of the game to generate a Public Seed hash, and at the end of the game, each user's number is generated based on the Public Seed and user joining order. If the user's number is the same as the prize number, they win.`,
  fairness_roll_public_seed:
    '<span class="text-white">Public Seed：</span>The safe random value generated at the beginning of the ROLL game. The SHA-256 hash value of Public Seed is immediately displayed to all players at the beginning of the game. Players can check whether the published Public Seed matches this SHA-256 hash value after the ROLL ends.',
  fairness_roll_prize_ID:
    '<span class="text-white">Prize ID：</span>The ID of the prize is displayed to all players at the beginning of the game.',
  fairness_roll_PRNumber:
    '<span class="text-white">PRNumber：</span>The total number of prizes.',
  fairness_roll_location:
    '<span class="text-white">Location：</span>Depending on the order in which players join the ROLL, starting from 1.',
  fairness_roll_PLNumber:
    '<span class="text-white">PLNumber：</span>The total number of players who have successfully participated in ROLL.',
  fairness_roll_win:
    'At the end of the ROLL, the system will generate a number for each user based on PublicSeed, location, PRNumber, or PLNumber (whichever is greater). When this number matches the prize number, the user will win.',
  fairness_roll_rule:
    'The same player in each ROLL can win at most once. If the number of participating players is less than the number of ROLL prizes, some prizes may not have winning users.',
  roll_record: 'Roll Record',
};
