import type { Locale } from '~/vue-i18n';

const languageCache: Record<string, any> = {};

async function fetchRemoteJson(url: string): Promise<any> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    return null;
  }
}

export async function loadRemoteLanguage(
  locale: Locale,
  fallbackToDefault = true,
): Promise<any> {
  const cacheKey = `lang_${locale}`;

  if (languageCache[cacheKey]) {
    return languageCache[cacheKey];
  }
  const { LANGUAGE_URL } = useRuntimeConfig().public;
  const url = `${LANGUAGE_URL}/${locale}.json`;
  const data = await fetchRemoteJson(url);

  if (data) {
    languageCache[cacheKey] = data;
    return data;
  } else if (fallbackToDefault && locale !== 'en') {
    return loadRemoteLanguage('en', false);
  }

  return {};
}

// 获取饰品多语言信息
export async function loadRemoteDecorationLanguage(
  locale: Locale,
): Promise<any> {
  const cacheKey = `decoration_${locale}`;
  if (languageCache[cacheKey]) {
    return languageCache[cacheKey];
  }
  const { LANGUAGE_URL } = useRuntimeConfig().public;
  const url = `${LANGUAGE_URL}/cs2/${locale}.json`;
  const data = await fetchRemoteJson(url);

  if (data) {
    languageCache[cacheKey] = data;
    return data;
  } else if (locale !== 'en') {
    return loadRemoteDecorationLanguage('en');
  }

  return {};
}

// 获取饰品全称多语言信息
export async function loadRemoteDecorationFullNameLanguage(
  locale: Locale,
): Promise<any> {
  const cacheKey = `decoration_full_name_${locale}`;
  if (languageCache[cacheKey]) {
    return languageCache[cacheKey];
  }
  const { LANGUAGE_URL } = useRuntimeConfig().public;
  const url = `${LANGUAGE_URL}/cs2_full_name/${locale}.json`;
  const data = await fetchRemoteJson(url);

  if (data) {
    languageCache[cacheKey] = data;
    return data;
  } else if (locale !== 'en') {
    return loadRemoteDecorationFullNameLanguage('en');
  }

  return {};
}

export function clearLanguageCache(locale?: Locale): void {
  if (locale) {
    delete languageCache[`lang_${locale}`];
    delete languageCache[`decoration_${locale}`];
    delete languageCache[`decoration_full_name_${locale}`];
  } else {
    Object.keys(languageCache).forEach((key) => delete languageCache[key]);
  }
}

export default {
  locale: 'en',
  fallbackLocale: 'en',
  legacy: false,
  globalInjection: true,
  warnHtmlMessage: false,
  loadRemoteLanguage,
};
