export enum AnimateStatus {
  UN_SATRT = 0,
  START = 1,
  REBOUND_SATRT = 2,
  FINISH = 3,
}

// 开箱饰品类型,适用:宝箱内容,幸运物品,开箱结果
export type CaseSkinItemType = V1CasesRecordSimple &
  V1MarketGoodsInfo &
  V1SimpleUserInfo &
  V1CasesContain & {
    case?: V1CasesItem;
    category_name?: string;
    steam_item_name?: string;
    update?: boolean;
  };
export type SpinnerInfoType = {
  spinnerList: CaseSkinItemType[];
  finished: boolean;
  converted: boolean;
  selectItem?: CaseSkinItemType;
  res?: V1CasesRecord;
  experiment?: boolean;
};
export type PlayOptConfig = {
  winItem?: CaseSkinItemType;
  skip?: boolean;
  fast?: boolean;
};
export type FairInfoType = {
  msg: string;
  seeds: {
    title: string;
    value: number | string;
  }[];
};
