import type { Component } from 'vue';

export type PaginatorType = {
  page_size: number;
  total: number;
  page: number;
};

export type HeaderItemType = {
  name: string;
  label: string;
  path: string;
  badge?: string;
  icon?: string;
  suffixIcon?: string;
  primary?: boolean;
  needLogin?: boolean;
  color?: string;
  textColor?: string;
  component?: Component;
  restrictIP?: boolean;
  tooltip?: string;
  iconClass?: string;
};
