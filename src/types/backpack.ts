type ExtractItem = ApiV1BackpackExtractListGet200ResponseDataItemsInner;
type RecordItem = V1GetBackpackRecordListReplyItem;
type SkinItem = V1GetBackpackListReplyItem;
type GoodsInfo = V1MarketGoodsInfo;

export type SkinItemWithGoods = SkinItem & {
  goods_info?: GoodsInfo;
  selected?: boolean;
};

export type ExtractItemWithGoods = ExtractItem & {
  goods_info?: GoodsInfo;
};

export type RecordItemWithGoods = RecordItem & {
  goods_info?: GoodsInfo;
};
