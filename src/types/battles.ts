// 对战宝箱选择的物品
export type V1CasesItemWithQuantity = V1CasesItem & {
  quantity: number;
};

export type Player =
  | (V1BattlePlayer &
      V1BattleRecord &
      V1SimpleUserInfo & {
        itemsData?: Array<V1BattleItem & V1BattleRecord>;
      })
  | undefined;

export type BattleItem = V1BattleInfo & {
  players: Player[];
};

export type BattleCase = V1CasesItem &
  V1BattleCase & {
    case_image: string;
    case_name: string;
    qty: number;
  };

export type BattleRecord = V1BattleRecord &
  V1MarketGoodsInfo & {
    category_name: string;
    steam_item_name: string;
  };

// 战斗玩家状态
export enum PlayerStateType {
  WAITING = 'WAITING',
  READY = 'READY',
  PLAYING = 'PLAYING',
  FINISHED = 'FINISHED',
}

export enum GameModesType {
  NORMAL = 1,
  UNO_REVERSE = 2,
}

export enum BattleStatusType {
  INIT = 0,
  WAITING = 1,
  RUNNING = 2,
  FINISHED = 3,
  CANCELLED = 4,
  WAITING_FOR_EOS = 5,
  TIEBREAKER = 6,
  ENDED = 7,
}

export enum BattlePlayerModesType {
  '1v1' = 2,
  '1v1v1' = 3,
  '1v1v1v1' = 4,
}

export enum BattleResultType {
  WON = 'won',
  LOST = 'lost',
}

// export enum BOT_STATUS {
//   REAL_USER = -1,
//   GOOD_BOT = 0,
//   EVIL_BOT = 1,
// }

export enum BOT_STATUS {
  GOOD_BOT = 1,
  REAL_USER = 2,
}

export enum BattleModesType {
  REGULAR = 1,
  SHARED = 2,
}

export interface PlayerType {
  id: number;
  steam_name: string;
  avatar: string;
  position: number;
}

export interface BattleCaseType {
  case_image: string;
  case_name: string;
  qty: number;
}

export interface BattleType {
  status: BattleStatusType;
  battle_mode: BattleModesType;
  game_mode: GameModesType;
  is_private: number;
  player_number: number;
  players: Player[];
  cases: BattleCase[];
  active_round: number;
  battle_cost: number;
  win_amount?: number;
  winners?: number[];
  creator_uid: string;
  ended_at?: number;
  seed: string;
  seed_committed_at: number;
  private_seed: string;
  private_seed_committed_at: number;
  private_seed_hash: string;
  seed_index: string;
  seed_index_committed_at: number;
  tiebreaker: {
    result: number;
    participantPositions: number[];
  };
  battleCaseList: any[];
  battlePlayers: any[];
  arena_id: string;
  status_desc: string;
  total_round: number;
  seed_source: string;
  is_tiebreaker: boolean;
  started_at: string;
}

export enum TagColor {
  YELLOW = 'yellow',
  RED = 'red',
  GREEN = 'green',
  GREY = 'grey',
}

export enum TagVariant {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
}

export enum TagSize {
  MEDIUM = 'medium',
}

export enum FairBattleResult {
  UNKNOWN = 0,
  WIN = 1,
  LOSE = 2,
  CANCEL = 3,
  EXIT = 4,
}
