interface FormValidation {
  required?: boolean;
  validator?: (value: any) => boolean | string;
}

export interface FormField {
  key: string;
  getShow?: () => boolean;
  'label-field'?: string;
  'children-field'?: string;
  'key-field'?: string;
  type?: string;
  label?: string;
  component?: string;
  value?: any;
  options?: any;
  listeners?: Record<string, boolean>;
  placeholder?: string;
  validation?: FormValidation;
  consistentMenuWidth?: boolean;
}

export interface FilterFormSchema {
  [key: string]: FormField;
}
