export enum Sound {
  /** case open */
  CASE_CHOOSE = '/audio/caseselect.mp3',
  UNLOCK = '../../audio/unlock.mp3',
  UNLOCK_SHORT = '../../audio/unlock_short.mp3',
  CASE_OPEN = '../../audio/open.mp3',
  CASE_OPEN_ROLL = '../../audio/roll.mp3',
  INSPECT = '../../audio/inspect.mp3',
  WIN0 = '../../audio/win0.mp3', // 灰
  FAST_WIN0 = '../../audio/fast_win0.mp3', // 灰
  WIN1 = '../../audio/win1.mp3', // 浅蓝
  FAST_WIN1 = '../../audio/fast_win1.mp3', // 浅蓝
  WIN2 = '../../audio/win2.mp3', // 蓝
  FAST_WIN2 = '../../audio/fast_win2.mp3', // 蓝
  WIN3 = '../../audio/win3.mp3', // 紫
  FAST_WIN3 = '../../audio/fast_win3.mp3', // 紫
  WIN4 = '../../audio/win4.mp3', // 粉
  FAST_WIN4 = '../../audio/fast_win4.mp3', // 粉
  WIN5 = '../../audio/win5.mp3', // 红
  FAST_WIN5 = '../../audio/fast_win5.mp3', // 红
  WIN6 = '../../audio/win6.mp3', // 黄
  FAST_WIN6 = '../../audio/fast_win6.mp3', // 黄
  COUNT_DOWN_TICK = '../../audio/count_down_tick.mp3',
  COUNT_DOWN_END = '../../audio/count_down_end.mp3',
  /** btn mouse */
  HOVER_MOUSE = '../../audio/hover-mouse.mp3',
  CLICK_BTN = '../../audio/click-button.mp3',
}
