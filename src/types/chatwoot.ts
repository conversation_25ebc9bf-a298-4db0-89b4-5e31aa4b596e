type Locale =
  | 'ar'
  | 'ca'
  | 'cs'
  | 'da'
  | 'de'
  | 'el'
  | 'en'
  | 'es'
  | 'fa'
  | 'fi'
  | 'fr'
  | 'hi'
  | 'hu'
  | 'id'
  | 'it'
  | 'ja'
  | 'ko'
  | 'ml'
  | 'nl'
  | 'no'
  | 'pl'
  | 'pt_BR'
  | 'pt'
  | 'ro'
  | 'ru'
  | 'sk'
  | 'sv'
  | 'ta'
  | 'th'
  | 'tr'
  | 'uk'
  | 'vi'
  | 'zh_CN'
  | 'zh_TW';

export type ChatwootVisibility = 'hide' | 'show';
export type ChatwootToggleState = 'open' | 'close';
interface ChatwootInit {
  websiteToken: string;
  /**
   * Base url
   * @default 'https://app.chatwoot.com'
   * @type string
   *
   */
  baseUrl?: string;
}
export interface ChatwootSettings {
  hideMessageBubble?: boolean;
  position?: 'left' | 'right';
  /**
   * Chat Widget Language
   * @default 'en'
   * @type Locale
   *
   */
  locale?: 'en' | Locale;
  type?: 'standard' | 'expanded_bubble';
  /**
   * Chat with us title
   * @default 'Chat with us'
   * @type string
   *
   */
  launcherTitle?: string;
  /**
   * Theme
   * @default 'auto'
   * @type 'light' | 'auto'
   *
   */
  darkMode?: 'light' | 'auto';
  showPopoutButton?: boolean;
}
interface ChatwootSdk {
  run: (init: ChatwootInit) => void;
}

export interface ChatwootSetUserProps {
  name?: string;
  avatar_url?: string;
  email?: string;
  identifier_hash?: string;
  phone_number?: string;
  description?: string;
  country_code?: string;
  city?: string;
  company_name?: string;
  social_profiles?: {
    twitter?: string;
    linkedin?: string;
    facebook?: string;
    github?: string;
  };
}
export interface Chatwoot {
  isModalVisible: boolean;
  toggle: (state?: ChatwootToggleState) => void;
  setUser: (key: string, args: ChatwootSetUserProps) => void;
  setCustomAttributes: (attributes: { [key: string]: string }) => void;
  setConversationCustomAttributes: (attributes: {
    [key: string]: string;
  }) => void;
  deleteCustomAttribute: (key: string) => void;
  setLocale: (local: string) => void;
  setLabel: (label: string) => void;
  removeLabel: (label: string) => void;
  reset: () => void;
  toggleBubbleVisibility: (visibility: ChatwootVisibility) => void;
  popoutChatWindow: () => void;
}
declare global {
  interface Window {
    chatwootSettings: ChatwootSettings;
    chatwootSDK: ChatwootSdk;
    $chatwoot: Chatwoot;
  }
}
