#!/usr/bin/env bash
# 更新接口模型

set -e
dir=$(pwd)

# 删除旧的文件
old_dir=$dir/src/api/type/
if [ -d "$old_dir" ]; then
  rm -rf $old_dir
  echo "Folder '$old_dir' deleted."
fi
# openapi 生成文件目录
target=$dir/src/api/openapi

json_file=$dir/openapi.json

curl "http://127.0.0.1:4523/export/openapi?projectId=5306386&version=3.0" > "$json_file"


if command -v jq >/dev/null 2>&1; then
  # 创建临时文件
  temp_file=$(mktemp)
  
  # 修改 x-md-device-id 参数的 required
  jq 'walk(
    if type == "object" and has("parameters") then
      .parameters |= map(
        if .name == "x-md-device-id" then
          .required = []
        else
          .
        end
      )
    else
      .
    end
  )' "$json_file" > "$temp_file"
  
  # 移回原文件
  mv "$temp_file" "$json_file"
else
  echo "Warning: jq is not installed. Skipping JSON processing."
fi

npx openapi-generator-cli generate \
  -g typescript-fetch \
  --remove-operation-id-prefix \
  --skip-validate-spec \
  --additional-properties=modelPropertyNaming=snake_case,paramNaming=snake_case,ensureUniqueParams=true \
  -i $dir/openapi.json \
  -o $target \
  -c $dir/openapi.yaml
