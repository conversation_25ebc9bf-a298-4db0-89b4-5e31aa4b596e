{"name": "nuxt-app", "private": true, "type": "module", "version": "1.0.0", "scripts": {"build": "run-s build:prod", "build:test": "nuxt build --dotenv .env.test", "build:prod": "nuxt build --dotenv .env.production", "dev": "NUXT_HOST=0.0.0.0 npx nuxi dev --dotenv .env.development", "dev:prod": "NUXT_HOST=0.0.0.0  npx nuxi dev --dotenv .env.production", "generate": "run-s generate:nuxt", "generate:nuxt": "nuxt generate", "preview": "nuxt preview", "lint:js": "eslint --ext \".js,.ts,.vue\" .", "lint:style": "stylelint \"**/*.{css,scss,sass,html,vue}\" --ignore-path .stylelintignore ", "lint:prettier": "prettier --check .", "lint": "pnpm lint:js && pnpm lint:style && pnpm lint:prettier", "lintfix": "prettier --write --list-different . && pnpm lint:js --fix && pnpm lint:style --fix", "prepare": "npx husky install", "gen:api": "bash ./src/scripts/genApiModel.sh"}, "devDependencies": {"@babel/eslint-parser": "^7.23.3", "@commitlint/config-conventional": "^18.4.3", "@css-render/vue3-ssr": "^0.15.12", "@nuxt/devtools": "latest", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/i18n": "9.1.0", "@nuxtjs/robots": "^4.0.2", "@nuxtjs/sitemap": "6.0.0-beta.1", "@nuxtjs/stylelint-module": "^5.1.0", "@nuxtjs/tailwindcss": "^6.12.2", "@openapitools/openapi-generator-cli": "^2.7.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@types/ua-parser-js": "^0.7.39", "ali-oss": "^6.20.0", "commitlint": "^18.4.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^9.19.2", "esno": "^4.0.0", "flex-gap-polyfill": "^4.2.0", "fs-extra": "^11.2.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "naive-ui": "^2.38.1", "npm-run-all": "^4.1.5", "nuxt": "^3.12.4", "nuxt-svgo": "^4.0.6", "postcss-preset-env": "^9.3.0", "prettier": "^3.1.1", "sass": "^1.69.5", "stylelint": "^16.0.2", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^35.0.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite-plugin-top-level-await": "^1.4.1", "vue": "^3.4.20", "vue-i18n": "^10.0.4", "vue-router": "^4.2.5"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.2.2", "@icon-park/vue-next": "^1.4.2", "@nuxt/image": "^1.1.0", "@pinia/nuxt": "^0.5.1", "@types/howler": "^2.2.12", "@vueuse/core": "^10.7.0", "@vueuse/integrations": "^10.7.0", "@vueuse/nuxt": "^10.7.0", "any-touch": "^2.2.0", "bignumber.js": "^9.1.2", "dayjs": "^1.11.10", "echarts": "^5.6.0", "howler": "^2.2.4", "i18n-iso-countries": "^7.14.0", "libphonenumber-js": "^1.12.9", "lodash-es": "^4.17.21", "nuxt-app": "link:", "socket.io-client": "^4.8.1", "ts-md5": "^1.3.1", "ua-parser-js": "^1.0.37", "vanilla-cookieconsent": "3.0.1", "vue-dragscroll": "4.0.6"}, "lint-staged": {"*.{js,ts,vue}": "eslint --cache --fix", "*.{css,scss,sass,html,vue}": "stylelint --fix --allow-empty-input", "*.**": "prettier --write --ignore-unknown"}, "browserslist": ["> 1%", "last 2 versions", "Safari >= 9"]}